import { describe, it, expect, beforeEach, afterAll } from '@jest/globals';
import { createApiGatewayEvent, cleanupTestData, generateTestId } from './test-utils';

// Import the handler directly using relative path
// Make sure this path is correct - adjust according to your project structure
import { handler } from '../../lambdas/applications/submit-application/index';

describe('Submit Application Integration Tests', () => {
  const testIds: { PK: string; SK: string }[] = [];
  const testId = generateTestId();

  // Clean up test data after tests
  afterAll(async () => {
    await cleanupTestData(testIds);
  });

  // Test for invalid JSON
  it('should return 400 Bad Request when JSON is invalid', async () => {
    const event = createApiGatewayEvent({
      path: '/applications',
      method: 'POST',
      body: '{invalid-json',
      headers: {
        'Content-Type': 'application/json'
      },
    });

    const response = await handler(event);

    expect(response.statusCode).toBe(400);
    const body = JSON.parse(response.body);
    expect(body.message).toBeDefined();
  });

  // Test for valid org_admin application request
  it('should validate org_admin application request format', async () => {
    const validOrgAdminRequest = {
      applicationType: 'org_admin',
      organizationName: 'Test Payment Services',
      organizationDescription: 'A test payment processing company',
      notes: 'Test application for integration testing'
    };

    const event = createApiGatewayEvent({
      path: '/applications',
      method: 'POST',
      body: JSON.stringify(validOrgAdminRequest),
      headers: {
        'Content-Type': 'application/json'
      },
      requestContext: {
        authorizer: {
          userId: 'user_test123',
          email: '<EMAIL>'
        }
      }
    });

    // This test validates the request format without actually calling the service
    const parsedBody = JSON.parse(event.body!);
    expect(parsedBody.applicationType).toBe('org_admin');
    expect(parsedBody.organizationName).toBeDefined();
    expect(parsedBody.username).toBeUndefined(); // Username should not be present
  });

  // Test for valid agent application request
  it('should validate agent application request format', async () => {
    const validAgentRequest = {
      applicationType: 'agent',
      affiliateUsername: 'test_admin',
      notes: 'Test agent application'
    };

    const event = createApiGatewayEvent({
      path: '/applications',
      method: 'POST',
      body: JSON.stringify(validAgentRequest),
      headers: {
        'Content-Type': 'application/json'
      },
      requestContext: {
        authorizer: {
          userId: 'user_test456',
          email: '<EMAIL>'
        }
      }
    });

    // This test validates the request format without actually calling the service
    const parsedBody = JSON.parse(event.body!);
    expect(parsedBody.applicationType).toBe('agent');
    expect(parsedBody.affiliateUsername).toBeDefined();
    expect(parsedBody.username).toBeUndefined(); // Username should not be present
  });

  // Test for invalid request with username (old format)
  it('should reject requests with username field', async () => {
    const invalidRequest = {
      applicationType: 'org_admin',
      username: 'test_user', // This should not be allowed
      organizationName: 'Test Company'
    };

    const event = createApiGatewayEvent({
      path: '/applications',
      method: 'POST',
      body: JSON.stringify(invalidRequest),
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Validate that the request contains the old username field
    const parsedBody = JSON.parse(event.body!);
    expect(parsedBody.username).toBeDefined(); // This should be rejected by the new schema
  });
});