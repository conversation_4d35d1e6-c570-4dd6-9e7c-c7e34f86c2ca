{"httpMethod": "POST", "path": "/kyc/complete/prove_abc123", "pathParameters": {"correlationId": "prove_abc123"}, "headers": {"Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...", "X-User-Id": "user_123456", "X-Clerk-User-Id": "user_2abc123def456", "X-Tenant-Id": "default"}, "body": "{\"individual\":{\"firstName\":\"<PERSON>\",\"lastName\":\"<PERSON><PERSON>\",\"dateOfBirth\":\"1990-01-01\",\"phoneNumber\":\"+**********\",\"emailAddresses\":[\"<EMAIL>\"],\"addresses\":[{\"street\":\"123 Main St\",\"city\":\"New York\",\"region\":\"NY\",\"postalCode\":\"10001\",\"country\":\"US\"}]}}", "requestContext": {"requestId": "test-request-id", "stage": "dev", "resourcePath": "/kyc/complete/{correlationId}", "httpMethod": "POST", "requestTime": "2024-01-01T12:00:00Z", "accountId": "**********12", "apiId": "test-api-id", "authorizer": {"principalId": "user_123456", "claims": {"sub": "user_2abc123def456", "email": "<EMAIL>"}}}, "isBase64Encoded": false}