{"httpMethod": "GET", "path": "/kyc/status/kyc_123456", "pathParameters": {"verificationId": "kyc_123456"}, "headers": {"Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...", "X-User-Id": "user_123456", "X-Clerk-User-Id": "user_2abc123def456", "X-Tenant-Id": "default"}, "body": null, "requestContext": {"requestId": "test-request-id", "stage": "dev", "resourcePath": "/kyc/status/{verificationId}", "httpMethod": "GET", "requestTime": "2024-01-01T12:00:00Z", "accountId": "************", "apiId": "test-api-id", "authorizer": {"principalId": "user_123456", "claims": {"sub": "user_2abc123def456", "email": "<EMAIL>"}}}, "isBase64Encoded": false}