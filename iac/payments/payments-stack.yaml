AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: >
  Payments Stack for handling payment operations including CashApp payments

Parameters:
  Environment:
    Type: String
    Default: dev
    Description: The deployment stage (e.g., dev, prod, staging)
    AllowedValues:
      - dev
      - prod
      - staging

  LayerCommons:
    Type: String
    Description: ARN of the Commons Layer

  TableName:
    Type: String
    Description: DynamoDB table name for storing payment data

  ClerkAuthorizerArn:
    Type: String
    Description: ARN of the Clerk JWT Authorizer for API Gateway

  PocketKnightsBaseUrl:
    Type: String
    Description: Base URL for PocketKnights API
    Default: https://api.pocketknights.com

  PocketKnightsApiSecretName:
    Type: String
    Description: Name of the PocketKnights API secret in Secrets Manager

  CashAppMerchantNo:
    Type: String
    Description: CashApp Merchant Number
    NoEcho: true
    Default: default

  CashAppScriptUrl:
    Type: String
    Description: CashApp SDK Script URL
    Default: https://sandbox.kit.cash.app/v1/pay.js

Globals:
  Function:
    Runtime: nodejs18.x
    Architectures:
      - arm64
    MemorySize: 1024
    Timeout: 30
    Tracing: Active
    Environment:
      Variables:
        TABLE_NAME: !Ref TableName
        POCKETKNIGHTS_BASE_URL: !Ref PocketKnightsBaseUrl
        CASHAPP_MERCHANT_NO: !Ref CashAppMerchantNo
        CASHAPP_SCRIPT_URL: !Ref CashAppScriptUrl
        LOG_LEVEL: INFO
    Layers:
      - !Ref LayerCommons

Resources:
  # ================================================
  # CASHAPP PAYMENT FUNCTIONS
  # ================================================

  CreateCashAppPaymentFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "${Environment}-create-cashapp-payment"
      CodeUri: ../../lambdas/payments/create-cashapp-payment/
      Handler: index.handler
      Description: Create CashApp payment with receiver lookup and provider integration
      Environment:
        Variables:
          LOG_LEVEL: INFO
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref TableName
        - Version: "2012-10-17"
          Statement:
            - Effect: Allow
              Action:
                - logs:CreateLogGroup
                - logs:CreateLogStream
                - logs:PutLogEvents
              Resource: "*"
            - Effect: Allow
              Action:
                - xray:PutTraceSegments
                - xray:PutTelemetryRecords
              Resource: "*"

  CheckCashAppPaymentStatusFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "${Environment}-check-cashapp-payment-status"
      CodeUri: ../../lambdas/payments/check-cashapp-payment-status/
      Handler: index.handler
      Description: Check CashApp payment status and update payment entity
      Environment:
        Variables:
          LOG_LEVEL: INFO
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref TableName
        - Version: "2012-10-17"
          Statement:
            - Effect: Allow
              Action:
                - logs:CreateLogGroup
                - logs:CreateLogStream
                - logs:PutLogEvents
              Resource: "*"
            - Effect: Allow
              Action:
                - xray:PutTraceSegments
                - xray:PutTelemetryRecords
              Resource: "*"

  # ================================================
  # CLOUDWATCH ALARMS
  # ================================================

  CreateCashAppPaymentErrorAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub "${Environment}-create-cashapp-payment-errors"
      AlarmDescription: "Alarm for errors in CashApp payment creation"
      MetricName: Errors
      Namespace: AWS/Lambda
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 5
      ComparisonOperator: GreaterThanOrEqualToThreshold
      Dimensions:
        - Name: FunctionName
          Value: !Ref CreateCashAppPaymentFunction
      TreatMissingData: notBreaching

  CheckCashAppPaymentStatusErrorAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub "${Environment}-check-cashapp-payment-status-errors"
      AlarmDescription: "Alarm for errors in CashApp payment status checking"
      MetricName: Errors
      Namespace: AWS/Lambda
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 5
      ComparisonOperator: GreaterThanOrEqualToThreshold
      Dimensions:
        - Name: FunctionName
          Value: !Ref CheckCashAppPaymentStatusFunction
      TreatMissingData: notBreaching

  # ================================================
  # LOG GROUPS
  # ================================================

  CreateCashAppPaymentLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub "/aws/lambda/${Environment}-create-cashapp-payment"
      RetentionInDays: 14

  CheckCashAppPaymentStatusLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub "/aws/lambda/${Environment}-check-cashapp-payment-status"
      RetentionInDays: 14

Outputs:
  CreateCashAppPaymentFunction:
    Description: "Create CashApp Payment Lambda Function ARN"
    Value: !GetAtt CreateCashAppPaymentFunction.Arn
    Export:
      Name: !Sub "${Environment}-create-cashapp-payment-function-arn"

  CheckCashAppPaymentStatusFunction:
    Description: "Check CashApp Payment Status Lambda Function ARN"
    Value: !GetAtt CheckCashAppPaymentStatusFunction.Arn
    Export:
      Name: !Sub "${Environment}-check-cashapp-payment-status-function-arn" 