openapi: "3.0.1"
info:
  title: "CLKK Payment System API"
  version: "1.0.0"
  description: |
    # CLKK Payment System API

    The CLKK Payment System REST API provides comprehensive access to the CLKK platform's payment processing,
    organization management, and user application functionality.

    This API enables you to process CashApp payments, manage organization applications, handle user registrations,
    and integrate with Clerk authentication services.

    ## Authentication

    All API requests require authentication using a JWT token from <PERSON>. The token should be included in the Authorization header.

    ## Rate Limiting

    API requests are subject to rate limiting to ensure system stability. Please implement appropriate retry logic with exponential backoff.

    ## Versioning

    The API follows semantic versioning. Breaking changes will be introduced in major version updates.
  contact:
    name: "CLKK Support Team"
    email: "<EMAIL>"
    url: "https://clkk.com/support"
  license:
    name: "Proprietary"
    url: "https://clkk.com/terms"
  termsOfService: "https://clkk.com/terms"
tags:
  - name: Payments
    description: "Endpoints for processing CashApp payments"
  - name: Applications
    description: "Endpoints for managing organization and agent applications"
  - name: Organizations
    description: "Endpoints for managing organizations in the CLKK system"
  - name: Users
    description: "Endpoints for user management and validation"
  - name: KYC
    description: "Endpoints for Know Your Customer (KYC) identity verification"
  - name: API Keys
    description: "Endpoints for managing partner API keys"
  - name: Webhooks
    description: "Endpoints for handling external service webhooks"
externalDocs:
  description: "CLKK Documentation"
  url: "https://docs.clkk.com"
security:
  - bearerAuth: []
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT token authentication from Clerk. The token should be included in the Authorization header as "Bearer {token}".

        Tokens are issued through the Clerk authentication process and have a limited lifetime.
        When a token expires, the client should request a new one.
  parameters:
    PageSize:
      name: pageSize
      in: query
      description: |
        Number of items to return per page.

        The default is 20 items per page, and the maximum allowed is 100 items per page.
        Values larger than the maximum will be capped at the maximum.
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 20
      required: false
    PageToken:
      name: pageToken
      in: query
      description: |
        Token for pagination. This token is returned in the `nextPageToken` field of the response.

        To get the next page of results, include this token in your next request.
        If this parameter is omitted, the first page of results is returned.
      schema:
        type: string
      required: false
  schemas:
    # ================================================
    # PAYMENT SCHEMAS
    # ================================================
    CreateCashAppPaymentRequest:
      type: object
      title: "CreateCashAppPaymentRequest"
      required:
        - amount
        - currency
        - redirectUrl
      properties:
        amount:
          type: number
          description: Payment amount in the specified currency
          minimum: 0.01
          example: 25.99
        currency:
          type: string
          description: Currency code (ISO 4217)
          enum: [USD]
          default: USD
          example: "USD"
        redirectUrl:
          type: string
          format: uri
          description: URL to redirect user after payment completion
          example: "https://app.clkk.com/payment/success"
        description:
          type: string
          description: Description of the payment
          maxLength: 255
          example: "Payment for premium subscription"
        metadata:
          type: object
          description: Additional metadata for the payment
          additionalProperties: true
          example:
            orderId: "order_123456"
            customerId: "cust_789"

    CreateCashAppPaymentResponse:
      type: object
      title: "CreateCashAppPaymentResponse"
      properties:
        paymentId:
          type: string
          description: Unique identifier for the payment
          example: "pay_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        paymentUrl:
          type: string
          format: uri
          description: URL to redirect user for payment
          example: "https://cash.app/pay/abc123def456"
        amount:
          type: number
          description: Payment amount
          example: 25.99
        currency:
          type: string
          description: Currency code
          example: "USD"
        status:
          type: string
          description: Current payment status
          enum: [PENDING, COMPLETED, FAILED, CANCELLED]
          example: "PENDING"
        expiresAt:
          type: string
          format: date-time
          description: When the payment link expires
          example: "2024-01-15T10:30:00Z"
        createdAt:
          type: string
          format: date-time
          description: When the payment was created
          example: "2024-01-15T10:00:00Z"

    CheckCashAppPaymentStatusRequest:
      type: object
      title: "CheckCashAppPaymentStatusRequest"
      required:
        - paymentId
      properties:
        paymentId:
          type: string
          description: Unique identifier for the payment to check
          example: "pay_01H9XD5JNZQ3BYKP6ZCVGWTVMR"

    CheckCashAppPaymentStatusResponse:
      type: object
      title: "CheckCashAppPaymentStatusResponse"
      properties:
        paymentId:
          type: string
          description: Unique identifier for the payment
          example: "pay_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        status:
          type: string
          description: Current payment status
          enum: [PENDING, COMPLETED, FAILED, CANCELLED]
          example: "COMPLETED"
        amount:
          type: number
          description: Payment amount
          example: 25.99
        currency:
          type: string
          description: Currency code
          example: "USD"
        completedAt:
          type: string
          format: date-time
          description: When the payment was completed (if applicable)
          example: "2024-01-15T10:15:00Z"
        failureReason:
          type: string
          description: Reason for failure (if applicable)
          example: "Insufficient funds"

    # ================================================
    # APPLICATION SCHEMAS
    # ================================================
    SubmitApplicationRequest:
      type: object
      title: "SubmitApplicationRequest"
      required:
        - applicationType
      properties:
        applicationType:
          type: string
          description: Type of application being submitted
          enum: [org_admin, agent]
          example: "org_admin"
        organizationName:
          type: string
          description: Name of the organization (required for org_admin applications)
          example: "Acme Payment Services"
        organizationDescription:
          type: string
          description: Description of the organization (optional for org_admin applications)
          example: "A leading payment processing company"
        affiliateUsername:
          type: string
          description: Username of the affiliate/organization admin (required for agent applications)
          example: "acme_admin"
        businessDetails:
          type: object
          description: Business details (for org_admin applications)
          properties:
            businessType:
              type: string
              description: Type of business
              example: "Payment Processing"
            registrationNumber:
              type: string
              description: Business registration number
              example: "REG123456789"
            taxId:
              type: string
              description: Tax identification number
              example: "TAX987654321"
            website:
              type: string
              format: uri
              description: Business website
              example: "https://acmepayments.com"
            description:
              type: string
              description: Business description
              example: "We provide payment processing services for small businesses"
        contactInfo:
          type: object
          description: Contact information
          properties:
            email:
              type: string
              format: email
              description: Contact email
              example: "<EMAIL>"
            phone:
              type: string
              description: Contact phone number
              example: "******-123-4567"
            address:
              type: string
              description: Business address
              example: "123 Main St, Anytown, ST 12345"

    SubmitApplicationResponse:
      type: object
      title: "SubmitApplicationResponse"
      properties:
        applicationId:
          type: string
          description: Unique identifier for the submitted application
          example: "app_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        applicationType:
          type: string
          description: Type of application
          enum: [org_admin, agent]
          example: "org_admin"
        userId:
          type: string
          description: ID of the authenticated user who submitted the application
          example: "user_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        userEmail:
          type: string
          description: Email of the authenticated user who submitted the application
          example: "<EMAIL>"
        status:
          type: string
          description: Current application status
          enum: [PENDING_APPROVAL, APPROVED, REJECTED]
          example: "PENDING_APPROVAL"
        organizationName:
          type: string
          description: Name of the organization (for org_admin applications)
          example: "Acme Payment Services"
        affiliateUsername:
          type: string
          description: Username of the affiliate (for agent applications)
          example: "acme_admin"
        createdAt:
          type: string
          format: date-time
          description: When the application was submitted
          example: "2024-01-15T10:00:00Z"

    ApproveApplicationRequest:
      type: object
      title: "ApproveApplicationRequest"
      required:
        - applicationId
        - approved
      properties:
        applicationId:
          type: string
          description: ID of the application to approve or reject
          example: "app_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        approved:
          type: boolean
          description: Set to true to approve the application, false to reject
          example: true
        adminNotes:
          type: string
          description: Administrative notes regarding the approval or rejection
          example: "Application approved after verification"
        rejectionReason:
          type: string
          description: Reason for rejection, required if 'approved' is false
          example: "Incomplete documentation"

    ApproveApplicationResponse:
      type: object
      title: "ApproveApplicationResponse"
      properties:
        applicationId:
          type: string
          description: ID of the processed application
          example: "app_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        status:
          type: string
          description: New status of the application
          enum: [APPROVED, REJECTED]
          example: "APPROVED"
        organizationId:
          type: string
          description: ID of the created organization (for approved org_admin applications)
          example: "org_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        clerkOrganizationId:
          type: string
          description: Clerk organization ID (for approved applications)
          example: "org_2QUu0EFyJPzV49iVsEn1gXDGypV"
        processedAt:
          type: string
          format: date-time
          description: When the application was processed
          example: "2024-01-15T11:00:00Z"

    # ================================================
    # ORGANIZATION SCHEMAS
    # ================================================
    CreateOrganizationRequest:
      type: object
      title: "CreateOrganizationRequest"
      required:
        - name
        - type
      properties:
        name:
          type: string
          description: Name of the organization
          example: "Acme Payment Services"
        type:
          type: string
          description: Type of organization
          enum: [PAYMENT_PROCESSOR, MERCHANT, AFFILIATE]
          example: "PAYMENT_PROCESSOR"
        description:
          type: string
          description: Description of the organization
          example: "Professional payment processing services"
        contactInfo:
          type: object
          description: Contact information for the organization
          properties:
            email:
              type: string
              format: email
              description: Primary contact email
              example: "<EMAIL>"
            phone:
              type: string
              description: Primary contact phone
              example: "******-123-4567"
            address:
              type: string
              description: Organization address
              example: "123 Main St, Anytown, ST 12345"
        website:
          type: string
          format: uri
          description: Organization website
          example: "https://acmepayments.com"
        metadata:
          type: object
          description: Additional metadata
          additionalProperties: true

    CreateOrganizationResponse:
      type: object
      title: "CreateOrganizationResponse"
      properties:
        id:
          type: string
          description: Unique identifier for the organization
          example: "org_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        clerkId:
          type: string
          description: Clerk organization ID
          example: "org_2QUu0EFyJPzV49iVsEn1gXDGypV"
        name:
          type: string
          description: Organization name
          example: "Acme Payment Services"
        type:
          type: string
          description: Organization type
          example: "PAYMENT_PROCESSOR"
        status:
          type: string
          description: Organization status
          enum: [ACTIVE, INACTIVE, PENDING, SUSPENDED]
          example: "ACTIVE"
        createdAt:
          type: string
          format: date-time
          description: When the organization was created
          example: "2024-01-15T10:00:00Z"



    # ================================================
    # USER SCHEMAS
    # ================================================
    ValidateUsernameRequest:
      type: object
      title: "ValidateUsernameRequest"
      required:
        - username
      properties:
        username:
          type: string
          description: Username to validate
          pattern: '^[a-zA-Z0-9_]{3,30}$'
          example: "john_doe_123"

    ValidateUsernameResponse:
      type: object
      title: "ValidateUsernameResponse"
      properties:
        username:
          type: string
          description: The username that was validated
          example: "john_doe_123"
        isValid:
          type: boolean
          description: Whether the username is valid and available
          example: true
        isAvailable:
          type: boolean
          description: Whether the username is available for use
          example: true
        validationErrors:
          type: array
          description: List of validation errors (if any)
          items:
            type: string
          example: []
        suggestions:
          type: array
          description: Alternative username suggestions (if username is taken)
          items:
            type: string
          example: ["john_doe_124", "john_doe_2024", "johndoe123"]

    # ================================================
    # PARTNER API KEY SCHEMAS
    # ================================================
    GenerateAPIKeyRequest:
      type: object
      title: "GenerateAPIKeyRequest"
      required:
        - name
        - type
      properties:
        name:
          type: string
          description: Name/description for the API key
          minLength: 3
          maxLength: 100
          example: "Production API Key"
        type:
          type: string
          description: Type of API key
          enum: [TEST, LIVE]
          example: "LIVE"
        permissions:
          type: array
          description: List of permissions for this API key
          items:
            type: string
          example: ["PAYMENTS_CREATE", "PAYMENTS_READ"]
        rateLimit:
          type: object
          description: Rate limit configuration for this API key
          properties:
            requestsPerMinute:
              type: integer
              description: Maximum requests per minute
              minimum: 1
              maximum: 10000
              example: 100
            requestsPerHour:
              type: integer
              description: Maximum requests per hour
              minimum: 1
              maximum: 100000
              example: 5000
        expiresAt:
          type: string
          format: date-time
          description: Optional expiration date for the API key
          example: "2024-12-31T23:59:59Z"
        metadata:
          type: object
          description: Additional metadata for the API key
          additionalProperties: true

    GenerateAPIKeyResponse:
      type: object
      title: "GenerateAPIKeyResponse"
      properties:
        keyId:
          type: string
          description: Unique identifier for the API key
          example: "key_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        apiKey:
          type: string
          description: The actual API key (only shown once)
          example: "ckpl_test_01H9XD5JNZQ3BYKP6ZCVGWTVMR_a7b8c9d0e1f2"
        name:
          type: string
          description: Name of the API key
          example: "Production API Key"
        type:
          type: string
          description: Type of API key
          enum: [TEST, LIVE]
          example: "LIVE"
        createdAt:
          type: string
          format: date-time
          description: When the API key was created
          example: "2024-01-15T10:00:00Z"
        expiresAt:
          type: string
          format: date-time
          description: When the API key expires (if applicable)
          example: "2024-12-31T23:59:59Z"

    ListAPIKeysResponse:
      type: object
      title: "ListAPIKeysResponse"
      properties:
        keys:
          type: array
          description: List of API keys
          items:
            type: object
            properties:
              keyId:
                type: string
                description: Unique identifier for the API key
                example: "key_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
              name:
                type: string
                description: Name of the API key
                example: "Production API Key"
              type:
                type: string
                description: Type of API key
                enum: [TEST, LIVE]
                example: "LIVE"
              status:
                type: string
                description: Current status of the API key
                enum: [ACTIVE, REVOKED, EXPIRED]
                example: "ACTIVE"
              lastUsedAt:
                type: string
                format: date-time
                description: When the API key was last used
                example: "2024-01-15T09:30:00Z"
              createdAt:
                type: string
                format: date-time
                description: When the API key was created
                example: "2024-01-01T10:00:00Z"
              expiresAt:
                type: string
                format: date-time
                description: When the API key expires (if applicable)
                example: "2024-12-31T23:59:59Z"
        totalCount:
          type: integer
          description: Total number of API keys
          example: 5
        nextPageToken:
          type: string
          description: Token for pagination
          example: "eyJsYXN0S2V5SWQiOiJrZXlfMDFIOVhE..."

    RevokeAPIKeyResponse:
      type: object
      title: "RevokeAPIKeyResponse"
      properties:
        keyId:
          type: string
          description: ID of the revoked API key
          example: "key_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        status:
          type: string
          description: New status of the API key
          enum: [REVOKED]
          example: "REVOKED"
        revokedAt:
          type: string
          format: date-time
          description: When the API key was revoked
          example: "2024-01-15T10:00:00Z"

    GetAPIKeyDetailsResponse:
      type: object
      title: "GetAPIKeyDetailsResponse"
      properties:
        keyId:
          type: string
          description: Unique identifier for the API key
          example: "key_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        name:
          type: string
          description: Name of the API key
          example: "Production API Key"
        type:
          type: string
          description: Type of API key
          enum: [TEST, LIVE]
          example: "LIVE"
        status:
          type: string
          description: Current status of the API key
          enum: [ACTIVE, REVOKED, EXPIRED]
          example: "ACTIVE"
        permissions:
          type: array
          description: List of permissions for this API key
          items:
            type: string
          example: ["PAYMENTS_CREATE", "PAYMENTS_READ"]
        rateLimit:
          type: object
          description: Rate limit configuration
          properties:
            requestsPerMinute:
              type: integer
              example: 100
            requestsPerHour:
              type: integer
              example: 5000
        usage:
          type: object
          description: Usage statistics for the API key
          properties:
            totalRequests:
              type: integer
              description: Total number of requests made
              example: 12543
            requestsToday:
              type: integer
              description: Number of requests made today
              example: 234
            requestsThisMonth:
              type: integer
              description: Number of requests made this month
              example: 4567
            lastUsedAt:
              type: string
              format: date-time
              description: When the API key was last used
              example: "2024-01-15T09:30:00Z"
        createdAt:
          type: string
          format: date-time
          description: When the API key was created
          example: "2024-01-01T10:00:00Z"
        expiresAt:
          type: string
          format: date-time
          description: When the API key expires (if applicable)
          example: "2024-12-31T23:59:59Z"
        metadata:
          type: object
          description: Additional metadata for the API key
          additionalProperties: true

    # ================================================
    # WEBHOOK SCHEMAS
    # ================================================
    ClerkWebhookRequest:
      type: object
      title: "ClerkWebhookRequest"
      properties:
        data:
          type: object
          description: The data of the webhook event
          additionalProperties: true
        object:
          type: string
          description: The type of object that triggered the event
          example: "event"
        type:
          type: string
          description: The type of event
          example: "user.created"
        created_at:
          type: integer
          format: int64
          description: Unix timestamp when the event was created
          example: **********

    # ================================================
    # KYC SCHEMAS
    # ================================================
    StartKYCVerificationRequest:
      type: object
      title: "StartKYCVerificationRequest"
      required:
        - provider
        - verificationLevel
        - individual
      properties:
        provider:
          type: string
          description: KYC provider to use for verification
          enum: [PROVE, JUMIO, ONFIDO, MANUAL]
          example: "PROVE"
        verificationLevel:
          type: string
          description: Level of verification required
          enum: [BASIC, ENHANCED, PREMIUM]
          example: "BASIC"
        individual:
          $ref: "#/components/schemas/KYCIndividual"
        metadata:
          type: object
          description: Additional metadata for the verification
          additionalProperties: true

    StartKYCVerificationResponse:
      type: object
      title: "StartKYCVerificationResponse"
      properties:
        verificationId:
          type: string
          description: Unique identifier for the verification
          example: "kyc_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        correlationId:
          type: string
          description: Provider correlation ID for tracking
          example: "prove_abc123def456"
        status:
          type: string
          description: Current verification status
          enum: [NOT_STARTED, IN_PROGRESS, VERIFIED, FAILED, EXPIRED]
          example: "IN_PROGRESS"
        provider:
          type: string
          description: KYC provider being used
          example: "PROVE"
        verificationLevel:
          type: string
          description: Level of verification
          example: "BASIC"
        nextStep:
          type: string
          description: Next step in the verification process
          example: "validate"
        message:
          type: string
          description: Human-readable message about the verification
          example: "Verification started successfully"
        createdAt:
          type: string
          format: date-time
          description: When the verification was created
          example: "2024-01-15T10:00:00Z"

    ValidateKYCStepResponse:
      type: object
      title: "ValidateKYCStepResponse"
      properties:
        correlationId:
          type: string
          description: Provider correlation ID
          example: "prove_abc123def456"
        isValid:
          type: boolean
          description: Whether the validation step was successful
          example: true
        nextStep:
          type: string
          description: Next step in the verification process
          example: "challenge"
        message:
          type: string
          description: Human-readable message about the validation
          example: "Validation successful"
        verificationId:
          type: string
          description: Verification ID
          example: "kyc_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        status:
          type: string
          description: Current verification status
          example: "IN_PROGRESS"

    ChallengeKYCStepRequest:
      type: object
      title: "ChallengeKYCStepRequest"
      properties:
        dateOfBirth:
          type: string
          pattern: '^\d{4}-\d{2}-\d{2}$'
          description: Date of birth in YYYY-MM-DD format
          example: "1990-01-01"
        last4SSN:
          type: string
          pattern: '^\d{4}$'
          description: Last 4 digits of Social Security Number
          example: "1234"
        challengeAnswers:
          type: array
          description: Answers to challenge questions
          items:
            type: string
          example: ["Answer 1", "Answer 2"]

    ChallengeKYCStepResponse:
      type: object
      title: "ChallengeKYCStepResponse"
      properties:
        correlationId:
          type: string
          description: Provider correlation ID
          example: "prove_abc123def456"
        success:
          type: boolean
          description: Whether the challenge step was successful
          example: true
        challengeQuestions:
          type: array
          description: Challenge questions (if any)
          items:
            type: string
          example: []
        verificationId:
          type: string
          description: Verification ID
          example: "kyc_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        nextStep:
          type: string
          description: Next step in the verification process
          example: "complete"
        message:
          type: string
          description: Human-readable message about the challenge
          example: "Challenge step completed"

    CompleteKYCVerificationRequest:
      type: object
      title: "CompleteKYCVerificationRequest"
      required:
        - individual
      properties:
        individual:
          $ref: "#/components/schemas/KYCIndividual"

    CompleteKYCVerificationResponse:
      type: object
      title: "CompleteKYCVerificationResponse"
      properties:
        correlationId:
          type: string
          description: Provider correlation ID
          example: "prove_abc123def456"
        verificationId:
          type: string
          description: Verification ID
          example: "kyc_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        status:
          type: string
          description: Final verification status
          enum: [VERIFIED, FAILED]
          example: "VERIFIED"
        success:
          type: boolean
          description: Whether the verification was successful
          example: true
        verificationResult:
          type: string
          description: Detailed verification result
          example: "VERIFIED"
        individual:
          $ref: "#/components/schemas/KYCIndividual"
        completedAt:
          type: string
          format: date-time
          description: When the verification was completed
          example: "2024-01-15T10:30:00Z"
        message:
          type: string
          description: Human-readable message about the completion
          example: "Verification completed successfully"
        score:
          type: number
          description: Verification confidence score (0-100)
          minimum: 0
          maximum: 100
          example: 95.5

    GetKYCStatusResponse:
      type: object
      title: "GetKYCStatusResponse"
      properties:
        hasVerification:
          type: boolean
          description: Whether the user has any verification
          example: true
        verificationId:
          type: string
          description: Verification ID (if exists)
          example: "kyc_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        status:
          type: string
          description: Current verification status
          enum: [NOT_STARTED, IN_PROGRESS, VERIFIED, FAILED, EXPIRED]
          example: "VERIFIED"
        provider:
          type: string
          description: KYC provider used
          example: "PROVE"
        verificationLevel:
          type: string
          description: Level of verification
          example: "BASIC"
        createdAt:
          type: string
          format: date-time
          description: When the verification was created
          example: "2024-01-15T10:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: When the verification was last updated
          example: "2024-01-15T10:30:00Z"
        canRetry:
          type: boolean
          description: Whether the user can retry verification
          example: false
        metadata:
          type: object
          description: Verification metadata (filtered for security)
          properties:
            correlationId:
              type: string
              description: Provider correlation ID
              example: "prove_abc123def456"
            completedAt:
              type: string
              format: date-time
              description: When verification was completed
              example: "2024-01-15T10:30:00Z"
            verificationResult:
              type: string
              description: Verification result
              example: "VERIFIED"
            hasIndividualData:
              type: boolean
              description: Whether individual data is stored
              example: true
            challengeCompleted:
              type: boolean
              description: Whether challenge step was completed
              example: true
            retryCount:
              type: integer
              description: Number of retry attempts (if applicable)
              example: 0
            maxRetries:
              type: integer
              description: Maximum allowed retries
              example: 3
            canRetryAfter:
              type: string
              format: date-time
              description: When retry is allowed (if applicable)
              example: "2024-01-16T10:00:00Z"

    KYCIndividual:
      type: object
      title: "KYCIndividual"
      required:
        - firstName
        - lastName
        - dateOfBirth
        - phoneNumber
      properties:
        firstName:
          type: string
          description: First name
          example: "John"
        lastName:
          type: string
          description: Last name
          example: "Doe"
        dateOfBirth:
          type: string
          pattern: '^\d{4}-\d{2}-\d{2}$'
          description: Date of birth in YYYY-MM-DD format
          example: "1990-01-01"
        phoneNumber:
          type: string
          description: Phone number with country code
          example: "+1234567890"
        ssn:
          type: string
          description: Social Security Number (last 4 digits for security)
          example: "1234"
        emailAddresses:
          type: array
          description: Email addresses
          items:
            type: string
            format: email
          example: ["<EMAIL>"]
        addresses:
          type: array
          description: Physical addresses
          items:
            $ref: "#/components/schemas/KYCAddress"

    KYCAddress:
      type: object
      title: "KYCAddress"
      required:
        - street
        - city
        - region
        - postalCode
        - country
      properties:
        street:
          type: string
          description: Street address
          example: "123 Main St"
        city:
          type: string
          description: City
          example: "New York"
        region:
          type: string
          description: State or region
          example: "NY"
        postalCode:
          type: string
          description: Postal or ZIP code
          example: "10001"
        country:
          type: string
          description: Country code (ISO 3166-1 alpha-2)
          example: "US"

    # ================================================
    # ERROR SCHEMAS
    # ================================================
    ErrorResponse:
      type: object
      title: "ErrorResponse"
      properties:
        code:
          type: string
          description: Error code that can be used to programmatically identify the error
          example: "VALIDATION_ERROR"
        message:
          type: string
          description: Human-readable error message
          example: "The request contains invalid parameters"
        details:
          type: object
          description: Additional details about the error, if available
          additionalProperties: true
          example:
            field: "username"
            reason: "Username is already taken"
      required:
        - code
        - message

    ValidationError:
      type: object
      title: "ValidationError"
      properties:
        code:
          type: string
          enum: ["VALIDATION_ERROR"]
          description: Error code for validation errors
        message:
          type: string
          description: Human-readable error message
        validationErrors:
          type: array
          description: List of validation errors
          items:
            type: object
            properties:
              field:
                type: string
                description: The field that failed validation
              message:
                type: string
                description: The validation error message
              code:
                type: string
                description: Specific validation error code
      required:
        - code
        - message
        - validationErrors

paths:
  # ================================================
  # PAYMENT ENDPOINTS
  # ================================================
  /payments/cashapp:
    post:
      tags:
        - Payments
      operationId: createCashAppPayment
      summary: Create CashApp payment
      description: |
        Creates a new CashApp payment request.

        This endpoint initiates a payment process with CashApp and returns a payment URL
        that the user can use to complete the payment.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateCashAppPaymentRequest"
      responses:
        "201":
          description: "Payment created successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateCashAppPaymentResponse"
        "400":
          description: "Bad request - validation error"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "Unauthorized - authentication required"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-auth:
        type: "custom"
        authorizerId: ClerkAuth
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${CreateCashAppPaymentFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  /payments/cashapp/status:
    post:
      tags:
        - Payments
      operationId: checkCashAppPaymentStatus
      summary: Check CashApp payment status
      description: |
        Checks the current status of a CashApp payment.

        This endpoint allows you to verify whether a payment has been completed,
        is still pending, or has failed.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CheckCashAppPaymentStatusRequest"
      responses:
        "200":
          description: "Payment status retrieved successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CheckCashAppPaymentStatusResponse"
        "400":
          description: "Bad request - validation error"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "404":
          description: "Payment not found"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-auth:
        type: "custom"
        authorizerId: ClerkAuth
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${CheckCashAppPaymentStatusFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  # ================================================
  # APPLICATION ENDPOINTS
  # ================================================
  /applications:
    post:
      tags:
        - Applications
      operationId: submitApplication
      summary: Submit application
      description: |
        Submits a new application for organization admin or agent role.

        This endpoint allows authenticated users to apply to become organization administrators
        or to join existing organizations as agents. The user must be authenticated and
        already exist in the system before submitting an application.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SubmitApplicationRequest"
      responses:
        "201":
          description: "Application submitted successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SubmitApplicationResponse"
        "400":
          description: "Bad request - validation error"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "Unauthorized - authentication required"
        "409":
          description: "Conflict - user already has a pending application"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-auth:
        type: "custom"
        authorizerId: ClerkAuth
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${SubmitOrganizationApplicationFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  /applications/{applicationId}/approve:
    post:
      tags:
        - Applications
      operationId: approveApplication
      summary: Approve or reject application
      description: |
        Approves or rejects a pending application.

        This endpoint is restricted to users with appropriate permissions.
        When an application is approved, the necessary Clerk organizations
        and memberships are created.
      parameters:
        - name: applicationId
          in: path
          required: true
          schema:
            type: string
          description: ID of the application to process
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ApproveApplicationRequest"
      responses:
        "200":
          description: "Application processed successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ApproveApplicationResponse"
        "400":
          description: "Bad request - validation error"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "Unauthorized - authentication required"
        "403":
          description: "Forbidden - insufficient permissions"
        "404":
          description: "Application not found"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-auth:
        type: "custom"
        authorizerId: ClerkAuth
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ApproveApplicationFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  # ================================================
  # ORGANIZATION ENDPOINTS
  # ================================================
  /organizations:
    post:
      tags:
        - Organizations
      operationId: createOrganization
      summary: Create organization
      description: |
        Creates a new organization in the CLKK system.

        This endpoint is typically used by system administrators or through
        the application approval process.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrganizationRequest"
      responses:
        "201":
          description: "Organization created successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateOrganizationResponse"
        "400":
          description: "Bad request - validation error"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "Unauthorized - authentication required"
        "403":
          description: "Forbidden - insufficient permissions"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-auth:
        type: "custom"
        authorizerId: ClerkAuth
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${CreateOrganizationFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"



  # ================================================
  # USER ENDPOINTS
  # ================================================
  /users/validate-username:
    get:
      tags:
        - Users
      operationId: validateUsernameGet
      summary: Validate username (GET)
      description: |
        Validates a username for availability and format compliance.

        This endpoint checks if a username is valid according to the system rules
        and whether it's available for use.

        Note: Username validation is no longer required for application submission,
        as applications now work with existing authenticated users. This endpoint
        is available for other use cases like profile management.
      parameters:
        - name: username
          in: query
          required: true
          schema:
            type: string
            pattern: '^[a-zA-Z0-9_]{3,30}$'
          description: Username to validate
      responses:
        "200":
          description: "Username validation completed"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidateUsernameResponse"
        "400":
          description: "Bad request - validation error"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-auth:
        type: "custom"
        authorizerId: ClerkAuth
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ValidateUsernameFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"
    post:
      tags:
        - Users
      operationId: validateUsernamePost
      summary: Validate username (POST)
      description: |
        Validates a username for availability and format compliance.

        This endpoint checks if a username is valid according to the system rules
        and whether it's available for use.

        Note: Username validation is no longer required for application submission,
        as applications now work with existing authenticated users. This endpoint
        is available for other use cases like profile management.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ValidateUsernameRequest"
      responses:
        "200":
          description: "Username validation completed"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidateUsernameResponse"
        "400":
          description: "Bad request - validation error"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-auth:
        type: "custom"
        authorizerId: ClerkAuth
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ValidateUsernameFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  # ================================================
  # KYC ENDPOINTS
  # ================================================
  /kyc/start:
    post:
      tags:
        - KYC
      operationId: startKYCVerification
      summary: Start KYC verification
      description: |
        Initiates a new KYC (Know Your Customer) verification process.

        This endpoint starts the identity verification process with the specified provider
        and returns a verification ID and correlation ID for tracking the process.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/StartKYCVerificationRequest"
      responses:
        "201":
          description: "KYC verification started successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StartKYCVerificationResponse"
        "400":
          description: "Bad request - validation error"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "Unauthorized - authentication required"
        "409":
          description: "Conflict - verification already in progress"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-auth:
        type: "custom"
        authorizerId: ClerkAuth
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${KYCStartVerificationFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  /kyc/validate/{correlationId}:
    post:
      tags:
        - KYC
      operationId: validateKYCStep
      summary: Validate KYC verification step
      description: |
        Validates a step in the KYC verification process.

        This endpoint is used to validate the current step of the verification
        process using the correlation ID provided by the provider.
      parameters:
        - name: correlationId
          in: path
          required: true
          schema:
            type: string
          description: Provider correlation ID for the verification
      responses:
        "200":
          description: "KYC step validated successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidateKYCStepResponse"
        "400":
          description: "Bad request - validation error"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "Unauthorized - authentication required"
        "404":
          description: "Verification not found"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-auth:
        type: "custom"
        authorizerId: ClerkAuth
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${KYCValidateStepFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  /kyc/challenge/{correlationId}:
    post:
      tags:
        - KYC
      operationId: challengeKYCStep
      summary: Handle KYC challenge step
      description: |
        Handles the challenge step in the KYC verification process.

        This endpoint is used to submit challenge responses such as date of birth,
        SSN verification, or answers to challenge questions.
      parameters:
        - name: correlationId
          in: path
          required: true
          schema:
            type: string
          description: Provider correlation ID for the verification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ChallengeKYCStepRequest"
      responses:
        "200":
          description: "KYC challenge step processed successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChallengeKYCStepResponse"
        "400":
          description: "Bad request - validation error"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "Unauthorized - authentication required"
        "404":
          description: "Verification not found"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-auth:
        type: "custom"
        authorizerId: ClerkAuth
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${KYCChallengeStepFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  /kyc/complete/{correlationId}:
    post:
      tags:
        - KYC
      operationId: completeKYCVerification
      summary: Complete KYC verification
      description: |
        Completes the KYC verification process.

        This endpoint finalizes the verification process and updates the user's
        KYC status based on the verification results.
      parameters:
        - name: correlationId
          in: path
          required: true
          schema:
            type: string
          description: Provider correlation ID for the verification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CompleteKYCVerificationRequest"
      responses:
        "200":
          description: "KYC verification completed successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CompleteKYCVerificationResponse"
        "400":
          description: "Bad request - validation error"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "Unauthorized - authentication required"
        "404":
          description: "Verification not found"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-auth:
        type: "custom"
        authorizerId: ClerkAuth
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${KYCCompleteVerificationFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  /kyc/status/{verificationId}:
    get:
      tags:
        - KYC
      operationId: getKYCStatus
      summary: Get KYC verification status
      description: |
        Retrieves the status of a specific KYC verification.

        This endpoint returns the current status and details of a verification
        process identified by the verification ID.
      parameters:
        - name: verificationId
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier for the verification
      responses:
        "200":
          description: "KYC status retrieved successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetKYCStatusResponse"
        "400":
          description: "Bad request - validation error"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "Unauthorized - authentication required"
        "404":
          description: "Verification not found"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-auth:
        type: "custom"
        authorizerId: ClerkAuth
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${KYCGetStatusFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  /kyc/user/status:
    get:
      tags:
        - KYC
      operationId: getUserKYCStatus
      summary: Get user's latest KYC status
      description: |
        Retrieves the latest KYC verification status for the authenticated user.

        This endpoint returns the most recent verification status and details
        for the current user, or indicates if no verification has been started.
      responses:
        "200":
          description: "User KYC status retrieved successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetKYCStatusResponse"
        "401":
          description: "Unauthorized - authentication required"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-auth:
        type: "custom"
        authorizerId: ClerkAuth
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${KYCGetStatusFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  # ================================================
  # PARTNER API KEY ENDPOINTS
  # ================================================
  /api-keys:
    post:
      tags:
        - API Keys
      operationId: generateAPIKey
      summary: Generate API key
      description: |
        Generates a new API key for the authenticated organization.

        This endpoint creates a new API key with the specified permissions and
        configuration. The actual API key is only shown once in the response.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GenerateAPIKeyRequest"
      responses:
        "201":
          description: "API key generated successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GenerateAPIKeyResponse"
        "400":
          description: "Bad request - validation error"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "Unauthorized - authentication required"
        "403":
          description: "Forbidden - insufficient permissions"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-auth:
        type: "custom"
        authorizerId: ClerkAuth
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${GenerateAPIKeyFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

    get:
      tags:
        - API Keys
      operationId: listAPIKeys
      summary: List API keys
      description: |
        Lists all API keys for the authenticated organization.

        This endpoint returns a paginated list of API keys with their status
        and usage information. API key values are never shown in list responses.
      parameters:
        - $ref: "#/components/parameters/PageSize"
        - $ref: "#/components/parameters/PageToken"
        - name: status
          in: query
          required: false
          schema:
            type: string
            enum: [ACTIVE, REVOKED, EXPIRED]
          description: Filter by key status
        - name: type
          in: query
          required: false
          schema:
            type: string
            enum: [TEST, LIVE]
          description: Filter by key type
      responses:
        "200":
          description: "API keys retrieved successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ListAPIKeysResponse"
        "401":
          description: "Unauthorized - authentication required"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-auth:
        type: "custom"
        authorizerId: ClerkAuth
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ListAPIKeysFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  /api-keys/{keyId}:
    get:
      tags:
        - API Keys
      operationId: getAPIKeyDetails
      summary: Get API key details
      description: |
        Retrieves detailed information about a specific API key.

        This endpoint returns comprehensive details about an API key including
        its configuration, permissions, and usage statistics.
      parameters:
        - name: keyId
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier for the API key
      responses:
        "200":
          description: "API key details retrieved successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAPIKeyDetailsResponse"
        "401":
          description: "Unauthorized - authentication required"
        "404":
          description: "API key not found"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-auth:
        type: "custom"
        authorizerId: ClerkAuth
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${GetAPIKeyDetailsFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

    delete:
      tags:
        - API Keys
      operationId: revokeAPIKey
      summary: Revoke API key
      description: |
        Revokes an API key, immediately invalidating it.

        This endpoint permanently revokes an API key. Once revoked, the key
        cannot be reactivated and will no longer authenticate requests.
      parameters:
        - name: keyId
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier for the API key to revoke
      responses:
        "200":
          description: "API key revoked successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RevokeAPIKeyResponse"
        "401":
          description: "Unauthorized - authentication required"
        "404":
          description: "API key not found"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-auth:
        type: "custom"
        authorizerId: ClerkAuth
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${RevokeAPIKeyFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  # ================================================
  # WEBHOOK ENDPOINTS
  # ================================================
  /webhooks/clerk:
    post:
      tags:
        - Webhooks
      operationId: clerkWebhook
      summary: Clerk webhook handler
      description: |
        Handles webhook events from Clerk.
        This endpoint processes various Clerk events such as user creation,
        organization updates, and membership changes.
      security: []  # Webhooks don't use bearer auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ClerkWebhookRequest"
      responses:
        "200":
          description: "Webhook processed successfully"
        "400":
          description: "Bad request - validation error"
        "401":
          description: "Unauthorized - invalid signature"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-auth:
        type: NONE
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ClerkWebhookFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"
