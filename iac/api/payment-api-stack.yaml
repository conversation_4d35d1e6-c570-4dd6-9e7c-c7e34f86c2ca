AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: CLKK Payment System - Centralized API Gateway

Parameters:
  ApiStageName:
    Type: String
    Default: dev
    Description: The API Gateway stage name

  ClerkAuthorizerArn:
    Type: String
    Description: ARN of the Clerk JWT Authorizer

  # Payment Function ARNs
  CreateCashAppPaymentFunctionArn:
    Type: String
    Description: ARN of the Create CashApp Payment function

  CheckCashAppPaymentStatusFunctionArn:
    Type: String
    Description: ARN of the Check CashApp Payment Status function

  # Organization Management Function ARNs
  SubmitOrganizationApplicationFunctionArn:
    Type: String
    Description: ARN of the Submit Organization Application function

  ApproveApplicationFunctionArn:
    Type: String
    Description: ARN of the Approve Application function

  CreateOrganizationFunctionArn:
    Type: String
    Description: ARN of the Create Organization function



  # Webhook Function ARNs
  ClerkWebhookFunctionArn:
    Type: String
    Description: ARN of the Clerk Webhook Handler function

  # User Management Function ARNs
  ValidateUsernameFunctionArn:
    Type: String
    Description: ARN of the Validate Username function

  # KYC Function ARNs
  KYCStartVerificationFunctionArn:
    Type: String
    Description: ARN of the KYC Start Verification function

  KYCValidateStepFunctionArn:
    Type: String
    Description: ARN of the KYC Validate Step function

  KYCChallengeStepFunctionArn:
    Type: String
    Description: ARN of the KYC Challenge Step function

  KYCCompleteVerificationFunctionArn:
    Type: String
    Description: ARN of the KYC Complete Verification function

  KYCGetStatusFunctionArn:
    Type: String
    Description: ARN of the KYC Get Status function

  # Partner API Key Function ARNs
  GenerateAPIKeyFunctionArn:
    Type: String
    Description: ARN of the Generate API Key function

  ListAPIKeysFunctionArn:
    Type: String
    Description: ARN of the List API Keys function

  RevokeAPIKeyFunctionArn:
    Type: String
    Description: ARN of the Revoke API Key function

  GetAPIKeyDetailsFunctionArn:
    Type: String
    Description: ARN of the Get API Key Details function

Resources:
  # ================================================
  # CENTRALIZED API GATEWAY
  # ================================================

  PaymentSystemApi:
    Type: AWS::Serverless::Api
    Properties:
      Name: !Sub "${ApiStageName}-clkk-payment-system-api-v2"
      StageName: !Ref ApiStageName
      DefinitionBody:
        Fn::Transform:
          Name: AWS::Include
          Parameters:
            Location: ./payment-api.yaml
      TracingEnabled: true
      Cors:
        AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Clerk-Auth'"
        AllowOrigin: "'*'"
        MaxAge: "'600'"
      Auth:
        DefaultAuthorizer: ClerkAuth
        Authorizers:
          ClerkAuth:
            FunctionArn: !Ref ClerkAuthorizerArn
            FunctionPayloadType: TOKEN
            Identity:
              Header: Authorization
              ValidationExpression: '^Bearer [-0-9a-zA-Z\._]*$'
              ReauthorizeEvery: 0
      GatewayResponses:
        DEFAULT_4XX:
          ResponseParameters:
            Headers:
              Access-Control-Allow-Origin: "'*'"
              Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Clerk-Auth'"
              Access-Control-Allow-Methods: "'GET,POST,PUT,DELETE,OPTIONS'"
        DEFAULT_5XX:
          ResponseParameters:
            Headers:
              Access-Control-Allow-Origin: "'*'"
              Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Clerk-Auth'"
              Access-Control-Allow-Methods: "'GET,POST,PUT,DELETE,OPTIONS'"
    DeletionPolicy: Delete
    UpdateReplacePolicy: Delete

  # ================================================
  # LAMBDA PERMISSIONS FOR PAYMENT ENDPOINTS
  # ================================================

  CreateCashAppPaymentPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !Ref CreateCashAppPaymentFunctionArn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${PaymentSystemApi}/*/POST/payments/cashapp"

  CheckCashAppPaymentStatusPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !Ref CheckCashAppPaymentStatusFunctionArn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${PaymentSystemApi}/*/POST/payments/cashapp/status"

  # ================================================
  # LAMBDA PERMISSIONS FOR ORGANIZATION ENDPOINTS
  # ================================================

  SubmitOrganizationApplicationPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !Ref SubmitOrganizationApplicationFunctionArn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${PaymentSystemApi}/*/POST/applications"

  ApproveApplicationPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !Ref ApproveApplicationFunctionArn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${PaymentSystemApi}/*/POST/applications/{applicationId}/approve"

  CreateOrganizationPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !Ref CreateOrganizationFunctionArn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${PaymentSystemApi}/*/POST/organizations"


  # ================================================
  # LAMBDA PERMISSIONS FOR WEBHOOK ENDPOINTS
  # ================================================

  ClerkWebhookPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !Ref ClerkWebhookFunctionArn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${PaymentSystemApi}/*/POST/webhooks/clerk"

  # ================================================
  # LAMBDA PERMISSIONS FOR USER MANAGEMENT ENDPOINTS
  # ================================================

  ValidateUsernamePermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !Ref ValidateUsernameFunctionArn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${PaymentSystemApi}/*/*/users/validate-username"

  # ================================================
  # LAMBDA PERMISSIONS FOR KYC ENDPOINTS
  # ================================================

  KYCStartVerificationPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !Ref KYCStartVerificationFunctionArn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${PaymentSystemApi}/*/POST/kyc/start"

  KYCValidateStepPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !Ref KYCValidateStepFunctionArn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${PaymentSystemApi}/*/POST/kyc/validate/{correlationId}"

  KYCChallengeStepPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !Ref KYCChallengeStepFunctionArn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${PaymentSystemApi}/*/POST/kyc/challenge/{correlationId}"

  KYCCompleteVerificationPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !Ref KYCCompleteVerificationFunctionArn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${PaymentSystemApi}/*/POST/kyc/complete/{correlationId}"

  KYCGetStatusPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !Ref KYCGetStatusFunctionArn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${PaymentSystemApi}/*/GET/kyc/status/{verificationId}"

  KYCGetUserStatusPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !Ref KYCGetStatusFunctionArn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${PaymentSystemApi}/*/GET/kyc/user/status"

  # ================================================
  # LAMBDA PERMISSIONS FOR PARTNER API KEY ENDPOINTS
  # ================================================

  GenerateAPIKeyPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !Ref GenerateAPIKeyFunctionArn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${PaymentSystemApi}/*/POST/api-keys"

  ListAPIKeysPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !Ref ListAPIKeysFunctionArn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${PaymentSystemApi}/*/GET/api-keys"

  RevokeAPIKeyPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !Ref RevokeAPIKeyFunctionArn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${PaymentSystemApi}/*/DELETE/api-keys/{keyId}"

  GetAPIKeyDetailsPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !Ref GetAPIKeyDetailsFunctionArn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${PaymentSystemApi}/*/GET/api-keys/{keyId}"

Outputs:
  ApiEndpoint:
    Description: "CLKK Payment System API Gateway endpoint URL"
    Value: !Sub "https://${PaymentSystemApi}.execute-api.${AWS::Region}.amazonaws.com/${ApiStageName}"
    Export:
      Name: !Sub "${ApiStageName}-payment-system-api-endpoint"

  ApiId:
    Description: "CLKK Payment System API Gateway ID"
    Value: !Ref PaymentSystemApi
    Export:
      Name: !Sub "${ApiStageName}-payment-system-api-id"

  ApiArn:
    Description: "CLKK Payment System API Gateway ARN"
    Value: !Sub "arn:aws:apigateway:${AWS::Region}::/restapis/${PaymentSystemApi}"
    Export:
      Name: !Sub "${ApiStageName}-payment-system-api-arn"