AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: >
  Clerk Webhooks Stack

  This stack contains the resources for handling Clerk webhook events

Parameters:
  Environment:
    Type: String
    Default: dev
    Description: The environment to deploy to

  LayerCommons:
    Type: String
    Description: The Layer Commons ARN

  DynamoTableName:
    Type: String
    Description: The DynamoDB Table Name

Globals:
  Function:
    Tracing: Active
    Runtime: nodejs18.x
    Architectures:
      - arm64
    MemorySize: 1024
    Timeout: 30

Resources:
  ##################################################################################################################
  # SNS Topics and SQS Queues (Messaging Resources)
  ##################################################################################################################

  # Add SNS Topic for CloudWatch Alarms
  AlarmNotificationTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub "${Environment}-clerk-webhook-alarms"
      DisplayName: !Sub "${Environment} Clerk Webhook Alarms"

  # Add SNS Topic for Organizations
  OrganizationWebhookTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub "${Environment}-organization-webhook-events"
      DisplayName: Organization Webhook Events Fanout
      Subscription:
        - Protocol: sqs
          Endpoint: !GetAtt OrganizationDynamoUpdatesQueue.Arn

  # Add SNS Topic for Users
  UserWebhookTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub "${Environment}-user-webhook-events"
      DisplayName: User Webhook Events Fanout
      Subscription:
        - Protocol: sqs
          Endpoint: !GetAtt UserDynamoUpdatesQueue.Arn

  # Add SQS Queues for Organization updates
  OrganizationDynamoUpdatesQueue:
    Type: AWS::SQS::Queue
    Properties:
      VisibilityTimeout: 300
      RedrivePolicy:
        maxReceiveCount: 3
        deadLetterTargetArn: !GetAtt OrganizationDynamoUpdatesDLQ.Arn
      MessageRetentionPeriod: 1209600 # 14 days

  OrganizationDynamoUpdatesDLQ:
    Type: AWS::SQS::Queue
    Properties:
      MessageRetentionPeriod: 1209600 # 14 days

  # Add SQS Queues for User updates
  UserDynamoUpdatesQueue:
    Type: AWS::SQS::Queue
    Properties:
      VisibilityTimeout: 300
      RedrivePolicy:
        maxReceiveCount: 3
        deadLetterTargetArn: !GetAtt UserDynamoUpdatesDLQ.Arn
      MessageRetentionPeriod: 1209600 # 14 days

  UserDynamoUpdatesDLQ:
    Type: AWS::SQS::Queue
    Properties:
      MessageRetentionPeriod: 1209600 # 14 days

  # Add SQS Queue Policies
  OrganizationDynamoUpdatesQueuePolicy:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - !Ref OrganizationDynamoUpdatesQueue
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: "sns.amazonaws.com"
            Action: "sqs:SendMessage"
            Resource: !GetAtt OrganizationDynamoUpdatesQueue.Arn
            Condition:
              ArnEquals:
                "aws:SourceArn": !Ref OrganizationWebhookTopic

  UserDynamoUpdatesQueuePolicy:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        - !Ref UserDynamoUpdatesQueue
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: "sns.amazonaws.com"
            Action: "sqs:SendMessage"
            Resource: !GetAtt UserDynamoUpdatesQueue.Arn
            Condition:
              ArnEquals:
                "aws:SourceArn": !Ref UserWebhookTopic

  ##################################################################################################################
  # Lambda Functions
  ##################################################################################################################

  # Webhook Handler Function
  ClerkWebhookFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${Environment}-ClerkWebhookFunction
      CodeUri: ../../lambdas/clerk-webhooks/
      Handler: index.handler
      MemorySize: 1024
      Timeout: 30
      Environment:
        Variables:
          ORGANIZATION_TOPIC_ARN: !Ref OrganizationWebhookTopic
          USER_TOPIC_ARN: !Ref UserWebhookTopic
          TABLE_NAME: !Ref DynamoTableName
          ENVIRONMENT: !Ref Environment
          POWERTOOLS_SERVICE_NAME: clerk-webhook
          LOG_LEVEL: INFO
          PARAMETERS_SECRETS_EXTENSION_CACHE_ENABLED: true
          PARAMETERS_SECRETS_EXTENSION_CACHE_SIZE: 1000
          SECRETS_MANAGER_TTL: 300
      Policies:
        - SNSPublishMessagePolicy:
            TopicName: !GetAtt OrganizationWebhookTopic.TopicName
        - SNSPublishMessagePolicy:
            TopicName: !GetAtt UserWebhookTopic.TopicName
        - DynamoDBCrudPolicy:
            TableName: !Ref DynamoTableName
        - Statement:
            - Effect: Allow
              Action:
                - secretsmanager:GetSecretValue
              Resource: !Sub "arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${Environment}/clkk/clerk/webhook-secret*"
      Layers:
        - !Ref LayerCommons
        - !Sub "arn:aws:lambda:${AWS::Region}:************:layer:AWS-Parameters-and-Secrets-Lambda-Extension-Arm64:11"
    Metadata:
      BuildMethod: esbuild
      BuildProperties:
        External:
          - commons
        Minify: true
        Target: "es2020"
        Sourcemap: true
        EntryPoints:
          - index.ts

  # Organization DynamoDB Updater
  OrganizationDynamoUpdater:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${Environment}-OrganizationDynamoUpdater
      CodeUri: ../../lambdas/clerk-webhooks/
      Handler: organization-processor.handler
      MemorySize: 512
      Timeout: 30
      Environment:
        Variables:
          TABLE_NAME: !Ref DynamoTableName
          POWERTOOLS_SERVICE_NAME: organization-processor
          LOG_LEVEL: INFO
      Events:
        SQSEvent:
          Type: SQS
          Properties:
            Queue: !GetAtt OrganizationDynamoUpdatesQueue.Arn
            BatchSize: 10
      Policies:
        - SQSPollerPolicy:
            QueueName: !GetAtt OrganizationDynamoUpdatesQueue.QueueName
        - DynamoDBCrudPolicy:
            TableName: !Ref DynamoTableName
      Layers:
        - !Ref LayerCommons
    Metadata:
      BuildMethod: esbuild
      BuildProperties:
        External:
          - commons
        Minify: true
        Target: "es2020"
        Sourcemap: true
        EntryPoints:
          - organization-processor.ts

  # User DynamoDB Updater
  UserDynamoUpdater:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${Environment}-UserDynamoUpdater
      CodeUri: ../../lambdas/clerk-webhooks/
      Handler: user-processor.handler
      MemorySize: 512
      Timeout: 30
      Environment:
        Variables:
          TABLE_NAME: !Ref DynamoTableName
          POWERTOOLS_SERVICE_NAME: user-processor
          LOG_LEVEL: INFO
      Events:
        SQSEvent:
          Type: SQS
          Properties:
            Queue: !GetAtt UserDynamoUpdatesQueue.Arn
            BatchSize: 10
      Policies:
        - SQSPollerPolicy:
            QueueName: !GetAtt UserDynamoUpdatesQueue.QueueName
        - DynamoDBCrudPolicy:
            TableName: !Ref DynamoTableName
      Layers:
        - !Ref LayerCommons
    Metadata:
      BuildMethod: esbuild
      BuildProperties:
        External:
          - commons
        Minify: true
        Target: "es2020"
        Sourcemap: true
        EntryPoints:
          - user-processor.ts

  ##################################################################################################################
  # CloudWatch Alarms
  ##################################################################################################################

  # Critical function error alarms
  ClerkWebhookErrorAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: !Sub "Errors detected in ${Environment} ClerkWebhookFunction"
      Namespace: "AWS/Lambda"
      MetricName: "Errors"
      Dimensions:
        - Name: FunctionName
          Value: !Ref ClerkWebhookFunction
      Statistic: Sum
      Period: 60
      EvaluationPeriods: 1
      Threshold: 1
      ComparisonOperator: GreaterThanOrEqualToThreshold
      TreatMissingData: notBreaching
      AlarmActions:
        - !Ref AlarmNotificationTopic
      OKActions:
        - !Ref AlarmNotificationTopic

  OrganizationDynamoUpdaterErrorAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: !Sub "Errors detected in ${Environment} OrganizationDynamoUpdater"
      Namespace: "AWS/Lambda"
      MetricName: "Errors"
      Dimensions:
        - Name: FunctionName
          Value: !Ref OrganizationDynamoUpdater
      Statistic: Sum
      Period: 60
      EvaluationPeriods: 1
      Threshold: 1
      ComparisonOperator: GreaterThanOrEqualToThreshold
      TreatMissingData: notBreaching
      AlarmActions:
        - !Ref AlarmNotificationTopic
      OKActions:
        - !Ref AlarmNotificationTopic

  UserDynamoUpdaterErrorAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: !Sub "Errors detected in ${Environment} UserDynamoUpdater"
      Namespace: "AWS/Lambda"
      MetricName: "Errors"
      Dimensions:
        - Name: FunctionName
          Value: !Ref UserDynamoUpdater
      Statistic: Sum
      Period: 60
      EvaluationPeriods: 1
      Threshold: 1
      ComparisonOperator: GreaterThanOrEqualToThreshold
      TreatMissingData: notBreaching
      AlarmActions:
        - !Ref AlarmNotificationTopic
      OKActions:
        - !Ref AlarmNotificationTopic

  # DLQ monitoring alarms
  OrganizationDynamoUpdatesDLQAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: !Sub "Messages in ${Environment} OrganizationDynamoUpdatesDLQ"
      Namespace: "AWS/SQS"
      MetricName: "ApproximateNumberOfMessagesVisible"
      Dimensions:
        - Name: QueueName
          Value: !GetAtt OrganizationDynamoUpdatesDLQ.QueueName
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 1
      Threshold: 1
      ComparisonOperator: GreaterThanOrEqualToThreshold
      TreatMissingData: notBreaching
      AlarmActions:
        - !Ref AlarmNotificationTopic
      OKActions:
        - !Ref AlarmNotificationTopic

  UserDynamoUpdatesDLQAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmDescription: !Sub "Messages in ${Environment} UserDynamoUpdatesDLQ"
      Namespace: "AWS/SQS"
      MetricName: "ApproximateNumberOfMessagesVisible"
      Dimensions:
        - Name: QueueName
          Value: !GetAtt UserDynamoUpdatesDLQ.QueueName
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 1
      Threshold: 1
      ComparisonOperator: GreaterThanOrEqualToThreshold
      TreatMissingData: notBreaching
      AlarmActions:
        - !Ref AlarmNotificationTopic
      OKActions:
        - !Ref AlarmNotificationTopic

Outputs:
  ClerkWebhookEndpoint:
    Description: "Clerk Webhook Endpoint URL"
    Value: "Webhook endpoint is now provided by the centralized API"

  OrganizationWebhookTopic:
    Description: "Organization Webhook SNS Topic ARN"
    Value: !Ref OrganizationWebhookTopic

  UserWebhookTopic:
    Description: "User Webhook SNS Topic ARN"
    Value: !Ref UserWebhookTopic

  AlarmNotificationTopic:
    Description: "CloudWatch Alarm SNS Topic ARN"
    Value: !Ref AlarmNotificationTopic

  WebhookHandlerFunction:
    Description: "Clerk Webhook Handler Function ARN"
    Value: !GetAtt ClerkWebhookFunction.Arn
