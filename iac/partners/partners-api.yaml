openapi: "3.0.1"
info:
  title: CLKK Partner API
  version: "1.0.0"
  description: |
    # CLKK Partner API

    The CLKK Partner API provides endpoints for managing API keys and partner integrations.

    ## Authentication

    All API endpoints require authentication using either:
    - **JWT Token**: For user-authenticated requests (managing API keys)
    - **API Key**: For partner API requests (using generated keys)

    ## Rate Limiting

    API requests are subject to rate limiting based on the API key configuration.

servers:
  - url: https://api.clkk.com/v1
    description: Production server
  - url: https://api-sandbox.clkk.com/v1
    description: Sandbox server

tags:
  - name: API Keys
    description: Endpoints for managing partner API keys
  - name: Authentication
    description: Partner authentication endpoints

paths:
  # ================================================
  # API KEY MANAGEMENT ENDPOINTS
  # ================================================

  /api-keys:
    post:
      tags:
        - API Keys
      summary: Generate a new API key
      description: |
        Generates a new API key for the authenticated organization.
        
        The API key is shown only once in the response. Partners must store it securely
        as it cannot be retrieved again.
      operationId: generateAPIKey
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GenerateAPIKeyRequest"
            examples:
              live_key:
                summary: Generate a live API key
                value:
                  type: "LIVE"
                  label: "Production API Key"
                  expiresAt: "2025-12-31T23:59:59Z"
                  rateLimit:
                    requestsPerSecond: 20
                    requestsPerMinute: 200
                  permissions:
                    allowedEndpoints: ["/api/v1/payments/*"]
                    allowedMethods: ["GET", "POST"]
              test_key:
                summary: Generate a test API key
                value:
                  type: "TEST"
                  label: "Development API Key"
      responses:
        "201":
          description: API key created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GenerateAPIKeyResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "500":
          $ref: "#/components/responses/InternalServerError"
      x-amazon-apigateway-integration:
        type: aws_proxy
        httpMethod: POST
        uri:
          Fn::Sub: "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${GenerateAPIKeyFunctionArn}/invocations"

    get:
      tags:
        - API Keys
      summary: List API keys
      description: |
        Retrieves a list of API keys for the authenticated organization.
        
        The response includes metadata about each key but not the actual key values.
      operationId: listAPIKeys
      security:
        - BearerAuth: []
      parameters:
        - name: status
          in: query
          description: Filter by key status
          required: false
          schema:
            type: string
            enum: [ACTIVE, REVOKED, EXPIRED, SUSPENDED]
        - name: type
          in: query
          description: Filter by key type
          required: false
          schema:
            type: string
            enum: [LIVE, TEST]
        - name: limit
          in: query
          description: Maximum number of keys to return
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 50
      responses:
        "200":
          description: API keys retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ListAPIKeysResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "500":
          $ref: "#/components/responses/InternalServerError"
      x-amazon-apigateway-integration:
        type: aws_proxy
        httpMethod: POST
        uri:
          Fn::Sub: "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ListAPIKeysFunctionArn}/invocations"

  /api-keys/{keyId}:
    parameters:
      - name: keyId
        in: path
        description: The ID of the API key
        required: true
        schema:
          type: string
          pattern: "^key_[a-zA-Z0-9]+$"

    get:
      tags:
        - API Keys
      summary: Get API key details
      description: |
        Retrieves detailed information about a specific API key, including
        usage statistics and audit logs.
      operationId: getAPIKeyDetails
      security:
        - BearerAuth: []
      parameters:
        - name: includeAuditLogs
          in: query
          description: Include audit logs in the response
          required: false
          schema:
            type: boolean
            default: false
        - name: auditStartTime
          in: query
          description: Start time for audit logs (ISO 8601)
          required: false
          schema:
            type: string
            format: date-time
        - name: auditEndTime
          in: query
          description: End time for audit logs (ISO 8601)
          required: false
          schema:
            type: string
            format: date-time
        - name: auditLimit
          in: query
          description: Maximum number of audit logs to return
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 50
      responses:
        "200":
          description: API key details retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/APIKeyDetailsResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
      x-amazon-apigateway-integration:
        type: aws_proxy
        httpMethod: POST
        uri:
          Fn::Sub: "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${GetAPIKeyDetailsFunctionArn}/invocations"

    delete:
      tags:
        - API Keys
      summary: Revoke an API key
      description: |
        Revokes an API key, preventing it from being used for authentication.
        
        This action is irreversible. The key cannot be reactivated once revoked.
      operationId: revokeAPIKey
      security:
        - BearerAuth: []
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  description: Reason for revoking the key
                  example: "Compromised key"
      responses:
        "200":
          description: API key revoked successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "API key revoked successfully"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
      x-amazon-apigateway-integration:
        type: aws_proxy
        httpMethod: POST
        uri:
          Fn::Sub: "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${RevokeAPIKeyFunctionArn}/invocations"

components:
  # ================================================
  # SECURITY SCHEMES
  # ================================================

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for user authentication
      
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API key for partner authentication

  # ================================================
  # REQUEST/RESPONSE SCHEMAS
  # ================================================

  schemas:
    GenerateAPIKeyRequest:
      type: object
      properties:
        type:
          type: string
          enum: [LIVE, TEST]
          description: Type of API key
          default: LIVE
        label:
          type: string
          description: User-friendly label for the key
          maxLength: 100
          example: "Production API Key"
        expiresAt:
          type: string
          format: date-time
          description: Optional expiration date for the key
          example: "2025-12-31T23:59:59Z"
        rateLimit:
          $ref: "#/components/schemas/APIKeyRateLimit"
        permissions:
          $ref: "#/components/schemas/APIKeyPermissions"

    GenerateAPIKeyResponse:
      type: object
      required:
        - success
        - data
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          required:
            - keyId
            - keySecret
            - keyPrefix
            - type
            - createdAt
          properties:
            keyId:
              type: string
              description: Unique identifier for the API key
              example: "key_7hY8iJkL9mNo0pQr"
            keySecret:
              type: string
              description: The full API key (shown only once)
              example: "ckpl_1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p"
            keyPrefix:
              type: string
              description: Display prefix for the key
              example: "ckpl_1a2b3c4d"
            type:
              type: string
              enum: [LIVE, TEST]
              example: "LIVE"
            label:
              type: string
              example: "Production API Key"
            createdAt:
              type: string
              format: date-time
              example: "2024-01-15T10:00:00Z"
            expiresAt:
              type: string
              format: date-time
              example: "2025-12-31T23:59:59Z"

    ListAPIKeysResponse:
      type: object
      required:
        - success
        - data
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          required:
            - keys
            - count
          properties:
            keys:
              type: array
              items:
                $ref: "#/components/schemas/APIKeyListItem"
            count:
              type: integer
              description: Total number of keys returned
              example: 5

    APIKeyListItem:
      type: object
      required:
        - keyId
        - keyPrefix
        - type
        - status
        - createdAt
      properties:
        keyId:
          type: string
          example: "key_7hY8iJkL9mNo0pQr"
        keyPrefix:
          type: string
          example: "ckpl_1a2b3c4d"
        label:
          type: string
          example: "Production API Key"
        type:
          type: string
          enum: [LIVE, TEST]
          example: "LIVE"
        status:
          type: string
          enum: [ACTIVE, REVOKED, EXPIRED, SUSPENDED]
          example: "ACTIVE"
        createdAt:
          type: string
          format: date-time
          example: "2024-01-15T10:00:00Z"
        expiresAt:
          type: string
          format: date-time
          example: "2025-12-31T23:59:59Z"
        lastUsedAt:
          type: string
          format: date-time
          example: "2024-01-15T11:30:00Z"
        usageCount:
          type: integer
          description: Number of times the key has been used
          example: 150

    APIKeyDetailsResponse:
      type: object
      required:
        - success
        - data
      properties:
        success:
          type: boolean
          example: true
        data:
          allOf:
            - $ref: "#/components/schemas/APIKeyListItem"
            - type: object
              properties:
                rateLimit:
                  $ref: "#/components/schemas/APIKeyRateLimit"
                permissions:
                  $ref: "#/components/schemas/APIKeyPermissions"
                auditLogs:
                  type: array
                  description: Audit logs (if requested)
                  items:
                    $ref: "#/components/schemas/APIKeyAuditLog"

    APIKeyRateLimit:
      type: object
      properties:
        requestsPerSecond:
          type: integer
          minimum: 1
          example: 10
        requestsPerMinute:
          type: integer
          minimum: 1
          example: 100
        requestsPerHour:
          type: integer
          minimum: 1
          example: 1000
        requestsPerDay:
          type: integer
          minimum: 1
          example: 10000

    APIKeyPermissions:
      type: object
      properties:
        allowedEndpoints:
          type: array
          description: List of allowed endpoint patterns
          items:
            type: string
          example: ["/api/v1/payments/*", "/api/v1/receivers/*"]
        allowedMethods:
          type: array
          description: List of allowed HTTP methods
          items:
            type: string
            enum: [GET, POST, PUT, DELETE, PATCH]
          example: ["GET", "POST"]
        ipWhitelist:
          type: array
          description: List of whitelisted IP addresses
          items:
            type: string
            format: ipv4
          example: ["***********", "10.0.0.0/8"]
        customPermissions:
          type: object
          description: Custom permissions specific to your use case
          additionalProperties: true

    APIKeyAuditLog:
      type: object
      required:
        - action
        - timestamp
      properties:
        action:
          type: string
          enum: [CREATED, REVOKED, UPDATED, ACCESSED, FAILED_VALIDATION, RATE_LIMITED, EXPIRED]
          example: "ACCESSED"
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T11:30:00Z"
        userId:
          type: string
          description: User who performed the action
          example: "user_2abc123def456"
        userEmail:
          type: string
          format: email
          example: "<EMAIL>"
        metadata:
          type: object
          properties:
            ip:
              type: string
              format: ipv4
              example: "***********00"
            userAgent:
              type: string
              example: "Mozilla/5.0..."
            endpoint:
              type: string
              example: "/api/v1/payments/create"
            method:
              type: string
              example: "POST"
            statusCode:
              type: integer
              example: 200
            errorMessage:
              type: string
              example: "Invalid API key format"

    ErrorResponse:
      type: object
      required:
        - error
      properties:
        error:
          type: string
          description: Error message
          example: "Invalid request parameters"
        errorCode:
          type: string
          description: Machine-readable error code
          example: "INVALID_REQUEST"
        details:
          type: object
          description: Additional error details
          additionalProperties: true

  # ================================================
  # COMMON RESPONSES
  # ================================================

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            error: "Invalid request parameters"
            errorCode: "INVALID_REQUEST"

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            error: "Authentication required"
            errorCode: "UNAUTHORIZED"

    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            error: "Insufficient permissions"
            errorCode: "FORBIDDEN"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            error: "API key not found"
            errorCode: "NOT_FOUND"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          example:
            error: "Internal server error"
            errorCode: "INTERNAL_ERROR" 