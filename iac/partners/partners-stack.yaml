AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: CLKK Partner API Key Management System

Parameters:
  Environment:
    Type: String
    Default: dev
    Description: The deployment environment
    
  LayerCommons:
    Type: String
    Description: ARN of the Commons Layer
    
  TableName:
    Type: String
    Description: Name of the DynamoDB table
    
  ClerkAuthorizerArn:
    Type: String
    Description: ARN of the Clerk Authorizer Lambda function

Globals:
  Function:
    Runtime: nodejs18.x
    Architectures:
      - arm64
    MemorySize: 512
    Timeout: 30
    Tracing: Active
    Environment:
      Variables:
        TABLE_NAME: !Ref TableName
        ENVIRONMENT: !Ref Environment
        NODE_OPTIONS: --enable-source-maps
    Layers:
      - !Ref LayerCommons

Resources:
  # ================================================
  # API KEY MANAGEMENT LAMBDA FUNCTIONS
  # ================================================

  GenerateAPIKeyFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "${Environment}-clkk-generate-api-key"
      Description: Generate new API keys for partners
      CodeUri: ../../lambdas/api-keys/generate-key/
      Handler: index.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref TableName
        - Statement:
          - Effect: Allow
            Action:
              - dynamodb:TransactWriteItems
            Resource:
              - !Sub "arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${TableName}"
    DeletionPolicy: Delete
    UpdateReplacePolicy: Delete

  ListAPIKeysFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "${Environment}-clkk-list-api-keys"
      Description: List API keys for an organization
      CodeUri: ../../lambdas/api-keys/list-keys/
      Handler: index.handler
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref TableName
    DeletionPolicy: Delete
    UpdateReplacePolicy: Delete

  RevokeAPIKeyFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "${Environment}-clkk-revoke-api-key"
      Description: Revoke an API key
      CodeUri: ../../lambdas/api-keys/revoke-key/
      Handler: index.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref TableName
    DeletionPolicy: Delete
    UpdateReplacePolicy: Delete

  GetAPIKeyDetailsFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "${Environment}-clkk-get-api-key-details"
      Description: Get detailed information about an API key
      CodeUri: ../../lambdas/api-keys/get-key-details/
      Handler: index.handler
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref TableName
    DeletionPolicy: Delete
    UpdateReplacePolicy: Delete

  # ================================================
  # CUSTOM API KEY AUTHORIZER
  # ================================================

  APIKeyAuthorizerFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "${Environment}-clkk-api-key-authorizer"
      Description: Custom authorizer for API key authentication
      CodeUri: ../../lambdas/auth/api-key-authorizer/
      Handler: index.handler
      MemorySize: 256
      Timeout: 10
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref TableName
    DeletionPolicy: Delete
    UpdateReplacePolicy: Delete

Outputs:
  GenerateAPIKeyFunctionArn:
    Description: ARN of the Generate API Key function
    Value: !GetAtt GenerateAPIKeyFunction.Arn
    Export:
      Name: !Sub "${Environment}-generate-api-key-function-arn"

  ListAPIKeysFunctionArn:
    Description: ARN of the List API Keys function
    Value: !GetAtt ListAPIKeysFunction.Arn
    Export:
      Name: !Sub "${Environment}-list-api-keys-function-arn"

  RevokeAPIKeyFunctionArn:
    Description: ARN of the Revoke API Key function
    Value: !GetAtt RevokeAPIKeyFunction.Arn
    Export:
      Name: !Sub "${Environment}-revoke-api-key-function-arn"

  GetAPIKeyDetailsFunctionArn:
    Description: ARN of the Get API Key Details function
    Value: !GetAtt GetAPIKeyDetailsFunction.Arn
    Export:
      Name: !Sub "${Environment}-get-api-key-details-function-arn"

  APIKeyAuthorizerFunctionArn:
    Description: ARN of the API Key Authorizer function
    Value: !GetAtt APIKeyAuthorizerFunction.Arn
    Export:
      Name: !Sub "${Environment}-api-key-authorizer-function-arn" 