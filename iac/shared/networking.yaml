AWSTemplateFormatVersion: '2010-09-09'
Description: CLKK Backend - Networking Infrastructure

Parameters:
    Environment:
        Type: String
        Default: dev
        AllowedValues:
            - dev
            - staging
            - prod
Resources:
    # VPC for Lambda functions
    LambdaVPC:
        Type: AWS::EC2::VPC
        Properties:
            CidrBlock: 10.0.0.0/16
            EnableDnsSupport: true
            EnableDnsHostnames: true
            Tags:
                - Key: Name
                  Value: !Sub ${AWS::StackName}-vpc
                - Key: Project
                  Value: CLKK
                - Key: Environment
                  Value: !Ref Environment

    # Public subnet for NAT Gateway
    PublicSubnet1:
        Type: AWS::EC2::Subnet
        Properties:
            VpcId: !Ref LambdaVPC
            AvailabilityZone: !Select
                - 0
                - !GetAZs ''
            CidrBlock: 10.0.0.0/24
            MapPublicIpOnLaunch: true
            Tags:
                - Key: Name
                  Value: !Sub ${AWS::StackName}-public-subnet-1
                - Key: Project
                  Value: CLKK
                - Key: Environment
                  Value: !Ref Environment

    # Private subnet for Lambda functions
    PrivateSubnet1:
        Type: AWS::EC2::Subnet
        Properties:
            VpcId: !Ref Lamb<PERSON>VPC
            AvailabilityZone: !Select
                - 0
                - !GetAZs ''
            CidrBlock: ********/24
            Tags:
                - Key: Name
                  Value: !Sub ${AWS::StackName}-private-subnet-1
                - Key: Project
                  Value: CLKK
                - Key: Environment
                  Value: !Ref Environment

    # Internet Gateway for public subnet
    InternetGateway:
        Type: AWS::EC2::InternetGateway
        Properties:
            Tags:
                - Key: Name
                  Value: !Sub ${AWS::StackName}-igw

    InternetGatewayAttachment:
        Type: AWS::EC2::VPCGatewayAttachment
        Properties:
            VpcId: !Ref LambdaVPC
            InternetGatewayId: !Ref InternetGateway

    # Elastic IP for NAT Gateway (this gives us our static IP)
    NatGatewayEIP:
        Type: AWS::EC2::EIP
        Properties:
            Domain: vpc
            Tags:
                - Key: Name
                  Value: !Sub ${AWS::StackName}-nat-eip

    # NAT Gateway with the Elastic IP
    NatGateway:
        Type: AWS::EC2::NatGateway
        DependsOn: InternetGatewayAttachment
        Properties:
            SubnetId: !Ref PublicSubnet1
            AllocationId: !GetAtt NatGatewayEIP.AllocationId
            Tags:
                - Key: Name
                  Value: !Sub ${AWS::StackName}-nat-gateway
                - Key: Project
                  Value: CLKK
                - Key: Environment
                  Value: !Ref Environment

    # Route table for public subnet
    PublicRouteTable:
        Type: AWS::EC2::RouteTable
        Properties:
            VpcId: !Ref LambdaVPC
            Tags:
                - Key: Name
                  Value: !Sub ${AWS::StackName}-public-route-table
                - Key: Project
                  Value: CLKK
                - Key: Environment
                  Value: !Ref Environment

    PublicRoute:
        Type: AWS::EC2::Route
        DependsOn: InternetGatewayAttachment
        Properties:
            RouteTableId: !Ref PublicRouteTable
            DestinationCidrBlock: 0.0.0.0/0
            GatewayId: !Ref InternetGateway

    PublicSubnet1RouteTableAssociation:
        Type: AWS::EC2::SubnetRouteTableAssociation
        Properties:
            SubnetId: !Ref PublicSubnet1
            RouteTableId: !Ref PublicRouteTable

    # Route table for private subnet
    PrivateRouteTable:
        Type: AWS::EC2::RouteTable
        Properties:
            VpcId: !Ref LambdaVPC
            Tags:
                - Key: Name
                  Value: !Sub ${AWS::StackName}-private-route-table
                - Key: Project
                  Value: CLKK
                - Key: Environment
                  Value: !Ref Environment

    PrivateRoute:
        Type: AWS::EC2::Route
        Properties:
            RouteTableId: !Ref PrivateRouteTable
            DestinationCidrBlock: 0.0.0.0/0
            NatGatewayId: !Ref NatGateway

    PrivateSubnet1RouteTableAssociation:
        Type: AWS::EC2::SubnetRouteTableAssociation
        Properties:
            SubnetId: !Ref PrivateSubnet1
            RouteTableId: !Ref PrivateRouteTable

    # Security group for Lambda functions
    LambdaSecurityGroup:
        Type: AWS::EC2::SecurityGroup
        Properties:
            GroupDescription: Security group for Lambda functions
            VpcId: !Ref LambdaVPC
            SecurityGroupEgress:
                - IpProtocol: -1
                  CidrIp: 0.0.0.0/0
            Tags:
                - Key: Name
                  Value: !Sub ${AWS::StackName}-lambda-sg
                - Key: Project
                  Value: CLKK
                - Key: Environment
                  Value: !Ref Environment

    # VPC Flow Logs Configuration
    VPCFlowLogsLogGroup:
        Type: AWS::Logs::LogGroup
        Properties:
            LogGroupName: !Sub /aws/vpc/flowlogs/${AWS::StackName}
            RetentionInDays: 90
            Tags:
                - Key: Name
                  Value: !Sub ${AWS::StackName}-flowlogs
                - Key: Project
                  Value: CLKK
                - Key: Environment
                  Value: !Ref Environment

    VPCFlowLogsRole:
        Type: AWS::IAM::Role
        Properties:
            AssumeRolePolicyDocument:
                Version: '2012-10-17'
                Statement:
                    - Effect: Allow
                      Principal:
                          Service: vpc-flow-logs.amazonaws.com
                      Action: sts:AssumeRole
            Policies:
                - PolicyName: !Sub ${AWS::StackName}-vpc-flow-logs-policy
                  PolicyDocument:
                      Version: '2012-10-17'
                      Statement:
                          - Effect: Allow
                            Action:
                                - logs:CreateLogGroup
                                - logs:CreateLogStream
                                - logs:PutLogEvents
                                - logs:DescribeLogGroups
                                - logs:DescribeLogStreams
                            Resource: !GetAtt VPCFlowLogsLogGroup.Arn
            Tags:
                - Key: Name
                  Value: !Sub ${AWS::StackName}-flowlogs-role
                - Key: Project
                  Value: CLKK
                - Key: Environment
                  Value: !Ref Environment

    VPCFlowLogs:
        Type: AWS::EC2::FlowLog
        Properties:
            DeliverLogsPermissionArn: !GetAtt VPCFlowLogsRole.Arn
            LogGroupName: !Ref VPCFlowLogsLogGroup
            ResourceId: !Ref LambdaVPC
            ResourceType: VPC
            TrafficType: ALL
            Tags:
                - Key: Name
                  Value: !Sub ${AWS::StackName}-vpc-flowlogs
                - Key: Project
                  Value: CLKK
                - Key: Environment
                  Value: !Ref Environment

Outputs:
    VpcId:
        Description: VPC ID
        Value: !Ref LambdaVPC
        Export:
            Name: !Sub ${AWS::StackName}-VpcId

    PrivateSubnetId:
        Description: Private Subnet ID for Lambda functions
        Value: !Ref PrivateSubnet1
        Export:
            Name: !Sub ${AWS::StackName}-PrivateSubnetId

    LambdaSecurityGroupId:
        Description: Security Group ID for Lambda functions
        Value: !Ref LambdaSecurityGroup
        Export:
            Name: !Sub ${AWS::StackName}-LambdaSecurityGroupId

    NatGatewayEIP:
        Description: Elastic IP address for NAT Gateway (Static IP for outbound requests)
        Value: !Ref NatGatewayEIP
        Export:
            Name: !Sub ${AWS::StackName}-NatGatewayEIP

    VPCFlowLogsLogGroupName:
        Description: CloudWatch Log Group for VPC Flow Logs
        Value: !Ref VPCFlowLogsLogGroup
        Export:
            Name: !Sub ${AWS::StackName}-VPCFlowLogsLogGroupName

    StackName:
        Description: Stack Name
        Value: !Ref AWS::StackName
        Export:
            Name: !Sub ${AWS::StackName}
