AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: >
  Shared infrastructure stack for CLKK Payment System, including Commons Layer, Authorizer, and core application functions.

Parameters:
  ClerkJwtPublicKeySsmParamName:
    Type: String
    Description: The SSM Parameter Store name for the Clerk JWT Public Key.
    Default: /clkk/clerk/jwt-public-key
  DynamoDbTableName:
    Type: String
    Description: The name of the DynamoDB table for application data.
  Environment:
    Type: String
    AllowedValues: [dev, staging, prod]
    Default: dev
  ClerkJwtPublicKeySecretName:
    Type: String
    Description: Name of the Clerk JWT public key secret in Secrets Manager

Resources:
  LayerCommons:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: "commons"
      ContentUri: ../../layers/commons/
      Description: Shared dependencies for all Lambda functions
      CompatibleRuntimes: [nodejs18.x]
      CompatibleArchitectures: [arm64]
      RetentionPolicy: delete
    Metadata:
      BuildMethod: makefile

  ClerkAuthorizerFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${Environment}-ClerkAuthorizerFunction
      Description: Custom authorizer to validate Clerk JWT tokens
      CodeUri: ../../lambdas/auth/clerk-authorizer/
      Handler: index.handler
      Runtime: nodejs18.x
      Architectures:
        - arm64
      MemorySize: 1024
      Timeout: 30
      Environment:
        Variables:
          ENVIRONMENT: !Ref Environment
          POWERTOOLS_SERVICE_NAME: clerk-authorizer
          LOG_LEVEL: INFO
          PARAMETERS_SECRETS_EXTENSION_CACHE_ENABLED: true
          PARAMETERS_SECRETS_EXTENSION_CACHE_SIZE: 1000
          SECRETS_MANAGER_TTL: 300
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - secretsmanager:GetSecretValue
              Resource: !Sub "arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${Environment}/clkk/clerk/jwt-public-key*"
      Layers:
        - !Ref LayerCommons
        - !Sub "arn:aws:lambda:${AWS::Region}:************:layer:AWS-Parameters-and-Secrets-Lambda-Extension-Arm64:11"
    Metadata:
      BuildMethod: esbuild
      BuildProperties:
        External:
          - commons
        Minify: true
        Target: "es2020"
        Sourcemap: true
        EntryPoints:
          - index.ts

  SubmitOrganizationApplicationFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${Environment}-SubmitOrganizationApplicationFunction
      CodeUri: ../../lambdas/applications/submit-application/
      Handler: index.handler
      Runtime: nodejs18.x
      Architectures:
        - arm64
      MemorySize: 256
      Timeout: 30
      Environment:
        Variables:
          TABLE_NAME: !Ref DynamoDbTableName
          POWERTOOLS_SERVICE_NAME: submit-application
          LOG_LEVEL: INFO
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - dynamodb:GetItem
                - dynamodb:PutItem
                - dynamodb:UpdateItem
                - dynamodb:DeleteItem
                - dynamodb:Query
                - dynamodb:Scan
                - dynamodb:BatchGetItem
                - dynamodb:BatchWriteItem
                - dynamodb:DescribeTable
              Resource:
                - !Sub arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${DynamoDbTableName}
                - !Sub arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${DynamoDbTableName}/index/*
      Layers:
        - !Ref LayerCommons
    Metadata:
      BuildMethod: esbuild
      BuildProperties:
        External:
          - commons
        Minify: true
        Target: "es2020"
        Sourcemap: true
        EntryPoints:
          - index.ts

  CreateOrganizationFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${Environment}-CreateOrganizationFunction
      CodeUri: ../../lambdas/organizations/create-organization/
      Handler: index.handler
      Runtime: nodejs18.x
      Architectures:
        - arm64
      MemorySize: 256
      Timeout: 30
      Environment:
        Variables:
          TABLE_NAME: !Ref DynamoDbTableName
          POWERTOOLS_SERVICE_NAME: create-organization
          LOG_LEVEL: INFO
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref DynamoDbTableName
      Layers:
        - !Ref LayerCommons
    Metadata:
      BuildMethod: esbuild
      BuildProperties:
        External:
          - commons
        Minify: true
        Target: "es2020"
        Sourcemap: true
        EntryPoints:
          - index.ts

  ApproveApplicationFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${Environment}-ApproveApplicationFunction
      CodeUri: ../../lambdas/applications/approve-application/
      Handler: index.handler
      Runtime: nodejs18.x
      Architectures:
        - arm64
      MemorySize: 256
      Timeout: 30
      Environment:
        Variables:
          TABLE_NAME: !Ref DynamoDbTableName
          POWERTOOLS_SERVICE_NAME: approve-application
          LOG_LEVEL: INFO
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref DynamoDbTableName
      Layers:
        - !Ref LayerCommons
    Metadata:
      BuildMethod: esbuild
      BuildProperties:
        External:
          - commons
        Minify: true
        Target: "es2020"
        Sourcemap: true
        EntryPoints:
          - index.ts

  

  ValidateUsernameFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${Environment}-ValidateUsernameFunction
      CodeUri: ../../lambdas/users/validate-username/
      Handler: index.handler
      Runtime: nodejs18.x
      Architectures:
        - arm64
      MemorySize: 256
      Timeout: 30
      Environment:
        Variables:
          TABLE_NAME: !Ref DynamoDbTableName
          POWERTOOLS_SERVICE_NAME: validate-username
          LOG_LEVEL: INFO
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - dynamodb:GetItem
                - dynamodb:Query
                - dynamodb:Scan
              Resource:
                - !Sub arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${DynamoDbTableName}
                - !Sub arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${DynamoDbTableName}/index/*
      Layers:
        - !Ref LayerCommons
    Metadata:
      BuildMethod: esbuild
      BuildProperties:
        External:
          - commons
        Minify: true
        Target: "es2020"
        Sourcemap: true
        EntryPoints:
          - index.ts

Outputs:
  LayerCommons:
    Value: !Ref LayerCommons
    Export:
      Name: !Sub "${Environment}-LayerCommons"

  ClerkAuthorizerArn:
    Description: "ARN of the Clerk Authorizer Lambda Function"
    Value: !GetAtt ClerkAuthorizerFunction.Arn
    Export:
      Name: !Sub "${Environment}-ClerkAuthorizerArn"

  SubmitOrganizationApplicationFunctionArn:
    Description: "Submit Organization Application Lambda Function ARN"
    Value: !GetAtt SubmitOrganizationApplicationFunction.Arn
    Export:
      Name: !Sub "${Environment}-SubmitOrgAppFnArn"

  CreateOrganizationFunctionArn:
    Description: "Create Organization Lambda Function ARN"
    Value: !GetAtt CreateOrganizationFunction.Arn
    Export:
      Name: !Sub "${Environment}-CreateOrgFnArn"

  ApproveApplicationFunctionArn:
    Description: "Approve Application Lambda Function ARN"
    Value: !GetAtt ApproveApplicationFunction.Arn
    Export:
      Name: !Sub "${Environment}-clkk-ApproveAppFnArn"


  ValidateUsernameFunctionArn:
    Description: "Validate Username Lambda Function ARN"
    Value: !GetAtt ValidateUsernameFunction.Arn
    Export:
      Name: !Sub "${Environment}-ValidateUsernameFnArn"
