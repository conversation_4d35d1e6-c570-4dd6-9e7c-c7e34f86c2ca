AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: >
  CLKK Payment System DynamoDB Resources
  Single-table design for payment system with multi-tenant architecture

Parameters:
  Environment:
    Type: String
    Description: The deployment stage (e.g., Prod, Dev) used for resource naming and tagging.
    Default: dev

Resources:
  CLKKPaymentTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    UpdateReplacePolicy: Retain
    Properties:
      TableName: !Sub ${Environment}-CLKKPaymentTable
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        # Primary Table Keys
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
        
        # GSI1: Status Lookup Index (Payments by organization and status)
        - AttributeName: StatusLookupPK
          AttributeType: S
        - AttributeName: StatusLookupSK
          AttributeType: S
        
        # GSI2: Date Lookup Index (Payments by organization and date range)
        - AttributeName: DateLookupPK
          AttributeType: S
        - AttributeName: DateLookupSK
          AttributeType: S
        
        # GSI3: User Lookup Index (Payments by user within organization)
        - AttributeName: UserLookupPK
          AttributeType: S
        - AttributeName: UserLookupSK
          AttributeType: S
        
        # GSI4: Reference Lookup Index (Payment by reference within organization)
        - AttributeName: ReferenceLookupPK
          AttributeType: S
        - AttributeName: ReferenceLookupSK
          AttributeType: S
        
        # GSI5: Email Lookup Index (Business receivers by email, User receivers global)
        - AttributeName: EmailLookupPK
          AttributeType: S
        - AttributeName: EmailLookupSK
          AttributeType: S
        
        # GSI6: External ID Lookup Index (Global lookup for receivers)
        - AttributeName: ExternalIdLookupPK
          AttributeType: S
        - AttributeName: ExternalIdLookupSK
          AttributeType: S
        
        
        # GSI10: Provider Customer Lookup Index (Customer by provider customer ID)
        - AttributeName: ProviderCustomerLookupPK
          AttributeType: S
        - AttributeName: ProviderCustomerLookupSK
          AttributeType: S

        # GSI11: Provider Lookup Index (Transactions by provider)
        - AttributeName: ProviderLookupPK
          AttributeType: S
        - AttributeName: ProviderLookupSK
          AttributeType: S

        # GSI12: User Type Lookup Index (Users by type)
        - AttributeName: UserTypeLookupPK
          AttributeType: S
        - AttributeName: UserTypeLookupSK
          AttributeType: S

        # GSI13: Username Lookup Index (User by username)
        - AttributeName: UsernameLookupPK
          AttributeType: S
        - AttributeName: UsernameLookupSK
          AttributeType: S

        # GSI14: Organization User Lookup Index (Users within organizations)
        - AttributeName: OrganizationLookupPK
          AttributeType: S
        - AttributeName: OrganizationLookupSK
          AttributeType: S

        # GSI15: Organization Type Lookup Index (Organizations by type)
        - AttributeName: TypeLookupPK
          AttributeType: S
        - AttributeName: TypeLookupSK
          AttributeType: S

        # GSI17: Organization Status Lookup Index (Organizations by status)
        - AttributeName: OrganizationStatusLookupPK
          AttributeType: S
        - AttributeName: OrganizationStatusLookupSK
          AttributeType: S

        # GSI18: Application Type Lookup Index (Applications by type)
        - AttributeName: ApplicationTypeLookupPK
          AttributeType: S
        - AttributeName: ApplicationTypeLookupSK
          AttributeType: S

        # GSI19: Application Email Lookup Index (Applications by email)
        - AttributeName: ApplicationEmailLookupPK
          AttributeType: S
        - AttributeName: ApplicationEmailLookupSK
          AttributeType: S

        # GSI20: User KYC Lookup Index (KYC verifications by user)
        - AttributeName: UserKycLookupPK
          AttributeType: S
        - AttributeName: UserKycLookupSK
          AttributeType: S

      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE

      GlobalSecondaryIndexes:
        # GSI1: Status-based payment queries
        - IndexName: StatusLookupIndex
          KeySchema:
            - AttributeName: StatusLookupPK
              KeyType: HASH
            - AttributeName: StatusLookupSK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

        # GSI2: Date-based payment queries
        - IndexName: DateLookupIndex
          KeySchema:
            - AttributeName: DateLookupPK
              KeyType: HASH
            - AttributeName: DateLookupSK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

        # GSI3: User-based payment queries
        - IndexName: UserLookupIndex
          KeySchema:
            - AttributeName: UserLookupPK
              KeyType: HASH
            - AttributeName: UserLookupSK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

        # GSI4: Payment reference lookup
        - IndexName: ReferenceLookupIndex
          KeySchema:
            - AttributeName: ReferenceLookupPK
              KeyType: HASH
            - AttributeName: ReferenceLookupSK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

        # GSI5: Email-based receiver lookup
        - IndexName: EmailLookupIndex
          KeySchema:
            - AttributeName: EmailLookupPK
              KeyType: HASH
            - AttributeName: EmailLookupSK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

        # GSI10: Provider customer lookup
        - IndexName: ProviderCustomerLookupIndex
          KeySchema:
            - AttributeName: ProviderCustomerLookupPK
              KeyType: HASH
            - AttributeName: ProviderCustomerLookupSK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

        # GSI11: Provider lookup
        - IndexName: ProviderLookupIndex
          KeySchema:
            - AttributeName: ProviderLookupPK
              KeyType: HASH
            - AttributeName: ProviderLookupSK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

        # GSI12: User type lookup
        - IndexName: UserTypeLookupIndex
          KeySchema:
            - AttributeName: UserTypeLookupPK
              KeyType: HASH
            - AttributeName: UserTypeLookupSK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

        # GSI13: Username lookup
        - IndexName: UsernameLookupIndex
          KeySchema:
            - AttributeName: UsernameLookupPK
              KeyType: HASH
            - AttributeName: UsernameLookupSK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

        # GSI14: Organization user lookup
        - IndexName: OrganizationLookupIndex
          KeySchema:
            - AttributeName: OrganizationLookupPK
              KeyType: HASH
            - AttributeName: OrganizationLookupSK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

        # GSI15: Organization type lookup
        - IndexName: TypeLookupIndex
          KeySchema:
            - AttributeName: TypeLookupPK
              KeyType: HASH
            - AttributeName: TypeLookupSK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL


        # GSI17: Organization status lookup
        - IndexName: OrganizationStatusLookupIndex
          KeySchema:
            - AttributeName: OrganizationStatusLookupPK
              KeyType: HASH
            - AttributeName: OrganizationStatusLookupSK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

        # GSI18: Application type lookup
        - IndexName: ApplicationTypeLookupIndex
          KeySchema:
            - AttributeName: ApplicationTypeLookupPK
              KeyType: HASH
            - AttributeName: ApplicationTypeLookupSK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

        # GSI19: Application email lookup
        - IndexName: ApplicationEmailLookupIndex
          KeySchema:
            - AttributeName: ApplicationEmailLookupPK
              KeyType: HASH
            - AttributeName: ApplicationEmailLookupSK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

        # GSI20: User KYC lookup
        - IndexName: UserKycLookupIndex
          KeySchema:
            - AttributeName: UserKycLookupPK
              KeyType: HASH
            - AttributeName: UserKycLookupSK
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
        
        - IndexName: ExternalIdLookupIndex
          KeySchema:
            - AttributeName: ExternalIdLookupPK # This must match AttributeDefinitions
              KeyType: HASH
            - AttributeName: ExternalIdLookupSK # This must match AttributeDefinitions
              KeyType: RANGE
          Projection:
            ProjectionType: ALL

        

      TimeToLiveSpecification:
        AttributeName: TTL
        Enabled: true
      
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES

      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Application
          Value: CLKK-Payment-System

  # Parameter to store the table name for other resources to reference
  CLKKPaymentTableNameParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub /${Environment}/CLKK/DynamoDB/TableName
      Type: String
      Value: !Ref CLKKPaymentTable
      Description: Name of CLKK Payment System DynamoDB Table for the specific stage

Outputs:
  TableName:
    Description: Name of the CLKK Payment System DynamoDB table
    Value: !Ref CLKKPaymentTable
    Export:
      Name: !Sub ${AWS::StackName}-${Environment}-CLKKPaymentTableName

  TableArn:
    Description: ARN of the CLKK Payment System DynamoDB table
    Value: !GetAtt CLKKPaymentTable.Arn
    Export:
      Name: !Sub ${AWS::StackName}-${Environment}-CLKKPaymentTableArn

  TableStreamArn:
    Description: Stream ARN of the CLKK Payment System DynamoDB table
    Value: !GetAtt CLKKPaymentTable.StreamArn
    Export:
      Name: !Sub ${AWS::StackName}-${Environment}-CLKKPaymentTableStreamArn
