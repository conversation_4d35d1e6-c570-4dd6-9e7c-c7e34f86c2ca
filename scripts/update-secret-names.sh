#!/bin/bash

# Script to help deploy with environment-specific secret names
# This script generates the correct secret name parameters for different environments

set -e

# Default values
ENVIRONMENT="dev"
REGION=""
PROFILE=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
usage() {
    echo "Usage: $0 [OPTIONS] [COMMAND]"
    echo ""
    echo "Deploy CLKK SAAS Backend with environment-specific secret names"
    echo ""
    echo "Commands:"
    echo "  deploy                   Deploy the stack with environment-specific secret names"
    echo "  generate-params          Generate parameter overrides for the environment"
    echo "  validate                 Validate that secrets exist for the environment"
    echo ""
    echo "Options:"
    echo "  -e, --environment ENV    Environment (dev, staging, prod) [default: dev]"
    echo "  -r, --region REGION      AWS region [default: from AWS config]"
    echo "  -p, --profile PROFILE    AWS profile [default: default]"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 deploy -e prod -r us-east-1"
    echo "  $0 generate-params --environment staging"
    echo "  $0 validate -e dev"
}

# Parse command line arguments
COMMAND=""
while [[ $# -gt 0 ]]; do
    case $1 in
        deploy|generate-params|validate)
            COMMAND="$1"
            shift
            ;;
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -r|--region)
            REGION="$2"
            shift 2
            ;;
        -p|--profile)
            PROFILE="$2"
            shift 2
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Default command
if [[ -z "$COMMAND" ]]; then
    COMMAND="deploy"
fi

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    print_error "Invalid environment: $ENVIRONMENT. Must be dev, staging, or prod."
    exit 1
fi

# Set AWS CLI options
AWS_CLI_OPTS=""
if [[ -n "$PROFILE" ]]; then
    AWS_CLI_OPTS="$AWS_CLI_OPTS --profile $PROFILE"
fi
if [[ -n "$REGION" ]]; then
    AWS_CLI_OPTS="$AWS_CLI_OPTS --region $REGION"
fi

# Generate secret names for the environment
generate_secret_names() {
    local env="$1"
    
    echo "ClerkWebhookSecretName=${env}/clkk/clerk/webhook-secret"
    echo "ClerkJwtPublicKeySecretName=${env}/clkk/clerk/jwt-public-key"
    echo "ProveApiCredentialsSecretName=${env}/clkk/prove/api-credentials"
    echo "PocketKnightsApiSecretName=${env}/clkk/pocketknights/api-credentials"
}

# Generate parameter overrides
generate_parameter_overrides() {
    local env="$1"
    
    cat << EOF
--parameter-overrides \\
  Environment=${env} \\
  ClerkWebhookSecretName=${env}/clkk/clerk/webhook-secret \\
  ClerkJwtPublicKeySecretName=${env}/clkk/clerk/jwt-public-key \\
  ProveApiCredentialsSecretName=${env}/clkk/prove/api-credentials \\
  PocketKnightsApiSecretName=${env}/clkk/pocketknights/api-credentials
EOF
}

# Validate that secrets exist
validate_secrets() {
    local env="$1"
    local all_exist=true
    
    secrets=(
        "${env}/clkk/clerk/webhook-secret"
        "${env}/clkk/clerk/jwt-public-key"
        "${env}/clkk/prove/api-credentials"
        "${env}/clkk/pocketknights/api-credentials"
    )
    
    print_info "Validating secrets for environment: $env"
    
    for secret in "${secrets[@]}"; do
        if aws secretsmanager describe-secret --secret-id "$secret" $AWS_CLI_OPTS >/dev/null 2>&1; then
            print_success "✓ $secret exists"
        else
            print_error "✗ $secret does not exist"
            all_exist=false
        fi
    done
    
    if [[ "$all_exist" == "true" ]]; then
        print_success "All secrets exist for environment: $env"
        return 0
    else
        print_error "Some secrets are missing for environment: $env"
        print_info "You can create missing secrets using the update-secrets.sh script"
        return 1
    fi
}

# Deploy the stack
deploy_stack() {
    local env="$1"
    
    print_info "Deploying CLKK SAAS Backend for environment: $env"
    
    # Check if AWS CLI is available
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed or not in PATH"
        exit 1
    fi
    
    # Check if SAM CLI is available
    if ! command -v sam &> /dev/null; then
        print_error "SAM CLI is not installed or not in PATH"
        exit 1
    fi
    
    # Test AWS credentials
    if ! aws sts get-caller-identity $AWS_CLI_OPTS >/dev/null 2>&1; then
        print_error "AWS credentials not configured or invalid"
        exit 1
    fi
    
    print_success "AWS credentials verified"
    
    # Validate secrets exist (optional - warn but don't fail)
    if ! validate_secrets "$env"; then
        print_warning "Some secrets are missing. The deployment may fail if Lambda functions try to access them."
        echo -n "Continue with deployment? (y/N): "
        read continue_deploy
        if [[ ! "$continue_deploy" =~ ^[Yy]$ ]]; then
            print_info "Deployment cancelled. Please create the missing secrets first."
            exit 1
        fi
    fi
    
    # Build the project
    print_info "Building SAM application..."
    if ! sam build; then
        print_error "SAM build failed"
        exit 1
    fi
    
    # Deploy with environment-specific parameters
    print_info "Deploying with environment-specific secret names..."
    
    sam deploy \
        --stack-name "clkk-saas-backend-${env}" \
        --parameter-overrides \
            Environment="${env}" \
            ClerkWebhookSecretName="${env}/clkk/clerk/webhook-secret" \
            ClerkJwtPublicKeySecretName="${env}/clkk/clerk/jwt-public-key" \
            ProveApiCredentialsSecretName="${env}/clkk/prove/api-credentials" \
            PocketKnightsApiSecretName="${env}/clkk/pocketknights/api-credentials" \
        --capabilities CAPABILITY_IAM \
        --resolve-s3 \
        $AWS_CLI_OPTS
    
    if [[ $? -eq 0 ]]; then
        print_success "Deployment completed successfully!"
        print_info ""
        print_info "Stack name: clkk-saas-backend-${env}"
        print_info "Environment: ${env}"
        print_info ""
        print_info "Next steps:"
        print_info "1. Update secrets using: ./scripts/update-secrets.sh -e ${env}"
        print_info "2. Test your API endpoints"
        print_info "3. Monitor CloudWatch logs for any issues"
    else
        print_error "Deployment failed"
        exit 1
    fi
}

# Main execution
case "$COMMAND" in
    deploy)
        deploy_stack "$ENVIRONMENT"
        ;;
    generate-params)
        print_info "Parameter overrides for environment: $ENVIRONMENT"
        echo ""
        generate_parameter_overrides "$ENVIRONMENT"
        echo ""
        ;;
    validate)
        validate_secrets "$ENVIRONMENT"
        ;;
    *)
        print_error "Unknown command: $COMMAND"
        usage
        exit 1
        ;;
esac 