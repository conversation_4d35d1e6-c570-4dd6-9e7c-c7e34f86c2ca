#!/bin/bash

# CLKK SaaS Backend - Secrets Setup Script
# This script creates the required secrets in AWS Secrets Manager

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=${ENVIRONMENT:-dev}
AWS_REGION=${AWS_REGION:-us-east-1}

echo -e "${BLUE}🔐 CLKK SaaS Backend - Secrets Setup${NC}"
echo -e "${BLUE}=====================================${NC}"
echo ""

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Function to check if AWS CLI is installed and configured
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi

    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS CLI is not configured or credentials are invalid."
        exit 1
    fi

    print_status "AWS CLI is configured and working"
}

# Function to create or update a secret
create_or_update_secret() {
    local secret_name="$1"
    local secret_value="$2"
    local description="$3"

    print_info "Processing secret: $secret_name"

    # Check if secret exists
    if aws secretsmanager describe-secret --secret-id "$secret_name" --region "$AWS_REGION" &> /dev/null; then
        print_warning "Secret $secret_name already exists. Updating..."
        aws secretsmanager update-secret \
            --secret-id "$secret_name" \
            --secret-string "$secret_value" \
            --region "$AWS_REGION" > /dev/null
        print_status "Updated secret: $secret_name"
    else
        print_info "Creating new secret: $secret_name"
        aws secretsmanager create-secret \
            --name "$secret_name" \
            --description "$description" \
            --secret-string "$secret_value" \
            --region "$AWS_REGION" > /dev/null
        print_status "Created secret: $secret_name"
    fi
}

# Function to prompt for secret value
prompt_for_secret() {
    local prompt="$1"
    local secret_var="$2"

    if [ -z "${!secret_var}" ]; then
        echo -n "$prompt: "
        read -s value
        echo ""
        eval "$secret_var='$value'"
    else
        print_info "Using $secret_var from environment variable"
    fi
}

# Main execution
main() {
    echo "Environment: $ENVIRONMENT"
    echo "AWS Region: $AWS_REGION"
    echo ""

    # Check prerequisites
    check_aws_cli

    # Prompt for required secrets if not provided via environment variables
    print_info "Please provide the following secret values:"
    echo ""

    prompt_for_secret "Clerk Webhook Secret" CLERK_WEBHOOK_SECRET
    prompt_for_secret "Clerk JWT Public Key" CLERK_JWT_PUBLIC_KEY
    prompt_for_secret "Prove API Client ID" PROVE_CLIENT_ID
    prompt_for_secret "Prove API Client Secret" PROVE_CLIENT_SECRET

    # Set default Prove environment if not provided
    PROVE_ENVIRONMENT=${PROVE_ENVIRONMENT:-SANDBOX}

    echo ""
    print_info "Creating secrets in AWS Secrets Manager..."
    echo ""

    # Create Clerk Webhook Secret
    create_or_update_secret \
        "${ENVIRONMENT}/clkk/clerk/webhook-secret" \
        "{\"webhookSecret\":\"$CLERK_WEBHOOK_SECRET\"}" \
        "Clerk webhook secret for verifying webhook signatures"

    # Create Clerk JWT Public Key Secret (as plain text, not JSON)
    create_or_update_secret \
        "${ENVIRONMENT}/clkk/clerk/jwt-public-key" \
        "$CLERK_JWT_PUBLIC_KEY" \
        "Clerk JWT public key for token verification"

    # Create Prove API Credentials Secret
    create_or_update_secret \
        "${ENVIRONMENT}/clkk/prove/api-credentials" \
        "{\"clientId\":\"$PROVE_CLIENT_ID\",\"clientSecret\":\"$PROVE_CLIENT_SECRET\",\"environment\":\"$PROVE_ENVIRONMENT\"}" \
        "Prove API credentials (client ID and secret)"

    echo ""
    print_status "All secrets have been created/updated successfully!"
    echo ""
    print_info "You can now deploy your application using:"
    echo "  sam deploy --parameter-overrides Environment=$ENVIRONMENT"
    echo ""
    print_warning "Note: The secrets contain sensitive information. Make sure to:"
    echo "  - Rotate them regularly"
    echo "  - Monitor access using CloudTrail"
    echo "  - Use least privilege IAM policies"
}

# Help function
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --environment ENVIRONMENT    Set the environment (default: dev)"
    echo "  -r, --region REGION             Set the AWS region (default: us-east-1)"
    echo "  -h, --help                      Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  ENVIRONMENT                     Deployment environment"
    echo "  AWS_REGION                      AWS region"
    echo "  CLERK_WEBHOOK_SECRET           Clerk webhook secret"
    echo "  CLERK_JWT_PUBLIC_KEY           Clerk JWT public key"
    echo "  PROVE_CLIENT_ID                Prove API client ID"
    echo "  PROVE_CLIENT_SECRET            Prove API client secret"
    echo "  PROVE_ENVIRONMENT              Prove environment (SANDBOX/PRODUCTION)"
    echo ""
    echo "Examples:"
    echo "  $0                              # Interactive mode with defaults"
    echo "  $0 -e prod -r us-west-2        # Set environment and region"
    echo "  CLERK_WEBHOOK_SECRET=xxx $0     # Provide secrets via env vars"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -r|--region)
            AWS_REGION="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run main function
main