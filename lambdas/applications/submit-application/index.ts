import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import Jo<PERSON>, { ValidationErrorItem } from "joi";

import {
  ApplicationService,
  SubmitOrgAdminApplicationRequest,
  SubmitAgentApplicationRequest,
  getUserByEmail,
  logger,
} from "commons";
import {
  createSuccessResponse,
  createErrorResponse,
  createBadRequestResponse,
} from "commons";

// --- Input Validation Schemas ---

// Base schema with conditional validation based on applicationType
const applicationSchema = Joi.object({
  applicationType: Joi.string().valid("org_admin", "agent").required(),
  // Fields for org_admin applications
  organizationName: Joi.when('applicationType', {
    is: 'org_admin',
    then: Joi.string().min(2).max(100).required(),
    otherwise: Joi.forbidden()
  }),
  organizationDescription: Joi.when('applicationType', {
    is: 'org_admin',
    then: Joi.string().max(500).optional(),
    otherwise: Joi.forbidden()
  }),
  // Fields for agent applications
  affiliateUsername: Joi.when('applicationType', {
    is: 'agent',
    then: Joi.string().min(3).max(20).pattern(/^[a-zA-Z0-9_]+$/).required(),
    otherwise: Joi.forbidden()
  }),
  // Common optional field
  notes: Joi.string().max(1000).optional(),
}).options({ stripUnknown: true }); // Remove any unknown fields

export const handler = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  const requestId = event.requestContext.requestId;

  logger.info("Received submit application request", {
    path: event.path,
    method: event.httpMethod,
    requestId
  });

  // Validate request body
  if (!event.body) {
    logger.warn("Request body is missing");
    return createBadRequestResponse("Request body is missing");
  }

  let requestBody: any;
  try {
    requestBody = JSON.parse(event.body);
    logger.info("Parsed request body", {
      applicationType: requestBody.applicationType,
      username: requestBody.username
    });
  } catch (error) {
    logger.error("Invalid JSON format in request body", error as Error);
    return createBadRequestResponse("Invalid JSON format");
  }

  // Validate request schema
  const { error: validationError, value: validatedBody } = applicationSchema.validate(requestBody);
  if (validationError) {
    logger.warn("Request body validation failed", {
      details: validationError.details,
    });
    return createBadRequestResponse(
      "Validation failed",
      validationError.details.map((d: ValidationErrorItem) => d.message)
    );
  }

  // Get user context from authorizer
  const applicantClerkUserId = event.requestContext.authorizer?.userId;
  const applicantEmail = event.requestContext.authorizer?.email;

  if (!applicantClerkUserId) {
    logger.error("Applicant Clerk User ID not found in authorizer context");
    return createErrorResponse(500, "Internal server error: User ID missing from context");
  }

  if (!applicantEmail) {
    logger.error("Applicant email not found in authorizer context");
    return createErrorResponse(500, "Internal server error: Email missing from context");
  }

  logger.info("Processing application", {
    applicantClerkUserId,
    applicantEmail: applicantEmail.split('@')[0] + '***',
    applicationType: validatedBody.applicationType
  });

  try {
    // Get the existing authenticated user from the User entity system
    const existingUser = await getUserByEmail(applicantEmail);
    if (!existingUser) {
      logger.error("Authenticated user not found in User entity system", {
        clerkUserId: applicantClerkUserId,
        email: applicantEmail
      });
      return createErrorResponse(500, "User not found in system. Please contact support.");
    }

    let application;

    if (validatedBody.applicationType === "org_admin") {
      // Submit org_admin application
      const orgAdminRequest: SubmitOrgAdminApplicationRequest = {
        organizationName: validatedBody.organizationName,
        organizationDescription: validatedBody.organizationDescription,
        notes: validatedBody.notes,
      };

      application = await ApplicationService.submitOrgAdminApplication(
        existingUser.id,
        applicantClerkUserId,
        applicantEmail,
        orgAdminRequest
      );

      logger.info("Org admin application submitted successfully", {
        applicationId: application.applicationId,
        organizationName: validatedBody.organizationName,
        userId: existingUser.id
      });

    } else if (validatedBody.applicationType === "agent") {
      // Submit agent application
      const agentRequest: SubmitAgentApplicationRequest = {
        affiliateUsername: validatedBody.affiliateUsername,
        notes: validatedBody.notes,
      };

      application = await ApplicationService.submitAgentApplication(
        existingUser.id,
        applicantClerkUserId,
        applicantEmail,
        agentRequest
      );

      logger.info("Agent application submitted successfully", {
        applicationId: application.applicationId,
        affiliateUsername: validatedBody.affiliateUsername,
        userId: existingUser.id
      });

    } else {
      return createBadRequestResponse("Invalid application type");
    }

    // Return success response
    const response = {
      applicationId: application.applicationId,
      applicationType: application.applicationType,
      status: application.status,
      userId: existingUser.id,
      userEmail: existingUser.email,
      ...(application.organizationName && { organizationName: application.organizationName }),
      ...(application.affiliateUsername && { affiliateUsername: application.affiliateUsername }),
      createdAt: application.createdAt,
    };

    return createSuccessResponse(201, response);

  } catch (error) {
    logger.error("Error submitting application", error as Error);

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes("already has a pending application")) {
        return createBadRequestResponse("You already have a pending application");
      }
      if (error.message.includes("already taken")) {
        return createBadRequestResponse(error.message);
      }
      if (error.message.includes("not found")) {
        return createBadRequestResponse(error.message);
      }
    }

    return createErrorResponse(500, "Failed to submit application due to an internal error");
  }
};
