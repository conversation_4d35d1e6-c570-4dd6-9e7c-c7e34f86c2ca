import { SQSEvent } from "aws-lambda";
import {
  <PERSON><PERSON>ebhookEvent,
  isUserCreated<PERSON><PERSON>,
  isUserUpdatedEvent,
  isUserDeletedEvent,
  UserCreatedEvent,
  UserUpdatedEvent,
  UserDeletedEvent,
  logger,
  metrics,
  tracer,
  MetricUnit,
  METRIC_NAMES,
  addMetric,
  User,
  UserStatus,
  UserType,
} from "commons";

/**
 * Gets a user's primary email from Clerk user data
 *
 * @param userData - Clerk user data
 * @returns The primary email or first email in the list, or undefined if no emails exist
 */
function getPrimaryEmail(
  userData: UserCreatedEvent["data"] | UserUpdatedEvent["data"]
): string | undefined {
  if (!userData.email_addresses || userData.email_addresses.length === 0) {
    return undefined;
  }

  // If primary email is set, find that email
  if (userData.primary_email_address_id) {
    const primaryEmail = userData.email_addresses.find(
      (email) => email.id === userData.primary_email_address_id
    );

    if (primaryEmail) {
      return primaryEmail.email_address;
    }
  }

  // Otherwise just return the first email
  return userData.email_addresses[0].email_address;
}

/**
 * Gets a user's primary phone from Clerk user data
 *
 * @param userData - Clerk user data
 * @returns The primary phone or first phone in the list, or undefined if no phones exist
 */
function getPrimaryPhone(
  userData: UserCreatedEvent["data"] | UserUpdatedEvent["data"]
): string | undefined {
  if (!userData.phone_numbers || userData.phone_numbers.length === 0) {
    return undefined;
  }

  // If primary phone is set, find that phone
  if (userData.primary_phone_number_id) {
    const primaryPhone = userData.phone_numbers.find(
      (phone) => phone.id === userData.primary_phone_number_id
    );

    if (primaryPhone) {
      return primaryPhone.phone_number;
    }
  }

  // Otherwise just return the first phone
  return userData.phone_numbers[0].phone_number;
}

/**
 * Determines user role from Clerk metadata
 *
 * @param userData - Clerk user data
 * @returns UserType
 */
function determineUserRole(
  userData: UserCreatedEvent["data"] | UserUpdatedEvent["data"]
): UserType {
  // Valid user roles based on our UserType type
  const validRoles: UserType[] = [
    "super_admin",
    "org_admin",
    "agent",
    "user"
  ];

  // Check public metadata first
  if (userData.public_metadata?.role) {
    const role = userData.public_metadata.role as UserType;
    if (validRoles.includes(role)) {
      return role;
    }
  }

  // Check private metadata
  if (userData.private_metadata?.role) {
    const role = userData.private_metadata.role as UserType;
    if (validRoles.includes(role)) {
      return role;
    }
  }

  // Default role for new users - most users will be basic users
  return "user";
}

/**
 * Gets organization ID from Clerk metadata if available
 *
 * @param userData - Clerk user data
 * @returns Organization ID or undefined
 */
function getOrganizationId(
  userData: UserCreatedEvent["data"] | UserUpdatedEvent["data"]
): string | undefined {
  // Check public metadata first
  if (userData.public_metadata?.organizationId) {
    return userData.public_metadata.organizationId;
  }

  // Check private metadata
  if (userData.private_metadata?.organizationId) {
    return userData.private_metadata.organizationId;
  }

  return undefined;
}

/**
 * Handles user created events from Clerk
 *
 * @param event - The user created event
 * @returns Success indicator
 */
async function handleUserCreated(event: UserCreatedEvent): Promise<boolean> {
  const userData = event.data;
  logger.info("Processing user created event", { userId: userData.id });

  try {
    // Check if the user already exists
    let existingUser: User | null = null;
    
    try {
      existingUser = await User.getById(userData.id);
    } catch (error) {
      // User doesn't exist, which is expected for create events
      logger.debug("User doesn't exist yet, proceeding with creation", { userId: userData.id });
    }

    if (existingUser) {
      logger.info("User already exists, updating instead", { userId: userData.id });
      return handleUserUpdated(event as UserUpdatedEvent);
    }

    // Get primary email (required)
    const email = getPrimaryEmail(userData);
    if (!email) {
      logger.error("User has no email addresses, cannot create user", { userId: userData.id });
      addMetric(METRIC_NAMES.WEBHOOK_FAILED, 1, MetricUnit.Count, { 
        eventType: "user.created", 
        reason: "no_email" 
      });
      return false;
    }

    // Get other user data
    const phone = getPrimaryPhone(userData);
    const role = determineUserRole(userData);
    const organizationId = getOrganizationId(userData);

    // Create User entity - no tenant needed for webhook users
    const user = new User(
      "default", // Keep minimal tenant for BaseEntity compatibility
      userData.id,
      email,
      role,
      "ACTIVE",
      {
        organizationId,
        firstName: userData.first_name,
        lastName: userData.last_name,
        phoneNumber: phone,
        profilePictureUrl: userData.image_url,
      }
    );

    // Save the user
    await user.create();

    logger.info("Successfully created user", { 
      userId: userData.id,
      email: email,
      role: role,
      hasOrganization: !!organizationId,
      organizationId: organizationId
    });
    
    addMetric(METRIC_NAMES.WEBHOOK_PROCESSED, 1, MetricUnit.Count, { eventType: "user.created" });
    return true;
    
  } catch (error) {
    logger.error("Error handling user created event", {
      error,
      userId: userData.id,
    });
    addMetric(METRIC_NAMES.WEBHOOK_FAILED, 1, MetricUnit.Count, { eventType: "user.created" });
    return false;
  }
}

/**
 * Handles user updated events from Clerk
 *
 * @param event - The user updated event
 * @returns Success indicator
 */
async function handleUserUpdated(event: UserUpdatedEvent): Promise<boolean> {
  const userData = event.data;
  logger.info("Processing user updated event", { userId: userData.id });

  try {
    let existingUser: User;
    
    try {
      existingUser = await User.getById(userData.id);
    } catch (error) {
      logger.info("User does not exist, creating instead", { userId: userData.id });
      return handleUserCreated(event as UserCreatedEvent);
    }

    // Get updated data
    const email = getPrimaryEmail(userData);
    if (!email) {
      logger.error("User has no email addresses, cannot update user", { userId: userData.id });
      addMetric(METRIC_NAMES.WEBHOOK_FAILED, 1, MetricUnit.Count, { 
        eventType: "user.updated", 
        reason: "no_email" 
      });
      return false;
        }

    const phone = getPrimaryPhone(userData);
    const role = determineUserRole(userData);
    const organizationId = getOrganizationId(userData);

    // Update user properties
    existingUser.email = email;
    existingUser.firstName = userData.first_name;
    existingUser.lastName = userData.last_name;
    existingUser.phoneNumber = phone;
    existingUser.profilePictureUrl = userData.image_url;
    existingUser.role = role;

    // Handle organization assignment/removal
    if (organizationId && organizationId !== existingUser.organizationId) {
      // User is being assigned to an organization or moved to a different one
      existingUser.organizationId = organizationId;
      logger.info("User assigned to organization", { 
        userId: userData.id, 
        organizationId: organizationId,
        previousOrganizationId: existingUser.organizationId
      });
    } else if (!organizationId && existingUser.organizationId) {
      // User is being removed from organization (made independent)
      existingUser.organizationId = undefined;
      logger.info("User removed from organization", { 
        userId: userData.id, 
        previousOrganizationId: existingUser.organizationId
      });
    }

    // Save the updated user
    await existingUser.update();

    logger.info("Successfully updated user", { 
      userId: userData.id,
      email: email,
      role: role,
      hasOrganization: !!existingUser.organizationId,
      organizationId: existingUser.organizationId
    });
    
    addMetric(METRIC_NAMES.WEBHOOK_PROCESSED, 1, MetricUnit.Count, { eventType: "user.updated" });
    return true;
    
  } catch (error) {
    logger.error("Error handling user updated event", {
      error,
      userId: userData.id,
    });
    addMetric(METRIC_NAMES.WEBHOOK_FAILED, 1, MetricUnit.Count, { eventType: "user.updated" });
    return false;
  }
}

/**
 * Handles user deleted events from Clerk
 *
 * @param event - The user deleted event
 * @returns Success indicator
 */
async function handleUserDeleted(event: UserDeletedEvent): Promise<boolean> {
  const userData = event.data;
  logger.info("Processing user deleted event", { userId: userData.id });

  try {
    let existingUser: User;
    
    try {
      existingUser = await User.getById(userData.id);
      } catch (error) {
      logger.warn("User not found for deletion", { userId: userData.id });
      // Consider this a success since the user is already gone
      addMetric(METRIC_NAMES.WEBHOOK_PROCESSED, 1, MetricUnit.Count, { eventType: "user.deleted" });
      return true;
    }

    // Mark user as inactive instead of deleting (soft delete)
    existingUser.status = "INACTIVE";
    await existingUser.update();

    logger.info("Successfully marked user as INACTIVE", {
      userId: userData.id,
    });
    
    addMetric(METRIC_NAMES.WEBHOOK_PROCESSED, 1, MetricUnit.Count, { eventType: "user.deleted" });
    return true;
    
  } catch (error) {
    logger.error("Error handling user deleted event", {
      error,
      userId: userData.id,
    });
    addMetric(METRIC_NAMES.WEBHOOK_FAILED, 1, MetricUnit.Count, { eventType: "user.deleted" });
    return false;
  }
}

/**
 * Lambda handler for processing user events from SQS
 *
 * @param sqsEvent - The SQS event containing Clerk webhook events
 */
export const handler = async (sqsEvent: SQSEvent): Promise<void> => {
  logger.info(`Processing ${sqsEvent.Records.length} user events`);

  for (const record of sqsEvent.Records) {
    try {
      // Parse the SNS message from the SQS record
      const snsMessage = JSON.parse(record.body);
      
      logger.info("SNS Message received", { 
        messageId: record.messageId,
        snsType: snsMessage.Type,
        topicArn: snsMessage.TopicArn
      });

      // Extract the actual webhook event from the SNS Message field
      let webhookEvent: ClerkWebhookEvent;
      
      if (snsMessage.Type === 'Notification' && snsMessage.Message) {
        // Parse the webhook event from the SNS Message field
        webhookEvent = JSON.parse(snsMessage.Message) as ClerkWebhookEvent;
      } else {
        // Fallback: try to parse the record body directly as webhook event
        webhookEvent = JSON.parse(record.body) as ClerkWebhookEvent;
      }

      logger.info("Processing user event", {
        eventType: webhookEvent.type,
        messageId: record.messageId,
        userId: webhookEvent.data?.id
      });

      // Process based on event type
      let success = false;

      if (isUserCreatedEvent(webhookEvent)) {
        success = await handleUserCreated(webhookEvent);
      } else if (isUserUpdatedEvent(webhookEvent)) {
        success = await handleUserUpdated(webhookEvent);
      } else if (isUserDeletedEvent(webhookEvent)) {
        success = await handleUserDeleted(webhookEvent);
      } else {
        logger.warn("Unsupported user event type", { 
          type: webhookEvent.type,
          availableData: Object.keys(webhookEvent.data || {})
        });
        addMetric("UnsupportedEventType", 1, MetricUnit.Count, { 
          eventType: webhookEvent.type || 'unknown'
        });
      }

      if (success) {
        addMetric("ProcessingSuccess", 1, MetricUnit.Count, { 
          eventType: webhookEvent.type || 'unknown'
        });
      } else {
        addMetric("ProcessingError", 1, MetricUnit.Count, { 
          eventType: webhookEvent.type || 'unknown'
        });
        // Let the error propagate to use SQS retry mechanism
        throw new Error(`Failed to process user event: ${webhookEvent.type}`);
      }
    } catch (error) {
      logger.error("Error processing SQS record", {
        error,
        messageId: record.messageId,
        recordBody: record.body.substring(0, 500) + "..." // Log first 500 chars for debugging
      });
      // Rethrow to trigger SQS retry
      throw error;
    }
  }
};
