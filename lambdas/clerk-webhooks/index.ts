import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { 
  ClerkWebhookService,
  ClerkWebhookEvent,
  logger,
  metrics,
  tracer,
  MetricUnit,
  METRIC_NAMES,
  addMetric,
  secretsManager,
  SECRET_NAMES,
  ClerkWebhookSecretValue,
} from "commons";

// Initialize webhook service with secrets manager
let webhookService: ClerkWebhookService;

async function initializeWebhookService(): Promise<ClerkWebhookService> {
  if (!webhookService) {
    try {
      logger.info('Initializing webhook service with secrets from Secrets Manager');
      
      const secretValue = await secretsManager.getSecret(SECRET_NAMES.CLERK_WEBHOOK_SECRET) as ClerkWebhookSecretValue;
      
      webhookService = new ClerkWebhookService({
        webhookSecret: secretValue.webhookSecret,
        organizationTopicArn: process.env.ORGANIZATION_TOPIC_ARN,
        userTopicArn: process.env.USER_TOPIC_ARN,
        logger,
      });
      
      logger.info('Webhook service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize webhook service', { error });
      throw new Error('Failed to initialize webhook service with secrets');
    }
  }
  
  return webhookService;
}

/**
 * Lambda handler for Clerk webhook events
 *
 * @param event - API Gateway proxy event
 * @returns API Gateway proxy result
 */
export const handler = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    addMetric(METRIC_NAMES.WEBHOOK_RECEIVED, 1, MetricUnit.Count);

    // Initialize webhook service with secrets
    const service = await initializeWebhookService();

    // Ensure we have a body
    if (!event.body) {
      logger.error("No request body");
      return {
        statusCode: 400,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ message: "Missing request body" }),
      };
    }

    // Extract Svix webhook headers
    const svixId = event.headers["svix-id"] || "";
    const svixTimestamp = event.headers["svix-timestamp"] || "";
    const svixSignature = event.headers["svix-signature"] || "";

    // Log all headers for debugging
    logger.info("Webhook headers received", {
      allHeaders: event.headers,
      svixId,
      svixTimestamp,
      svixSignature,
      bodyLength: event.body?.length
    });

    if (!svixId || !svixTimestamp || !svixSignature) {
      logger.error("Missing Svix headers", { headers: event.headers });
      return {
        statusCode: 400,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ message: "Missing Svix webhook headers" }),
      };
    }

    // Log webhook details for debugging (without sensitive data)
    logger.debug("Webhook verification details", {
      svixId,
      svixTimestamp,
      signatureFormat: svixSignature.substring(0, 20) + "...", // Only log first 20 chars
      bodyLength: event.body.length,
      serviceConfigured: service.isConfigured()
    });

    // Verify webhook signature
    const isValid = service.verifyWebhookSignature(
      event.body,
      svixId,
      svixTimestamp,
      svixSignature
    );

    if (!isValid) {
      logger.error("Invalid webhook signature");
      addMetric("InvalidSignature", 1, MetricUnit.Count);
      return {
        statusCode: 401,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ message: "Invalid webhook signature" }),
      };
    }

    // Parse webhook event
    let webhookEvent: ClerkWebhookEvent;
    try {
      webhookEvent = JSON.parse(event.body) as ClerkWebhookEvent;
    } catch (error) {
      logger.error("Failed to parse webhook event", { error });
      addMetric("ParseError", 1, MetricUnit.Count);
      return {
        statusCode: 400,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ message: "Invalid webhook payload" }),
      };
    }

    logger.info("Received webhook event", {
      type: webhookEvent.type,
      timestamp: webhookEvent.timestamp,
    });

    // Add event-specific metrics
    addMetric(
      `Event_${webhookEvent.type.replace(/\./g, "_")}`,
      1,
      MetricUnit.Count,
      { eventType: webhookEvent.type }
    );

    // Process the webhook event
    const success = await service.processWebhookEvent(webhookEvent);

    if (success) {
      logger.info("Successfully processed webhook event", {
        type: webhookEvent.type,
      });
      addMetric(METRIC_NAMES.WEBHOOK_PROCESSED, 1, MetricUnit.Count, { eventType: webhookEvent.type });
      return {
        statusCode: 200,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ message: "Webhook processed successfully" }),
      };
    } else {
      logger.error("Failed to process webhook event", {
        type: webhookEvent.type,
      });
      addMetric(METRIC_NAMES.WEBHOOK_FAILED, 1, MetricUnit.Count, { eventType: webhookEvent.type });
      return {
        statusCode: 500,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ message: "Failed to process webhook event" }),
      };
    }
  } catch (error) {
    logger.error("Unexpected error processing webhook", { error });
    addMetric("UnexpectedError", 1, MetricUnit.Count);
    return {
      statusCode: 500,
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ message: "Internal server error" }),
    };
  }
}; 