import { SQSE<PERSON> } from "aws-lambda";
import {
  ClerkWebhookEvent,
  isOrganizationCreatedEvent,
  isOrganizationUpdatedEvent,
  isOrganizationDeletedEvent,
  isOrganizationMembershipCreatedEvent,
  isOrganizationMembershipUpdatedEvent,
  isOrganizationMembershipDeletedEvent,
  OrganizationCreatedEvent,
  OrganizationUpdatedEvent,
  OrganizationDeletedEvent,
  logger,
  metrics,
  tracer,
  MetricUnit,
  METRIC_NAMES,
  addMetric,
  Organization,
  OrganizationType,
  OrganizationStatus,
} from "commons";

/**
 * Handles organization created events from Clerk
 *
 * @param event - The organization created event
 * @returns Success indicator
 */
async function handleOrganizationCreated(
  event: OrganizationCreatedEvent
): Promise<boolean> {
  const org = event.data;
  logger.info("Processing organization created event", { orgId: org.id });

  try {
    // Use a default tenant ID for now - this should be configurable
    const tenantId = "default";
    
    // Check if the organization already exists in our DB
    let existingOrg: Organization | null = null;
    try {
      existingOrg = await Organization.getById(tenantId, org.id);
    } catch (error) {
      // Organization doesn't exist, which is expected for create events
      logger.debug("Organization doesn't exist yet, proceeding with creation", { orgId: org.id });
    }

    if (existingOrg) {
      logger.info("Organization already exists, updating instead", {
        orgId: org.id,
      });
      return handleOrganizationUpdated(event as OrganizationUpdatedEvent);
    }

    // Determine organization type from public metadata or default to SERVICE_PROVIDER
    const orgType: OrganizationType = (org.public_metadata?.type as OrganizationType) || "SERVICE_PROVIDER";
    
    // Validate organization type
    if (!["AUTHORITY", "SERVICE_PROVIDER", "PROPERTY_MANAGEMENT"].includes(orgType)) {
      logger.warn("Invalid organization type in metadata, defaulting to SERVICE_PROVIDER", {
        orgId: org.id,
        providedType: org.public_metadata?.type
      });
    }

    // Create new Organization entity
    const organization = new Organization(
      tenantId,
      org.id, // Use Clerk organization ID as our ID
      org.name,
      orgType,
      "ACTIVE" as OrganizationStatus,
      {
        description: org.public_metadata?.description,
        logoUrl: org.logo_url || org.image_url,
        website: org.public_metadata?.website,
        contactEmail: org.public_metadata?.contactEmail,
        contactPhone: org.public_metadata?.contactPhone,
        address: org.public_metadata?.address,
        licenseNumber: org.public_metadata?.licenseNumber,
        additionalDetails: org.public_metadata?.additionalDetails,
        contactInfo: org.public_metadata?.contactInfo ? {
          name: org.public_metadata.contactInfo.name,
          email: org.public_metadata.contactInfo.email,
          phone: org.public_metadata.contactInfo.phone,
          address: org.public_metadata.contactInfo.address,
        } : undefined,
      }
    );

    // Store additional Clerk-specific metadata
    const clerkMetadata = {
      clerkId: org.id,
      slug: org.slug,
      createdBy: org.created_by,
      clerkCreatedAt: new Date(org.created_at).toISOString(),
      clerkUpdatedAt: new Date(org.updated_at).toISOString(),
      publicMetadata: org.public_metadata,
    };

    // Add Clerk metadata to the organization's additional details
    if (organization.additionalDetails) {
      organization.additionalDetails = JSON.stringify({
        ...JSON.parse(organization.additionalDetails || "{}"),
        clerk: clerkMetadata
      });
    } else {
      organization.additionalDetails = JSON.stringify({ clerk: clerkMetadata });
    }

    // Save the organization using the entity's create method
    await organization.create();

    logger.info("Successfully stored organization in DynamoDB", {
      orgId: org.id,
      tenantId,
      organizationType: orgType,
    });
    addMetric(METRIC_NAMES.WEBHOOK_PROCESSED, 1, MetricUnit.Count, { eventType: "organization.created" });
    return true;
  } catch (error) {
    logger.error("Error handling organization created event", {
      error,
      orgId: org.id,
    });
    addMetric(METRIC_NAMES.WEBHOOK_FAILED, 1, MetricUnit.Count, { eventType: "organization.created" });
    return false;
  }
}

/**
 * Handles organization updated events from Clerk
 *
 * @param event - The organization updated event
 * @returns Success indicator
 */
async function handleOrganizationUpdated(
  event: OrganizationUpdatedEvent
): Promise<boolean> {
  const org = event.data;
  logger.info("Processing organization updated event", { orgId: org.id });

  try {
    const tenantId = "default";
    
    // Get existing organization
    let existingOrg: Organization;
    try {
      existingOrg = await Organization.getById(tenantId, org.id);
    } catch (error) {
      logger.info("Organization does not exist, creating instead", {
        orgId: org.id,
      });
      return handleOrganizationCreated(event as OrganizationCreatedEvent);
    }

    // Update organization fields
    existingOrg.name = org.name;
    
    // Update organization type if provided in metadata
    const orgType: OrganizationType = (org.public_metadata?.type as OrganizationType) || existingOrg.type;
    if (["AUTHORITY", "SERVICE_PROVIDER", "PROPERTY_MANAGEMENT"].includes(orgType)) {
      existingOrg.type = orgType;
    }

    // Update optional fields
    if (org.public_metadata?.description !== undefined) {
      existingOrg.description = org.public_metadata.description;
    }
    if (org.logo_url || org.image_url) {
      existingOrg.logoUrl = org.logo_url || org.image_url;
    }
    if (org.public_metadata?.website !== undefined) {
      existingOrg.website = org.public_metadata.website;
    }
    if (org.public_metadata?.contactEmail !== undefined) {
      existingOrg.contactEmail = org.public_metadata.contactEmail;
    }
    if (org.public_metadata?.contactPhone !== undefined) {
      existingOrg.contactPhone = org.public_metadata.contactPhone;
    }
    if (org.public_metadata?.address !== undefined) {
      existingOrg.address = org.public_metadata.address;
    }
    if (org.public_metadata?.licenseNumber !== undefined) {
      existingOrg.licenseNumber = org.public_metadata.licenseNumber;
    }

    // Update contact info if provided
    if (org.public_metadata?.contactInfo) {
      existingOrg.contactInfo = {
        name: org.public_metadata.contactInfo.name,
        email: org.public_metadata.contactInfo.email,
        phone: org.public_metadata.contactInfo.phone,
        address: org.public_metadata.contactInfo.address,
      };
    }

    // Update Clerk-specific metadata
    const clerkMetadata = {
      clerkId: org.id,
      slug: org.slug,
      createdBy: org.created_by,
      clerkCreatedAt: new Date(org.created_at).toISOString(),
      clerkUpdatedAt: new Date(org.updated_at).toISOString(),
      publicMetadata: org.public_metadata,
    };

    // Parse existing additional details and update Clerk metadata
    let additionalDetails: any = {};
    if (existingOrg.additionalDetails) {
      try {
        additionalDetails = JSON.parse(existingOrg.additionalDetails);
      } catch (error) {
        logger.warn("Failed to parse existing additional details", { orgId: org.id });
      }
    }
    
    additionalDetails.clerk = clerkMetadata;
    existingOrg.additionalDetails = JSON.stringify(additionalDetails);

    // Save the updated organization
    await existingOrg.update();

    logger.info("Successfully updated organization in DynamoDB", {
      orgId: org.id,
      tenantId,
    });
    addMetric(METRIC_NAMES.WEBHOOK_PROCESSED, 1, MetricUnit.Count, { eventType: "organization.updated" });
    return true;
  } catch (error) {
    logger.error("Error handling organization updated event", {
      error,
      orgId: org.id,
    });
    addMetric(METRIC_NAMES.WEBHOOK_FAILED, 1, MetricUnit.Count, { eventType: "organization.updated" });
    return false;
  }
}

/**
 * Handles organization deleted events from Clerk
 *
 * @param event - The organization deleted event
 * @returns Success indicator
 */
async function handleOrganizationDeleted(
  event: OrganizationDeletedEvent
): Promise<boolean> {
  const org = event.data;
  logger.info("Processing organization deleted event", { orgId: org.id });

  try {
    const tenantId = "default";
    
    // Get existing organization
    let existingOrg: Organization;
    try {
      existingOrg = await Organization.getById(tenantId, org.id);
    } catch (error) {
      logger.warn("Organization not found for deletion", { orgId: org.id });
      // Consider this a success since the organization is already gone
      return true;
    }

    // Mark organization as inactive instead of deleting
    existingOrg.status = "INACTIVE";
    
    // Update additional details to mark as deleted
    let additionalDetails: any = {};
    if (existingOrg.additionalDetails) {
      try {
        additionalDetails = JSON.parse(existingOrg.additionalDetails);
      } catch (error) {
        logger.warn("Failed to parse existing additional details", { orgId: org.id });
      }
    }
    
    additionalDetails.deletedAt = new Date().toISOString();
    additionalDetails.deletedFromClerk = true;
    existingOrg.additionalDetails = JSON.stringify(additionalDetails);

    // Save the updated organization
    await existingOrg.update();

    logger.info("Successfully marked organization as INACTIVE in DynamoDB", {
      orgId: org.id,
      tenantId,
    });
    addMetric(METRIC_NAMES.WEBHOOK_PROCESSED, 1, MetricUnit.Count, { eventType: "organization.deleted" });
    return true;
  } catch (error) {
    logger.error("Error handling organization deleted event", {
      error,
      orgId: org.id,
    });
    addMetric(METRIC_NAMES.WEBHOOK_FAILED, 1, MetricUnit.Count, { eventType: "organization.deleted" });
    return false;
  }
}

/**
 * Lambda handler for processing organization events from SQS
 *
 * @param sqsEvent - The SQS event containing Clerk webhook events
 */
export const handler = async (sqsEvent: SQSEvent): Promise<void> => {
  logger.info(`Processing ${sqsEvent.Records.length} organization events`);

  for (const record of sqsEvent.Records) {
    try {
      // Parse the SNS message from the SQS record
      const snsMessage = JSON.parse(record.body);
      
      logger.info("SNS Message received", { 
        messageId: record.messageId,
        snsType: snsMessage.Type,
        topicArn: snsMessage.TopicArn
      });

      // Extract the actual webhook event from the SNS Message field
      let webhookEvent: ClerkWebhookEvent;
      
      if (snsMessage.Type === 'Notification' && snsMessage.Message) {
        // Parse the webhook event from the SNS Message field
        webhookEvent = JSON.parse(snsMessage.Message) as ClerkWebhookEvent;
      } else {
        // Fallback: try to parse the record body directly as webhook event
        webhookEvent = JSON.parse(record.body) as ClerkWebhookEvent;
      }

      logger.info("Processing organization event", {
        eventType: webhookEvent.type,
        messageId: record.messageId,
        organizationId: webhookEvent.data?.id
      });

      // Process based on event type
      let success = false;

      if (isOrganizationCreatedEvent(webhookEvent)) {
        success = await handleOrganizationCreated(webhookEvent);
      } else if (isOrganizationUpdatedEvent(webhookEvent)) {
        success = await handleOrganizationUpdated(webhookEvent);
      } else if (isOrganizationDeletedEvent(webhookEvent)) {
        success = await handleOrganizationDeleted(webhookEvent);
      } else if (isOrganizationMembershipCreatedEvent(webhookEvent)) {
        logger.info("Organization membership created - routing to organization handler");
        // Handle membership events as organization events for now
        success = true; // Just acknowledge for now
      } else if (isOrganizationMembershipUpdatedEvent(webhookEvent)) {
        logger.info("Organization membership updated - routing to organization handler");
        success = true; // Just acknowledge for now
      } else if (isOrganizationMembershipDeletedEvent(webhookEvent)) {
        logger.info("Organization membership deleted - routing to organization handler");
        success = true; // Just acknowledge for now
      } else {
        logger.warn("Unsupported organization event type", { 
          type: webhookEvent.type,
          availableData: Object.keys(webhookEvent.data || {})
        });
        addMetric("UnsupportedEventType", 1, MetricUnit.Count, { 
          eventType: webhookEvent.type || 'unknown'
        });
      }

      if (success) {
        addMetric("ProcessingSuccess", 1, MetricUnit.Count, { 
          eventType: webhookEvent.type || 'unknown'
        });
      } else {
        addMetric("ProcessingError", 1, MetricUnit.Count, { 
          eventType: webhookEvent.type || 'unknown'
        });
        // Let the error propagate to use SQS retry mechanism
        throw new Error(`Failed to process organization event: ${webhookEvent.type}`);
      }
    } catch (error) {
      logger.error("Error processing SQS record", {
        error,
        messageId: record.messageId,
        recordBody: record.body.substring(0, 500) + "..." // Log first 500 chars for debugging
      });
      // Rethrow to trigger SQS retry
      throw error;
    }
  }
};
