import { APIGatewayTokenAuthorizerEvent, APIGatewayAuthorizerResult } from "aws-lambda";
import { apiKeyService } from "commons";
import { logger } from "commons";

/**
 * Custom authorizer for API key authentication
 *
 * This authorizer validates API keys passed in the X-API-Key header
 * and returns the organization context for downstream Lambda functions
 */

const generatePolicy = (
  principalId: string,
  effect: "Allow" | "Deny",
  resource: string,
  context?: { [key: string]: any }
): APIGatewayAuthorizerResult => {
  const authResponse: APIGatewayAuthorizerResult = {
    principalId,
    policyDocument: {
      Version: "2012-10-17",
      Statement: [
        {
          Action: "execute-api:Invoke",
          Effect: effect,
          Resource: resource,
        },
      ],
    },
  };

  if (context) {
    authResponse.context = context;
  }

  return authResponse;
};

export const handler = async (
  event: APIGatewayTokenAuthorizerEvent
): Promise<APIGatewayAuthorizerResult> => {
  logger.info("API Key Authorizer invoked", {
    methodArn: event.methodArn,
    type: event.type,
  });

  try {
    // Extract API key from authorization token
    // Token format: "Bearer ckpl_xxxxx" or just "ckpl_xxxxx"
    const token = event.authorizationToken;
    if (!token) {
      logger.warn("No authorization token provided");
      return generatePolicy("anonymous", "Deny", event.methodArn);
    }

    // Remove "Bearer " prefix if present
    const apiKey = token.startsWith("Bearer ")
      ? token.substring(7)
      : token;

    // Validate the API key
    const validationResult = await apiKeyService.validateAPIKey({
      apiKey,
      endpoint: extractEndpoint(event.methodArn),
      method: extractMethod(event.methodArn),
    });

    if (!validationResult.isValid) {
      logger.warn("Invalid API key", {
        errorCode: validationResult.errorCode,
        errorMessage: validationResult.errorMessage,
      });
      return generatePolicy(apiKey.substring(0, 12), "Deny", event.methodArn);
    }

    // Check rate limits
    if (validationResult.rateLimit) {
      const rateLimitResult = await apiKeyService.checkRateLimit(
        validationResult.keyId!,
        validationResult.rateLimit
      );

      if (!rateLimitResult.allowed) {
        logger.warn("Rate limit exceeded", {
          keyId: validationResult.keyId,
          remainingRequests: rateLimitResult.remainingRequests,
        });
        return generatePolicy(validationResult.keyId!, "Deny", event.methodArn);
      }
    }

    // Generate Allow policy with context
    const context = {
      organizationId: validationResult.organizationId!,
      tenantId: validationResult.tenantId!,
      keyId: validationResult.keyId!,
      keyType: validationResult.type === "TEST" ? "TEST" : "LIVE",
    };

    logger.info("API key validated successfully", {
      keyId: validationResult.keyId,
      organizationId: validationResult.organizationId,
    });

    return generatePolicy(validationResult.keyId!, "Allow", event.methodArn, context);
  } catch (error) {
    logger.error("Error in API key authorizer", error as Error);
    return generatePolicy("error", "Deny", event.methodArn);
  }
};

/**
 * Extract endpoint from method ARN
 * Format: arn:aws:execute-api:region:account:api-id/stage/METHOD/path
 */
function extractEndpoint(methodArn: string): string {
  const parts = methodArn.split("/");
  return "/" + parts.slice(3).join("/");
}

/**
 * Extract HTTP method from method ARN
 */
function extractMethod(methodArn: string): string {
  const parts = methodArn.split("/");
  return parts[2] || "GET";
}