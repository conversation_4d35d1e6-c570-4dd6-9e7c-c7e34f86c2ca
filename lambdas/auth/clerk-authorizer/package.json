{"name": "clerk-authorizer-lambda", "version": "1.0.0", "description": "Clerk J<PERSON><PERSON> Authorizer for FireGuard API", "main": "index.js", "scripts": {"build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"jsonwebtoken": "^9.0.0", "@aws-lambda-powertools/logger": "^1.5.1", "joi": "^17.13.3", "commons": "file:../../layers/commons"}, "devDependencies": {"@types/aws-lambda": "^8.10.0", "@types/jsonwebtoken": "^9.0.0", "typescript": "^5.0.0"}}