import {
  APIGatewayTokenAuthorizerEvent,
  APIGatewayAuthorizerResult,
  PolicyDocument,
  Statement,
} from "aws-lambda";
import * as jwt from "jsonwebtoken";
import {
  secretsManager,
  SECRET_NAMES,
  ClerkJwtPublicKeyValue,
  logger
} from "commons";

// Cache for the public key
let cachedPublicKey: string | null = null;
let publicKeyLastFetchTime: number = 0;
const CACHE_DURATION_MS = 5 * 1000; // 5 seconds (for testing fresh secrets)

interface DecodedToken {
  sub: string; // Subject (Clerk User ID)
  organizationId?: string; // Direct organization ID if available
  organization?: {
    [key: string]: string; // Clerk organization format: {"org_id": "org:role"}
  };
  // Allow for other custom claims
  [key: string]: any;
}

/**
 * Fetches the Clerk public key from AWS Secrets Manager with caching.
 */
async function getClerkPublicKey(): Promise<string> {
  const now = Date.now();
  if (cachedPublicKey && now - publicKeyLastFetchTime < CACHE_DURATION_MS) {
    console.log("Returning cached public key.");
    return cachedPublicKey!; // Assert non-null as logic should ensure it
  }

  try {
    console.log(`Fetching public key from Secrets Manager: ${SECRET_NAMES.CLERK_JWT_PUBLIC_KEY}`);

    const secretValue = await secretsManager.getSecret(SECRET_NAMES.CLERK_JWT_PUBLIC_KEY) as ClerkJwtPublicKeyValue;

    // log first 10 characters of the public key
    console.log("Public key", {
      secret: secretValue.publicKey
    });

    if (!secretValue.publicKey) {
      console.error("Public key not found in secret.");
      throw new Error("Public key not found in secret.");
    }

    //log

    cachedPublicKey = secretValue.publicKey;
    publicKeyLastFetchTime = now;
    console.log("Successfully fetched and cached public key from Secrets Manager.");
    return cachedPublicKey!; // Assert non-null here as well after successful fetch
  } catch (error) {
    console.error("Error fetching public key from Secrets Manager:", error);
    // If fetching fails, clear the cache to force a refetch next time
    cachedPublicKey = null;
    throw new Error("Could not fetch public key from Secrets Manager.");
  }
}

/**
 * Generates an IAM policy document for API Gateway authorizer.
 */
const generatePolicy = (
  principalId: string,
  effect: "Allow" | "Deny",
  resource: string,
  context?: { [key: string]: any } // Optional context to pass to backend Lambda
): APIGatewayAuthorizerResult => {
  const authResponse: APIGatewayAuthorizerResult = {
    principalId,
    policyDocument: {
      Version: "2012-10-17",
      Statement: [
        {
          Action: "execute-api:Invoke",
          Effect: effect,
          Resource: resource,
        },
      ],
    },
  };
  if (context) {
    authResponse.context = context;
  }
  return authResponse;
};

/**
 * API Gateway Token Authorizer Lambda handler.
 */
export const handler = async (
  event: APIGatewayTokenAuthorizerEvent
): Promise<APIGatewayAuthorizerResult> => {
  console.log("Authorizer event:", JSON.stringify(event, null, 2));

  const tokenHeader = event.authorizationToken;
  if (!tokenHeader) {
    console.log("No authorization token found.");
    // Return a 401 Unauthorized response explicitly is also an option via an error type
    // For simplicity here, we deny access via policy. APIGW handles the 401/403.
    return generatePolicy("user_unauthorized", "Deny", event.methodArn);
  }

  const tokenParts = tokenHeader.split(" ");
  if (tokenParts.length !== 2 || tokenParts[0].toLowerCase() !== "bearer") {
    console.log("Invalid token format. Expected Bearer token.");
    return generatePolicy("user_unauthorized", "Deny", event.methodArn);
  }
  const token = tokenParts[1];

  try {
    const publicKey = await getClerkPublicKey();

    // Verify the token. Clerk typically uses RS256.
    // Ensure your public key from SSM is in the correct PEM format.
    const decoded = jwt.verify(token, publicKey, {
      algorithms: ["RS256"],
    }) as DecodedToken;
    console.log(
      "Token successfully verified. Decoded:",
      JSON.stringify(decoded, null, 2)
    );

    // Extract the subject (user ID) from the token
    const userId = decoded.sub;
    console.log("User ID extracted from token:", userId);

    // Log organization information for debugging
    if (decoded.organization) {
      console.log(
        "Organization data in token:",
        JSON.stringify(decoded.organization, null, 2)
      );
    } else if (decoded.organizationId) {
      console.log("Organization ID in token:", decoded.organizationId);
    } else {
      console.log("No organization information found in token");
    }

    // --- Context for backend Lambda ---
    // This context object will be available in `event.requestContext.authorizer` in the integrated Lambda.
    // Extract organization information from decoded token
    let organizationId: string | undefined = decoded.organizationId;

    // Check for organization in the special Clerk format
    if (!organizationId && decoded.organization) {
      // Get the first key from the organization object (the org ID)
      const orgKeys = Object.keys(decoded.organization);
      if (orgKeys.length > 0) {
        organizationId = orgKeys[0];
        console.log("Extracted organization ID from token:", organizationId);
      }
    }

    // Ensure the organizationId is always set when present in the token
    // API Gateway sometimes doesn't properly pass complex objects through context
    // so we'll make sure organizationId is a top-level property
    const context: Record<string, any> = {
      userId: userId, // Explicitly set userId in context
      sub: userId, // Also set sub for compatibility
      organizationId: organizationId || "", // Set organization ID (use empty string if not found)
      // Also include the context as a serialized JSON string to ensure it's passed correctly
      context: JSON.stringify({
        userId,
        sub: userId,
        organizationId,
        organization: decoded.organization,
      }),
    };

    // If we have an organization object, add string-based keys for API Gateway compatibility
    if (decoded.organization) {
      const orgKeys = Object.keys(decoded.organization);
      if (orgKeys.length > 0) {
        // Add org_id for primary organization
        context["org_id"] = orgKeys[0];
      }
    }

    console.log(
      "Setting authorizer context:",
      JSON.stringify(context, null, 2)
    );

    // Return an Allow policy with the context
    return generatePolicy(userId, "Allow", event.methodArn, context);
  } catch (error: unknown) {
    console.error("Token verification failed or other error:", error);
    if (error instanceof jwt.JsonWebTokenError) {
      console.log("JWT Error:", error.message);
    } else if (error instanceof Error) {
      console.log("Generic Error:", error.message);
    } else {
      console.log("Unknown error during token verification");
    }
    return generatePolicy("user_denied", "Deny", event.methodArn);
  }
};
