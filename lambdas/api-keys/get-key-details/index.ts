import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { apiKeyService } from "/opt/nodejs/lib/services/api-key-service";
import { createApiResponse, createErrorResponse } from "/opt/nodejs/lib/utils/api-responses";
import { getAuthContext } from "/opt/nodejs/lib/utils/auth-context";
import { logger } from "/opt/nodejs/lib/utils/logger";
import { tracer } from "/opt/nodejs/lib/utils/tracer";

/**
 * Lambda handler for getting API key details
 * 
 * Path parameter:
 * - keyId: The ID of the API key to get details for
 * 
 * Query parameters:
 * - includeAuditLogs: boolean (optional) - Whether to include audit logs
 * - auditStartTime: ISO 8601 date string (optional) - Start time for audit logs
 * - auditEndTime: ISO 8601 date string (optional) - End time for audit logs
 * - auditLimit: number (optional) - Maximum number of audit logs to return
 */
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  const segment = tracer.getSegment();
  const subsegment = segment?.addNewSubsegment("GetAPIKeyDetails");

  try {
    logger.info("Get API Key Details request received", {
      path: event.path,
      method: event.httpMethod,
      pathParams: event.pathParameters,
      queryParams: event.queryStringParameters,
    });

    // Get auth context
    const authContext = getAuthContext(event);
    if (!authContext || !authContext.organizationId) {
      logger.warn("Unauthorized request - missing auth context");
      return createErrorResponse(401, "Unauthorized");
    }

    const { organizationId, tenantId } = authContext;

    // Get key ID from path parameters
    const keyId = event.pathParameters?.keyId;
    if (!keyId) {
      return createErrorResponse(400, "Key ID is required");
    }

    // Get API key details
    const keyDetails = await apiKeyService.getAPIKeyDetails(tenantId, organizationId, keyId);

    // Check if audit logs are requested
    const includeAuditLogs = event.queryStringParameters?.includeAuditLogs === 'true';
    let auditLogs = undefined;

    if (includeAuditLogs) {
      const auditStartTime = event.queryStringParameters?.auditStartTime;
      const auditEndTime = event.queryStringParameters?.auditEndTime;
      const auditLimit = event.queryStringParameters?.auditLimit 
        ? parseInt(event.queryStringParameters.auditLimit) 
        : undefined;

      // Validate audit parameters
      if (auditStartTime && isNaN(Date.parse(auditStartTime))) {
        return createErrorResponse(400, "Invalid auditStartTime format");
      }
      if (auditEndTime && isNaN(Date.parse(auditEndTime))) {
        return createErrorResponse(400, "Invalid auditEndTime format");
      }
      if (auditLimit !== undefined && (isNaN(auditLimit) || auditLimit <= 0)) {
        return createErrorResponse(400, "auditLimit must be a positive number");
      }

      // Get audit logs
      auditLogs = await apiKeyService.getAPIKeyAuditLogs(
        tenantId,
        organizationId,
        keyId,
        {
          startTime: auditStartTime,
          endTime: auditEndTime,
          limit: auditLimit,
        }
      );
    }

    logger.info("API key details retrieved successfully", {
      keyId,
      organizationId,
      includeAuditLogs,
      auditLogCount: auditLogs?.length,
    });

    subsegment?.close();

    const response: any = {
      success: true,
      data: keyDetails,
    };

    if (auditLogs !== undefined) {
      response.data.auditLogs = auditLogs;
    }

    return createApiResponse(200, response);
  } catch (error) {
    logger.error("Failed to get API key details", error as Error);
    subsegment?.addError(error as Error);
    subsegment?.close();

    if ((error as Error).message?.includes("not found")) {
      return createErrorResponse(404, "API key not found");
    }

    return createErrorResponse(500, "Failed to get API key details");
  }
}; 