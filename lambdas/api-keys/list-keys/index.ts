import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { apiKeyService } from "/opt/nodejs/lib/services/api-key-service";
import { createApiResponse, createErrorResponse } from "/opt/nodejs/lib/utils/api-responses";
import { getAuthContext } from "/opt/nodejs/lib/utils/auth-context";
import { logger } from "/opt/nodejs/lib/utils/logger";
import { tracer } from "/opt/nodejs/lib/utils/tracer";

/**
 * Lambda handler for listing API keys
 * 
 * Query parameters:
 * - status: ACTIVE | REVOKED | EXPIRED | SUSPENDED (optional)
 */
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  const segment = tracer.getSegment();
  const subsegment = segment?.addNewSubsegment("ListAPIKeys");

  try {
    logger.info("List API Keys request received", {
      path: event.path,
      method: event.httpMethod,
      queryParams: event.queryStringParameters,
    });

    // Get auth context
    const authContext = getAuthContext(event);
    if (!authContext || !authContext.organizationId) {
      logger.warn("Unauthorized request - missing auth context");
      return createErrorResponse(401, "Unauthorized");
    }

    const { organizationId, tenantId } = authContext;

    // Get optional status filter from query params
    const status = event.queryStringParameters?.status as any;
    
    // Validate status if provided
    if (status && !['ACTIVE', 'REVOKED', 'EXPIRED', 'SUSPENDED'].includes(status)) {
      return createErrorResponse(400, "Invalid status filter. Must be one of: ACTIVE, REVOKED, EXPIRED, SUSPENDED");
    }

    // List API keys
    const apiKeys = await apiKeyService.listAPIKeys(tenantId, organizationId, status);

    logger.info("API keys listed successfully", {
      organizationId,
      count: apiKeys.length,
      status,
    });

    subsegment?.close();

    return createApiResponse(200, {
      success: true,
      data: {
        keys: apiKeys,
        count: apiKeys.length,
      },
    });
  } catch (error) {
    logger.error("Failed to list API keys", error as Error);
    subsegment?.addError(error as Error);
    subsegment?.close();

    return createErrorResponse(500, "Failed to list API keys");
  }
}; 