import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { apiKeyService } from "/opt/nodejs/lib/services/api-key-service";
import { createApiResponse, createErrorResponse } from "/opt/nodejs/lib/utils/api-responses";
import { getAuthContext } from "/opt/nodejs/lib/utils/auth-context";
import { logger } from "/opt/nodejs/lib/utils/logger";
import { tracer } from "/opt/nodejs/lib/utils/tracer";

/**
 * Lambda handler for revoking API keys
 * 
 * Path parameter:
 * - keyId: The ID of the API key to revoke
 * 
 * Request body (optional):
 * {
 *   "reason": "Compromised key"
 * }
 */
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  const segment = tracer.getSegment();
  const subsegment = segment?.addNewSubsegment("RevokeAPIKey");

  try {
    logger.info("Revoke API Key request received", {
      path: event.path,
      method: event.httpMethod,
      pathParams: event.pathParameters,
    });

    // Get auth context
    const authContext = getAuthContext(event);
    if (!authContext || !authContext.organizationId) {
      logger.warn("Unauthorized request - missing auth context");
      return createErrorResponse(401, "Unauthorized");
    }

    const { userId, organizationId, tenantId } = authContext;

    // Get key ID from path parameters
    const keyId = event.pathParameters?.keyId;
    if (!keyId) {
      return createErrorResponse(400, "Key ID is required");
    }

    // Get optional reason from request body
    let reason: string | undefined;
    if (event.body) {
      try {
        const requestBody = JSON.parse(event.body);
        reason = requestBody.reason;
      } catch (error) {
        // Ignore JSON parse errors for optional body
      }
    }

    // Revoke the API key
    await apiKeyService.revokeAPIKey(tenantId, organizationId, keyId, userId, reason);

    logger.info("API key revoked successfully", {
      keyId,
      organizationId,
      userId,
      reason,
    });

    subsegment?.close();

    return createApiResponse(200, {
      success: true,
      message: "API key revoked successfully",
    });
  } catch (error) {
    logger.error("Failed to revoke API key", error as Error);
    subsegment?.addError(error as Error);
    subsegment?.close();

    if ((error as Error).message?.includes("not found")) {
      return createErrorResponse(404, "API key not found");
    }

    return createErrorResponse(500, "Failed to revoke API key");
  }
}; 