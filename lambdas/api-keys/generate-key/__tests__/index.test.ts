import { handler } from "../index";
import { APIGatewayProxyEvent } from "aws-lambda";
import { apiKeyService } from "/opt/nodejs/lib/services/api-key-service";

// Mock dependencies
jest.mock("/opt/nodejs/lib/services/api-key-service");
jest.mock("/opt/nodejs/lib/utils/auth-context", () => ({
  getAuthContext: jest.fn(),
}));
jest.mock("/opt/nodejs/lib/utils/logger", () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));
jest.mock("/opt/nodejs/lib/utils/tracer", () => ({
  tracer: {
    getSegment: jest.fn(() => ({
      addNewSubsegment: jest.fn(() => ({
        close: jest.fn(),
        addError: jest.fn(),
      })),
    })),
  },
}));

const { getAuthContext } = require("/opt/nodejs/lib/utils/auth-context");

describe("Generate API Key Lambda Handler", () => {
  const mockAuthContext = {
    userId: "user_123",
    organizationId: "org_123",
    tenantId: "tenant_123",
    userEmail: "<EMAIL>",
  };

  const createMockEvent = (body: any, headers = {}): APIGatewayProxyEvent => ({
    body: JSON.stringify(body),
    headers: {
      "Content-Type": "application/json",
      ...headers,
    },
    httpMethod: "POST",
    path: "/api/v1/api-keys",
    pathParameters: null,
    queryStringParameters: null,
    requestContext: {} as any,
    resource: "",
    stageVariables: null,
    isBase64Encoded: false,
    multiValueHeaders: {},
    multiValueQueryStringParameters: null,
  });

  beforeEach(() => {
    jest.clearAllMocks();
    getAuthContext.mockReturnValue(mockAuthContext);
  });

  it("should generate a new API key successfully", async () => {
    const mockApiKey = {
      keyId: "key_123",
      keySecret: "ckpl_test123456789",
      keyPrefix: "ckpl_test1234",
      type: "LIVE",
      label: "Production API Key",
      createdAt: new Date().toISOString(),
    };

    (apiKeyService.generateAPIKey as jest.Mock).mockResolvedValue(mockApiKey);

    const event = createMockEvent({
      type: "LIVE",
      label: "Production API Key",
    });

    const response = await handler(event);

    expect(response.statusCode).toBe(201);
    const body = JSON.parse(response.body);
    expect(body.success).toBe(true);
    expect(body.data).toEqual(mockApiKey);
    expect(apiKeyService.generateAPIKey).toHaveBeenCalledWith({
      organizationId: "org_123",
      tenantId: "tenant_123",
      userId: "user_123",
      userEmail: "<EMAIL>",
      type: "LIVE",
      label: "Production API Key",
    });
  });

  it("should handle authentication failure", async () => {
    getAuthContext.mockReturnValue(null);

    const event = createMockEvent({
      type: "LIVE",
    });

    const response = await handler(event);

    expect(response.statusCode).toBe(401);
    const body = JSON.parse(response.body);
    expect(body.error).toBe("Unauthorized");
  });

  it("should validate missing request body", async () => {
    const event = createMockEvent(null);
    event.body = null;

    const response = await handler(event);

    expect(response.statusCode).toBe(400);
    const body = JSON.parse(response.body);
    expect(body.error).toBe("Request body is required");
  });

  it("should validate invalid JSON", async () => {
    const event = createMockEvent({});
    event.body = "invalid json";

    const response = await handler(event);

    expect(response.statusCode).toBe(400);
    const body = JSON.parse(response.body);
    expect(body.error).toBe("Invalid JSON in request body");
  });

  it("should validate invalid API key type", async () => {
    const event = createMockEvent({
      type: "INVALID",
    });

    const response = await handler(event);

    expect(response.statusCode).toBe(400);
    const body = JSON.parse(response.body);
    expect(body.error).toBe("Invalid API key type. Must be 'LIVE' or 'TEST'");
  });

  it("should validate expiration date", async () => {
    const event = createMockEvent({
      expiresAt: "2020-01-01T00:00:00Z",
    });

    const response = await handler(event);

    expect(response.statusCode).toBe(400);
    const body = JSON.parse(response.body);
    expect(body.error).toBe("Expiration date must be in the future");
  });

  it("should validate rate limit structure", async () => {
    const event = createMockEvent({
      rateLimit: {
        invalidKey: 10,
      },
    });

    const response = await handler(event);

    expect(response.statusCode).toBe(400);
    const body = JSON.parse(response.body);
    expect(body.error).toBe("Invalid rate limit key: invalidKey");
  });

  it("should validate rate limit values", async () => {
    const event = createMockEvent({
      rateLimit: {
        requestsPerSecond: -5,
      },
    });

    const response = await handler(event);

    expect(response.statusCode).toBe(400);
    const body = JSON.parse(response.body);
    expect(body.error).toBe("Rate limit requestsPerSecond must be a positive number");
  });

  it("should validate permissions structure", async () => {
    const event = createMockEvent({
      permissions: {
        allowedEndpoints: "not-an-array",
      },
    });

    const response = await handler(event);

    expect(response.statusCode).toBe(400);
    const body = JSON.parse(response.body);
    expect(body.error).toBe("allowedEndpoints must be an array");
  });

  it("should handle service errors", async () => {
    (apiKeyService.generateAPIKey as jest.Mock).mockRejectedValue(new Error("Service error"));

    const event = createMockEvent({
      type: "LIVE",
    });

    const response = await handler(event);

    expect(response.statusCode).toBe(500);
    const body = JSON.parse(response.body);
    expect(body.error).toBe("Failed to generate API key");
  });

  it("should handle invalid organization error", async () => {
    (apiKeyService.generateAPIKey as jest.Mock).mockRejectedValue(new Error("Invalid organization"));

    const event = createMockEvent({
      type: "LIVE",
    });

    const response = await handler(event);

    expect(response.statusCode).toBe(403);
    const body = JSON.parse(response.body);
    expect(body.error).toBe("Organization not found or inactive");
  });

  it("should accept all valid parameters", async () => {
    const mockApiKey = {
      keyId: "key_123",
      keySecret: "ckpl_test123456789",
      keyPrefix: "ckpl_test1234",
      type: "TEST",
      label: "Test API Key",
      createdAt: new Date().toISOString(),
      expiresAt: "2025-01-01T00:00:00Z",
    };

    (apiKeyService.generateAPIKey as jest.Mock).mockResolvedValue(mockApiKey);

    const event = createMockEvent({
      type: "TEST",
      label: "Test API Key",
      expiresAt: "2025-01-01T00:00:00Z",
      rateLimit: {
        requestsPerSecond: 5,
        requestsPerMinute: 50,
      },
      permissions: {
        allowedEndpoints: ["/api/v1/*"],
        allowedMethods: ["GET", "POST"],
        ipWhitelist: ["***********"],
      },
    });

    const response = await handler(event);

    expect(response.statusCode).toBe(201);
    expect(apiKeyService.generateAPIKey).toHaveBeenCalledWith({
      organizationId: "org_123",
      tenantId: "tenant_123",
      userId: "user_123",
      userEmail: "<EMAIL>",
      type: "TEST",
      label: "Test API Key",
      expiresAt: "2025-01-01T00:00:00Z",
      rateLimit: {
        requestsPerSecond: 5,
        requestsPerMinute: 50,
      },
      permissions: {
        allowedEndpoints: ["/api/v1/*"],
        allowedMethods: ["GET", "POST"],
        ipWhitelist: ["***********"],
      },
    });
  });
}); 