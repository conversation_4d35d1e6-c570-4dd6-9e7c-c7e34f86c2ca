import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { apiKeyService, GenerateAPIKeyRequest } from "/opt/nodejs/lib/services/api-key-service";
import { createApiResponse, createErrorResponse } from "/opt/nodejs/lib/utils/api-responses";
import { getAuthContext } from "/opt/nodejs/lib/utils/auth-context";
import { logger } from "/opt/nodejs/lib/utils/logger";
import { tracer } from "/opt/nodejs/lib/utils/tracer";

/**
 * Lambda handler for generating new API keys
 * 
 * Expected request body:
 * {
 *   "type": "LIVE" | "TEST",
 *   "label": "Production API Key",
 *   "expiresAt": "2025-12-31T23:59:59Z", // optional
 *   "rateLimit": { // optional
 *     "requestsPerSecond": 10,
 *     "requestsPerMinute": 100,
 *     "requestsPerHour": 1000,
 *     "requestsPerDay": 10000
 *   },
 *   "permissions": { // optional
 *     "allowedEndpoints": ["/api/v1/*"],
 *     "allowedMethods": ["GET", "POST"],
 *     "ipWhitelist": ["***********"]
 *   }
 * }
 */
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  const segment = tracer.getSegment();
  const subsegment = segment?.addNewSubsegment("GenerateAPIKey");

  try {
    logger.info("Generate API Key request received", {
      path: event.path,
      method: event.httpMethod,
    });

    // Get auth context
    const authContext = getAuthContext(event);
    if (!authContext || !authContext.organizationId) {
      logger.warn("Unauthorized request - missing auth context");
      return createErrorResponse(401, "Unauthorized");
    }

    const { userId, organizationId, tenantId, userEmail } = authContext;

    // Validate request body
    if (!event.body) {
      return createErrorResponse(400, "Request body is required");
    }

    let requestBody: any;
    try {
      requestBody = JSON.parse(event.body);
    } catch (error) {
      return createErrorResponse(400, "Invalid JSON in request body");
    }

    // Validate required fields
    const { type, label, expiresAt, rateLimit, permissions } = requestBody;

    // Validate type
    if (type && !['LIVE', 'TEST'].includes(type)) {
      return createErrorResponse(400, "Invalid API key type. Must be 'LIVE' or 'TEST'");
    }

    // Validate expiration date if provided
    if (expiresAt) {
      const expirationDate = new Date(expiresAt);
      if (isNaN(expirationDate.getTime())) {
        return createErrorResponse(400, "Invalid expiration date format");
      }
      if (expirationDate <= new Date()) {
        return createErrorResponse(400, "Expiration date must be in the future");
      }
    }

    // Validate rate limits if provided
    if (rateLimit) {
      const validRateLimitKeys = ['requestsPerSecond', 'requestsPerMinute', 'requestsPerHour', 'requestsPerDay'];
      for (const key in rateLimit) {
        if (!validRateLimitKeys.includes(key)) {
          return createErrorResponse(400, `Invalid rate limit key: ${key}`);
        }
        if (typeof rateLimit[key] !== 'number' || rateLimit[key] <= 0) {
          return createErrorResponse(400, `Rate limit ${key} must be a positive number`);
        }
      }
    }

    // Validate permissions if provided
    if (permissions) {
      if (permissions.allowedEndpoints && !Array.isArray(permissions.allowedEndpoints)) {
        return createErrorResponse(400, "allowedEndpoints must be an array");
      }
      if (permissions.allowedMethods && !Array.isArray(permissions.allowedMethods)) {
        return createErrorResponse(400, "allowedMethods must be an array");
      }
      if (permissions.ipWhitelist && !Array.isArray(permissions.ipWhitelist)) {
        return createErrorResponse(400, "ipWhitelist must be an array");
      }
    }

    // Generate API key
    const generateRequest: GenerateAPIKeyRequest = {
      organizationId,
      tenantId,
      userId,
      userEmail,
      type: type || 'LIVE',
      label,
      expiresAt,
      rateLimit,
      permissions,
    };

    const apiKey = await apiKeyService.generateAPIKey(generateRequest);

    logger.info("API key generated successfully", {
      keyId: apiKey.keyId,
      keyPrefix: apiKey.keyPrefix,
      organizationId,
    });

    subsegment?.close();

    return createApiResponse(201, {
      success: true,
      data: apiKey,
    });
  } catch (error) {
    logger.error("Failed to generate API key", error as Error);
    subsegment?.addError(error as Error);
    subsegment?.close();

    if ((error as Error).message === "Invalid organization") {
      return createErrorResponse(403, "Organization not found or inactive");
    }

    return createErrorResponse(500, "Failed to generate API key");
  }
}; 