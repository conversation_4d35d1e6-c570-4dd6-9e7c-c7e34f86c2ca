import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import Jo<PERSON>, { ValidationErrorItem } from "joi";

import {
  KYCService,
  ProveProvider,
  UserService,
  KYCIndividual,
  logger,
  createSuccessResponse,
  createErrorResponse,
  createBadRequestResponse,
} from "commons";

// Input validation schema
const completeVerificationSchema = Joi.object({
  individual: Joi.object({
    firstName: Joi.string().required(),
    lastName: Joi.string().required(),
    dateOfBirth: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required(),
    phoneNumber: Joi.string().required(),
    emailAddresses: Joi.array().items(Joi.string().email()).optional(),
    addresses: Joi.array().items(Joi.object({
      street: Joi.string().required(),
      city: Joi.string().required(),
      region: Joi.string().required(),
      postalCode: Joi.string().required(),
      country: Joi.string().required()
    })).optional()
  }).required()
});

export const handler = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  const requestId = event.requestContext.requestId;
  
  logger.info("Received complete KYC verification request", { 
    path: event.path,
    method: event.httpMethod,
    requestId
  });

  // Get correlation ID from path parameters
  const correlationId = event.pathParameters?.correlationId;
  
  if (!correlationId) {
    logger.warn("Correlation ID missing from path parameters");
    return createBadRequestResponse("Correlation ID is required");
  }

  // Validate request body
  let requestBody: any = {};
  if (event.body) {
    try {
      requestBody = JSON.parse(event.body);
      logger.info("Parsed request body", { 
        hasIndividual: !!requestBody.individual,
        firstName: requestBody.individual?.firstName,
        lastName: requestBody.individual?.lastName
      });
    } catch (error) {
      logger.error("Invalid JSON format in request body", error as Error);
      return createBadRequestResponse("Invalid JSON format");
    }
  }

  // Validate request schema
  const { error: validationError, value: validatedBody } = completeVerificationSchema.validate(requestBody);
  if (validationError) {
    logger.warn("Request body validation failed", {
      details: validationError.details,
    });
    return createBadRequestResponse(
      "Validation failed",
      validationError.details.map((d: ValidationErrorItem) => d.message)
    );
  }

  // Get user context from authorizer
  const userId = event.requestContext.authorizer?.userId;
  const clerkUserId = event.requestContext.authorizer?.sub || event.requestContext.authorizer?.userId;
  const tenantId = event.requestContext.authorizer?.tenantId || 'default';
  
  if (!userId || !clerkUserId) {
    logger.error("User ID not found in authorizer context");
    return createErrorResponse(500, "Internal server error: User ID missing from context");
  }

  logger.info("Processing KYC verification completion", { 
    userId, 
    clerkUserId,
    tenantId,
    correlationId
  });

  try {
    // Initialize services
    const kycService = new KYCService(tenantId);

    // Find verification by correlation ID
    const verification = await kycService.getLatestVerificationByUser(userId);
    
    if (!verification) {
      logger.warn("No verification found for user", { userId });
      return createBadRequestResponse("No verification found for user");
    }

    if (verification.metadata.correlationId !== correlationId) {
      logger.warn("Correlation ID mismatch", { 
        expected: verification.metadata.correlationId,
        provided: correlationId 
      });
      return createBadRequestResponse("Invalid correlation ID");
    }

    if (verification.status !== "IN_PROGRESS") {
      logger.warn("Verification not in progress", { 
        verificationId: verification.id,
        status: verification.status 
      });
      return createBadRequestResponse("Verification is not in progress");
    }

    // Complete verification with provider
    let providerResponse;
    if (verification.provider === "PROVE") {
      const proveProvider = new ProveProvider({
        clientId: process.env.PROVE_CLIENT_ID!,
        clientSecret: process.env.PROVE_CLIENT_SECRET!,
        environment: (process.env.PROVE_ENVIRONMENT as "SANDBOX" | "PRODUCTION") || "SANDBOX",
        isEnabled: process.env.PROVE_ENABLED === "true"
      });

      providerResponse = await proveProvider.completeVerification(
        verification.id, 
        validatedBody.individual
      );
    } else {
      // For other providers, return basic completion response
      providerResponse = {
        correlationId,
        success: true,
        verificationResult: "VERIFIED",
        individual: validatedBody.individual
      };
    }

    // Determine final verification status
    const isVerified = providerResponse.success && providerResponse.verificationResult === "VERIFIED";
    const finalStatus = isVerified ? "VERIFIED" : "FAILED";

    // Update verification with completion data
    const updatedVerification = await kycService.updateVerification(
      verification.id,
      userId,
      finalStatus,
      {
        ...verification.metadata,
        completedAt: new Date().toISOString(),
        verificationResult: providerResponse.verificationResult,
        individual: validatedBody.individual,
        providerResponse: {
          success: providerResponse.success,
          message: providerResponse.message,
          score: providerResponse.score
        }
      }
    );

    // Update user's KYC status if verified
    if (isVerified) {
      await UserService.updateKYCStatus(
        userId,
        clerkUserId,
        "VERIFIED",
        verification.verificationLevel,
        verification.provider
      );

      logger.info("User KYC status updated to VERIFIED", { 
        userId,
        verificationId: verification.id,
        level: verification.verificationLevel,
        provider: verification.provider
      });
    }

    logger.info("KYC verification completed", { 
      verificationId: verification.id,
      correlationId,
      success: providerResponse.success,
      finalStatus,
      isVerified
    });

    // Return success response
    const response = {
      correlationId,
      verificationId: verification.id,
      status: finalStatus,
      success: providerResponse.success,
      verificationResult: providerResponse.verificationResult,
      individual: validatedBody.individual,
      completedAt: updatedVerification?.metadata.completedAt,
      message: providerResponse.message || (isVerified ? "Verification completed successfully" : "Verification failed"),
      score: providerResponse.score
    };

    return createSuccessResponse(200, response);

  } catch (error) {
    logger.error("Error completing KYC verification", error as Error);
    
    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes("not found")) {
        return createBadRequestResponse("Verification not found");
      }
      if (error.message.includes("Invalid correlation")) {
        return createBadRequestResponse("Invalid correlation ID");
      }
      if (error.message.includes("Verification failed")) {
        return createBadRequestResponse("Verification completion failed");
      }
    }

    return createErrorResponse(500, "Failed to complete KYC verification due to an internal error");
  }
}; 