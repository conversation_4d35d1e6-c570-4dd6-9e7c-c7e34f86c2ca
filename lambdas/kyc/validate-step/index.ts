import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import Jo<PERSON>, { ValidationErrorItem } from "joi";

import {
  KYCService,
  ProveProvider,
  logger,
  createSuccessResponse,
  createErrorResponse,
  createBadRequestResponse,
} from "commons";

// Input validation schema
const validateStepSchema = Joi.object({
  correlationId: Joi.string().required()
});

export const handler = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  const requestId = event.requestContext.requestId;
  
  logger.info("Received validate KYC step request", { 
    path: event.path,
    method: event.httpMethod,
    requestId
  });

  // Get correlation ID from path parameters
  const correlationId = event.pathParameters?.correlationId;
  
  if (!correlationId) {
    logger.warn("Correlation ID missing from path parameters");
    return createBadRequestResponse("Correlation ID is required");
  }

  // Validate correlation ID format
  const { error: validationError } = validateStepSchema.validate({ correlationId });
  if (validationError) {
    logger.warn("Correlation ID validation failed", {
      details: validationError.details,
    });
    return createBadRequestResponse(
      "Invalid correlation ID format",
      validationError.details.map((d: ValidationErrorItem) => d.message)
    );
  }

  // Get user context from authorizer
  const userId = event.requestContext.authorizer?.userId;
  const clerkUserId = event.requestContext.authorizer?.sub || event.requestContext.authorizer?.userId;
  const tenantId = event.requestContext.authorizer?.tenantId || 'default';
  
  if (!userId || !clerkUserId) {
    logger.error("User ID not found in authorizer context");
    return createErrorResponse(500, "Internal server error: User ID missing from context");
  }

  logger.info("Processing KYC validation step", { 
    userId, 
    clerkUserId,
    tenantId,
    correlationId
  });

  try {
    // Initialize KYC service
    const kycService = new KYCService(tenantId);

    // Find verification by correlation ID
    // Note: This would require a method to find verification by correlation ID
    // For now, we'll get the latest verification for the user
    const verification = await kycService.getLatestVerificationByUser(userId);
    
    if (!verification) {
      logger.warn("No verification found for user", { userId });
      return createBadRequestResponse("No verification found for user");
    }

    if (verification.metadata.correlationId !== correlationId) {
      logger.warn("Correlation ID mismatch", { 
        expected: verification.metadata.correlationId,
        provided: correlationId 
      });
      return createBadRequestResponse("Invalid correlation ID");
    }

    if (verification.status !== "IN_PROGRESS") {
      logger.warn("Verification not in progress", { 
        verificationId: verification.id,
        status: verification.status 
      });
      return createBadRequestResponse("Verification is not in progress");
    }

    // Validate with provider
    let providerResponse;
    if (verification.provider === "PROVE") {
      const proveProvider = new ProveProvider({
        clientId: process.env.PROVE_CLIENT_ID!,
        clientSecret: process.env.PROVE_CLIENT_SECRET!,
        environment: (process.env.PROVE_ENVIRONMENT as "SANDBOX" | "PRODUCTION") || "SANDBOX",
        isEnabled: process.env.PROVE_ENABLED === "true"
      });

      providerResponse = await proveProvider.validateStep(verification.id, { correlationId });
    } else {
      // For other providers, return basic validation
      providerResponse = {
        correlationId,
        isValid: true,
        nextStep: "challenge"
      };
    }

    logger.info("KYC validation step completed", { 
      verificationId: verification.id,
      correlationId,
      isValid: providerResponse.isValid,
      nextStep: providerResponse.nextStep
    });

    // Return success response
    const response = {
      correlationId,
      isValid: providerResponse.isValid || true,
      nextStep: providerResponse.nextStep || "challenge",
      message: providerResponse.message || "Validation successful",
      verificationId: verification.id,
      status: verification.status
    };

    return createSuccessResponse(200, response);

  } catch (error) {
    logger.error("Error validating KYC step", error as Error);
    
    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes("not found")) {
        return createBadRequestResponse("Verification not found");
      }
      if (error.message.includes("Invalid correlation")) {
        return createBadRequestResponse("Invalid correlation ID");
      }
    }

    return createErrorResponse(500, "Failed to validate KYC step due to an internal error");
  }
}; 