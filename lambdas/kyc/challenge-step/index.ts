import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import Jo<PERSON>, { ValidationErrorItem } from "joi";

import {
  KYCService,
  ProveProvider,
  logger,
  createSuccessResponse,
  createErrorResponse,
  createBadRequestResponse,
} from "commons";

// Input validation schema
const challengeStepSchema = Joi.object({
  dateOfBirth: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).optional(),
  last4SSN: Joi.string().length(4).optional(),
  challengeAnswers: Joi.array().items(Joi.string()).optional()
});

export const handler = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  const requestId = event.requestContext.requestId;
  
  logger.info("Received challenge KYC step request", { 
    path: event.path,
    method: event.httpMethod,
    requestId
  });

  // Get correlation ID from path parameters
  const correlationId = event.pathParameters?.correlationId;
  
  if (!correlationId) {
    logger.warn("Correlation ID missing from path parameters");
    return createBadRequestResponse("Correlation ID is required");
  }

  // Validate request body
  let requestBody: any = {};
  if (event.body) {
    try {
      requestBody = JSON.parse(event.body);
      logger.info("Parsed request body", { 
        hasDateOfBirth: !!requestBody.dateOfBirth,
        hasLast4SSN: !!requestBody.last4SSN,
        challengeAnswersCount: requestBody.challengeAnswers?.length || 0
      });
    } catch (error) {
      logger.error("Invalid JSON format in request body", error as Error);
      return createBadRequestResponse("Invalid JSON format");
    }
  }

  // Validate request schema
  const { error: validationError, value: validatedBody } = challengeStepSchema.validate(requestBody);
  if (validationError) {
    logger.warn("Request body validation failed", {
      details: validationError.details,
    });
    return createBadRequestResponse(
      "Validation failed",
      validationError.details.map((d: ValidationErrorItem) => d.message)
    );
  }

  // Get user context from authorizer
  const userId = event.requestContext.authorizer?.userId;
  const clerkUserId = event.requestContext.authorizer?.sub || event.requestContext.authorizer?.userId;
  const tenantId = event.requestContext.authorizer?.tenantId || 'default';
  
  if (!userId || !clerkUserId) {
    logger.error("User ID not found in authorizer context");
    return createErrorResponse(500, "Internal server error: User ID missing from context");
  }

  logger.info("Processing KYC challenge step", { 
    userId, 
    clerkUserId,
    tenantId,
    correlationId
  });

  try {
    // Initialize KYC service
    const kycService = new KYCService(tenantId);

    // Find verification by correlation ID
    const verification = await kycService.getLatestVerificationByUser(userId);
    
    if (!verification) {
      logger.warn("No verification found for user", { userId });
      return createBadRequestResponse("No verification found for user");
    }

    if (verification.metadata.correlationId !== correlationId) {
      logger.warn("Correlation ID mismatch", { 
        expected: verification.metadata.correlationId,
        provided: correlationId 
      });
      return createBadRequestResponse("Invalid correlation ID");
    }

    if (verification.status !== "IN_PROGRESS") {
      logger.warn("Verification not in progress", { 
        verificationId: verification.id,
        status: verification.status 
      });
      return createBadRequestResponse("Verification is not in progress");
    }

    // Process challenge with provider
    let providerResponse;
    if (verification.provider === "PROVE") {
      const proveProvider = new ProveProvider({
        clientId: process.env.PROVE_CLIENT_ID!,
        clientSecret: process.env.PROVE_CLIENT_SECRET!,
        environment: (process.env.PROVE_ENVIRONMENT as "SANDBOX" | "PRODUCTION") || "SANDBOX",
        isEnabled: process.env.PROVE_ENABLED === "true"
      });

      const challengeData = {
        correlationId,
        dateOfBirth: validatedBody.dateOfBirth,
        last4SSN: validatedBody.last4SSN,
        challengeAnswers: validatedBody.challengeAnswers
      };

      providerResponse = await proveProvider.challengeStep(verification.id, challengeData);
    } else {
      // For other providers, return basic challenge response
      providerResponse = {
        correlationId,
        success: true,
        nextStep: "complete"
      };
    }

    // Update verification metadata with challenge data
    await kycService.updateVerification(
      verification.id,
      userId,
      "IN_PROGRESS",
      {
        ...verification.metadata,
        challengeCompleted: true,
        challengeData: {
          dateOfBirth: validatedBody.dateOfBirth,
          hasSSN: !!validatedBody.last4SSN,
          answersProvided: validatedBody.challengeAnswers?.length || 0
        }
      }
    );

    logger.info("KYC challenge step completed", { 
      verificationId: verification.id,
      correlationId,
      success: providerResponse.success,
      nextStep: providerResponse.nextStep
    });

    // Return success response
    const response = {
      correlationId,
      success: providerResponse.success || true,
      challengeQuestions: providerResponse.challengeQuestions || [],
      verificationId: verification.id,
      nextStep: providerResponse.nextStep || "complete",
      message: providerResponse.message || "Challenge step completed"
    };

    return createSuccessResponse(200, response);

  } catch (error) {
    logger.error("Error processing KYC challenge step", error as Error);
    
    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes("not found")) {
        return createBadRequestResponse("Verification not found");
      }
      if (error.message.includes("Invalid correlation")) {
        return createBadRequestResponse("Invalid correlation ID");
      }
      if (error.message.includes("Challenge failed")) {
        return createBadRequestResponse("Challenge verification failed");
      }
    }

    return createErrorResponse(500, "Failed to process KYC challenge step due to an internal error");
  }
}; 