import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";

import {
  KYCService,
  logger,
  createSuccessResponse,
  createErrorResponse,
  createBadRequestResponse,
} from "commons";

export const handler = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  const requestId = event.requestContext.requestId;
  
  logger.info("Received get KYC status request", { 
    path: event.path,
    method: event.httpMethod,
    requestId
  });

  // Get user context from authorizer
  const userId = event.requestContext.authorizer?.userId;
  const clerkUserId = event.requestContext.authorizer?.sub || event.requestContext.authorizer?.userId;
  const tenantId = event.requestContext.authorizer?.tenantId || 'default';
  
  if (!userId || !clerkUserId) {
    logger.error("User ID not found in authorizer context");
    return createErrorResponse(500, "Internal server error: User ID missing from context");
  }

  // Check if we're getting status by verification ID or latest for user
  const verificationId = event.pathParameters?.verificationId;
  const isLatestRequest = event.path.includes('/user/status');

  logger.info("Processing KYC status request", { 
    userId, 
    clerkUserId,
    tenantId,
    verificationId,
    isLatestRequest
  });

  try {
    // Initialize KYC service
    const kycService = new KYCService(tenantId);

    let verification;
    
    if (isLatestRequest) {
      // Get latest verification for user
      verification = await kycService.getLatestVerificationByUser(userId);
      
      if (!verification) {
        logger.info("No verification found for user", { userId });
        return createSuccessResponse(200, {
          hasVerification: false,
          status: "NOT_STARTED",
          message: "No verification found for user"
        });
      }
    } else {
      // Get specific verification by ID
      if (!verificationId) {
        logger.warn("Verification ID missing from path parameters");
        return createBadRequestResponse("Verification ID is required");
      }

      verification = await kycService.getVerification(verificationId, userId);
      
      if (!verification) {
        logger.warn("Verification not found", { verificationId });
        return createBadRequestResponse("Verification not found");
      }
    }

    logger.info("KYC status retrieved", { 
      verificationId: verification.id,
      status: verification.status,
      provider: verification.provider,
      level: verification.verificationLevel
    });

    // Prepare response data (filter sensitive information)
    const responseData = {
      hasVerification: true,
      verificationId: verification.id,
      status: verification.status,
      provider: verification.provider,
      verificationLevel: verification.verificationLevel,
      createdAt: verification.createdAt,
      updatedAt: verification.updatedAt,
      canRetry: verification.status === "FAILED" || verification.status === "EXPIRED",
      metadata: {
        correlationId: verification.metadata.correlationId,
        completedAt: verification.metadata.completedAt,
        verificationResult: verification.metadata.verificationResult,
        // Don't include sensitive individual data or provider responses
        hasIndividualData: !!verification.metadata.individual,
        challengeCompleted: verification.metadata.challengeCompleted || false
      }
    };

    // Add retry information if applicable
    if (responseData.canRetry) {
      responseData.metadata = {
        ...responseData.metadata,
        retryCount: verification.metadata.retryCount || 0,
        maxRetries: 3,
        canRetryAfter: verification.metadata.canRetryAfter
      } as any;
    }

    return createSuccessResponse(200, responseData);

  } catch (error) {
    logger.error("Error retrieving KYC status", error as Error);
    
    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes("not found")) {
        return createBadRequestResponse("Verification not found");
      }
      if (error.message.includes("Access denied")) {
        return createBadRequestResponse("Access denied");
      }
    }

    return createErrorResponse(500, "Failed to retrieve KYC status due to an internal error");
  }
}; 