import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import Joi, { ValidationErrorItem } from "joi";

import {
  KYCService,
  ProveProviderFactory,
  logger,
  createSuccessResponse,
  createErrorResponse,
  createBadRequestResponse,
} from "commons";
import {
  KYCProvider,
  KYCVerificationLevel,
  KYCIndividual,
  StartKYCVerificationRequest,
} from "commons";

// Input validation schema
const startKYCSchema = Joi.object({
  provider: Joi.string().valid("PROVE", "JUMIO", "ONFIDO", "MANUAL").required(),
  verificationLevel: Joi.string().valid("BASIC", "ENHANCED", "PREMIUM").required(),
  individual: Joi.object({
    firstName: Joi.string().required(),
    lastName: Joi.string().required(),
    dateOfBirth: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required(),
    phoneNumber: Joi.string().when('$provider', {
      is: 'PROVE',
      then: Joi.required(),
      otherwise: Joi.optional()
    }),
    ssn: Joi.string().when('$provider', {
      is: 'PROVE',
      then: Joi.required(),
      otherwise: Joi.optional()
    }),
    emailAddresses: Joi.array().items(Joi.string().email()).optional(),
    addresses: Joi.array().items(Joi.object({
      street: Joi.string().required(),
      extendedAddress: Joi.string().optional(),
      city: Joi.string().required(),
      region: Joi.string().required(),
      postalCode: Joi.string().required(),
      country: Joi.string().optional()
    })).optional()
  }).required(),
  metadata: Joi.object({
    ipAddress: Joi.string().optional(),
    userAgent: Joi.string().optional(),
    deviceFingerprint: Joi.string().optional(),
    verificationMethod: Joi.string().optional(),
    documentTypes: Joi.array().items(Joi.string()).optional()
  }).optional()
});

export const handler = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  const requestId = event.requestContext.requestId;
  
  logger.info("Received start KYC verification request", { 
    path: event.path,
    method: event.httpMethod,
    requestId
  });

  // Validate request body
  if (!event.body) {
    logger.warn("Request body is missing");
    return createBadRequestResponse("Request body is missing");
  }

  let requestBody: any;
  try {
    requestBody = JSON.parse(event.body);
    logger.info("Parsed request body", { 
      provider: requestBody.provider,
      verificationLevel: requestBody.verificationLevel 
    });
  } catch (error) {
    logger.error("Invalid JSON format in request body", error as Error);
    return createBadRequestResponse("Invalid JSON format");
  }

  // Validate request schema
  const { error: validationError, value: validatedBody } = startKYCSchema.validate(
    requestBody, 
    { context: { provider: requestBody.provider } }
  );
  
  if (validationError) {
    logger.warn("Request body validation failed", {
      details: validationError.details,
    });
    return createBadRequestResponse(
      "Validation failed",
      validationError.details.map((d: ValidationErrorItem) => d.message)
    );
  }

  // Get user context from authorizer
  const userId = event.requestContext.authorizer?.userId;
  const clerkUserId = event.requestContext.authorizer?.sub || event.requestContext.authorizer?.userId;
  const tenantId = event.requestContext.authorizer?.tenantId || 'default';
  
  if (!userId || !clerkUserId) {
    logger.error("User ID not found in authorizer context");
    return createErrorResponse(500, "Internal server error: User ID missing from context");
  }

  logger.info("Processing KYC verification start", { 
    userId, 
    clerkUserId,
    tenantId,
    provider: validatedBody.provider,
    verificationLevel: validatedBody.verificationLevel
  });

  try {
    // Initialize KYC service
    const kycService = new KYCService(tenantId);

    // Check if user already has a pending verification
    const existingVerification = await kycService.getLatestVerificationByUser(userId);
    if (existingVerification && existingVerification.status === "IN_PROGRESS") {
      logger.warn("User already has pending verification", { 
        userId,
        existingVerificationId: existingVerification.id 
      });
      return createBadRequestResponse(
        "You already have a verification in progress",
        { verificationId: existingVerification.id }
      );
    }

    // Prepare metadata
    const metadata = {
      provider: validatedBody.provider as KYCProvider,
      verificationLevel: validatedBody.verificationLevel as KYCVerificationLevel,
      ipAddress: validatedBody.metadata?.ipAddress || event.requestContext.identity?.sourceIp,
      userAgent: validatedBody.metadata?.userAgent || event.headers?.["User-Agent"],
      deviceFingerprint: validatedBody.metadata?.deviceFingerprint,
      verificationMethod: validatedBody.metadata?.verificationMethod,
      documentTypes: validatedBody.metadata?.documentTypes,
    };

    // Initialize verification
    const verification = await kycService.initializeVerification(
      userId,
      clerkUserId,
      validatedBody.provider as KYCProvider,
      validatedBody.verificationLevel as KYCVerificationLevel,
      metadata
    );

    // Initialize provider and start verification
    let providerResponse;
    if (validatedBody.provider === "PROVE") {
      const proveProvider = await ProveProviderFactory.createProvider();

      const startRequest: StartKYCVerificationRequest = {
        provider: validatedBody.provider,
        verificationLevel: validatedBody.verificationLevel,
        individual: validatedBody.individual,
        metadata
      };

      providerResponse = await proveProvider.startVerification(startRequest);
      
      // Update verification with provider response
      await kycService.updateVerification(
        verification.id,
        userId,
        "IN_PROGRESS",
        {
          ...metadata,
          correlationId: providerResponse.correlationId,
          providerTransactionId: providerResponse.correlationId
        }
      );
    } else {
      // For other providers, return basic response
      providerResponse = {
        verificationId: verification.id,
        status: "IN_PROGRESS" as const,
        nextStep: "validate"
      };
    }

    logger.info("KYC verification started successfully", { 
      verificationId: verification.id,
      provider: validatedBody.provider,
      correlationId: providerResponse.correlationId
    });

    // Return success response
    const response = {
      verificationId: verification.id,
      correlationId: providerResponse.correlationId,
      status: verification.status,
      nextStep: providerResponse.nextStep || "validate",
      authToken: providerResponse.authToken,
      redirectUrl: providerResponse.redirectUrl,
      provider: validatedBody.provider
    };

    return createSuccessResponse(201, response);

  } catch (error) {
    logger.error("Error starting KYC verification", error as Error);
    
    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes("Provider not enabled")) {
        return createBadRequestResponse("KYC provider is not enabled");
      }
      if (error.message.includes("Invalid configuration")) {
        return createErrorResponse(500, "KYC provider configuration error");
      }
    }

    return createErrorResponse(500, "Failed to start KYC verification due to an internal error");
  }
}; 