import { DynamoDB } from "aws-sdk";
import { BaseEntity } from "./base-entity";
import { getClient, getDocumentClient } from "../client";
import { UserType } from "../../types";

export type UserStatus = "ACTIVE" | "INACTIVE" | "PENDING" | "SUSPENDED";

export class User extends BaseEntity {
  id: string;
  email: string;
  organizationId?: string;
  role: UserType;
  status: UserStatus;
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  profilePictureUrl?: string;
  lastLoginAt?: string;

  constructor(
    tenantId: string,
    id: string,
    email: string,
    role: UserType,
    status: UserStatus = "ACTIVE",
    options: {
      organizationId?: string;
      firstName?: string;
      lastName?: string;
      phoneNumber?: string;
      profilePictureUrl?: string;
      lastLoginAt?: string;
    } = {}
  ) {
    super(tenantId);
    this.id = id;
    this.email = email.toLowerCase();
    this.organizationId = options.organizationId;
    this.role = role;
    this.status = status;
    this.firstName = options.firstName;
    this.lastName = options.lastName;
    this.phoneNumber = options.phoneNumber;
    this.profilePictureUrl = options.profilePictureUrl;
    this.lastLoginAt = options.lastLoginAt;
  }

  get entityType(): string {
    return "USER";
  }

  get pk(): string {
    return `USER#${this.id}`;
  }

  get sk(): string {
    return "METADATA";
  }

  get fullName(): string {
    if (this.firstName && this.lastName) {
      return `${this.firstName} ${this.lastName}`;
    }
    if (this.firstName) return this.firstName;
    if (this.lastName) return this.lastName;
    return "";
  }

  toItem(): DynamoDB.AttributeMap {
    const item: DynamoDB.AttributeMap = {
      PK: { S: this.pk },
      SK: { S: this.sk },
      // Index for querying users by email
      EmailLookupPK: { S: `USER_EMAIL` },
      EmailLookupSK: { S: `EMAIL#${this.email}` },
      // Index for querying users by role and status
      RoleLookupPK: { S: `USER_ROLE#${this.role}` },
      RoleLookupSK: { S: `STATUS#${this.status}#${this.fullName || this.email}` },
      ...this.baseFields(),
      id: { S: this.id },
      email: { S: this.email },
      role: { S: this.role },
      status: { S: this.status },
      ...(this.firstName && { firstName: { S: this.firstName } }),
      ...(this.lastName && { lastName: { S: this.lastName } }),
      ...(this.phoneNumber && { phoneNumber: { S: this.phoneNumber } }),
      ...(this.profilePictureUrl && {
        profilePictureUrl: { S: this.profilePictureUrl },
      }),
      ...(this.lastLoginAt && { lastLoginAt: { S: this.lastLoginAt } }),
    };

    // Add organization index if user has an organization
    if (this.organizationId) {
      item.organizationId = { S: this.organizationId };
      // Add organization-based queries
      item.OrganizationLookupPK = { S: `ORG#${this.organizationId}#USERS` };
      item.OrganizationLookupSK = { S: `ROLE#${this.role}#STATUS#${this.status}#${this.fullName || this.email}` };
    }

    return item;
  }

  toDocClientItem(): Record<string, any> {
    const item: Record<string, any> = {
      PK: this.pk,
      SK: this.sk,
      // Index for querying users by email
      EmailLookupPK: `USER_EMAIL`,
      EmailLookupSK: `EMAIL#${this.email}`,
      // Index for querying users by role and status
      RoleLookupPK: `USER_ROLE#${this.role}`,
      RoleLookupSK: `STATUS#${this.status}#${this.fullName || this.email}`,
      ...this.baseDocClientFields(),
      id: this.id,
      email: this.email,
      role: this.role,
      status: this.status,
      ...(this.firstName && { firstName: this.firstName }),
      ...(this.lastName && { lastName: this.lastName }),
      ...(this.phoneNumber && { phoneNumber: this.phoneNumber }),
      ...(this.profilePictureUrl && {
        profilePictureUrl: this.profilePictureUrl,
      }),
      ...(this.lastLoginAt && { lastLoginAt: this.lastLoginAt }),
    };

    // Add organization index if user has an organization
    if (this.organizationId) {
      item.organizationId = this.organizationId;
      item.OrganizationLookupPK = `ORG#${this.organizationId}#USERS`;
      item.OrganizationLookupSK = `ROLE#${this.role}#STATUS#${this.status}#${this.fullName || this.email}`;
    }

    return item;
  }

  static fromItem(item?: DynamoDB.AttributeMap): User {
    if (!item) throw new Error("No user item found!");

    return new User(
      item.tenantId.S!,
      item.id.S!,
      item.email.S!,
      item.role.S as UserType,
      item.status.S as UserStatus,
      {
        organizationId: item.organizationId?.S,
        firstName: item.firstName?.S,
        lastName: item.lastName?.S,
        phoneNumber: item.phoneNumber?.S,
        profilePictureUrl: item.profilePictureUrl?.S,
        lastLoginAt: item.lastLoginAt?.S,
      }
    );
  }

  static fromDocClientItem(item?: Record<string, any>): User {
    if (!item) throw new Error("No user item found!");

    return new User(
      item.tenantId,
      item.id,
      item.email,
      item.role as UserType,
      item.status as UserStatus,
      {
        organizationId: item.organizationId,
        firstName: item.firstName,
        lastName: item.lastName,
        phoneNumber: item.phoneNumber,
        profilePictureUrl: item.profilePictureUrl,
        lastLoginAt: item.lastLoginAt,
      }
    );
  }

  // Update last login timestamp
  async updateLastLogin(): Promise<User> {
    const timestamp = new Date().toISOString();
    this.lastLoginAt = timestamp;
    this.updatedAt = timestamp;
    return this.update();
  }

  // Get user by ID
  static async getById(userId: string): Promise<User> {
    const client = getDocumentClient();
    const pk = `USER#${userId}`;
    const sk = "METADATA";

    try {
      const response = await client
        .get({
          TableName: process.env.TABLE_NAME!,
          Key: { PK: pk, SK: sk },
        })
        .promise();

      if (!response.Item) {
        throw new Error(`User not found: ${userId}`);
      }

      return User.fromDocClientItem(response.Item);
    } catch (error) {
      console.error("Error getting user by ID:", error);
      throw error;
    }
  }

  // Get user by email
  static async getByEmail(email: string): Promise<User | null> {
    const client = getDocumentClient();
    const normalizedEmail = email.toLowerCase();

    try {
      const response = await client
        .query({
          TableName: process.env.TABLE_NAME!,
          IndexName: "EmailLookupIndex",
          KeyConditionExpression: "EmailLookupPK = :emailLookupPK AND EmailLookupSK = :emailLookupSK",
          ExpressionAttributeValues: {
            ":emailLookupPK": `USER_EMAIL`,
            ":emailLookupSK": `EMAIL#${normalizedEmail}`,
          },
          Limit: 1,
        })
        .promise();

      if (!response.Items || response.Items.length === 0) {
        return null;
      }

      return User.fromDocClientItem(response.Items[0]);
    } catch (error) {
      console.error("Error getting user by email:", error);
      throw error;
    }
  }

  // List users by organization
  static async listByOrganization(
    organizationId: string,
    role?: UserType,
    status: UserStatus = "ACTIVE"
  ): Promise<User[]> {
    const client = getDocumentClient();

    try {
      const keyCondition = role
        ? "OrganizationLookupPK = :organizationLookupPK AND begins_with(OrganizationLookupSK, :prefix)"
        : "OrganizationLookupPK = :organizationLookupPK AND begins_with(OrganizationLookupSK, :statusPrefix)";

      const expressionValues: Record<string, any> = {
        ":organizationLookupPK": `ORG#${organizationId}#USERS`,
      };

      if (role) {
        expressionValues[":prefix"] = `ROLE#${role}#STATUS#${status}`;
      } else {
        expressionValues[":statusPrefix"] = `ROLE#`;

        if (status) {
          keyCondition + " AND contains(OrganizationLookupSK, :status)";
          expressionValues[":status"] = `STATUS#${status}`;
        }
      }

      const response = await client
        .query({
          TableName: process.env.TABLE_NAME!,
          IndexName: "OrganizationLookupIndex",
          KeyConditionExpression: keyCondition,
          ExpressionAttributeValues: expressionValues,
        })
        .promise();

      return (response.Items || []).map((item) => User.fromDocClientItem(item));
    } catch (error) {
      console.error("Error listing users by organization:", error);
      throw error;
    }
  }
}

// Convenience function to create a new user
export const createUser = async (user: User): Promise<User> => {
  return user.create();
};

// Convenience function to get a user by ID
export const getUser = async (userId: string): Promise<User> => {
  return User.getById(userId);
};

// Convenience function to get a user by email
export const getUserByEmail = async (email: string): Promise<User | null> => {
  return User.getByEmail(email);
};

// Convenience function to list users by organization
export const listUsersByOrganization = async (
  organizationId: string,
  role?: UserType,
  status: UserStatus = "ACTIVE"
): Promise<User[]> => {
  return User.listByOrganization(organizationId, role, status);
};
