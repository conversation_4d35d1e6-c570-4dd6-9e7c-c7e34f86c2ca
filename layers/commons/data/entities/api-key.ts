import { DynamoDB } from "aws-sdk";
import { BaseEntity } from "./base-entity";
import { UniquenessConstraint } from "../uniqueness-constraints";
import { generateUUIDv7 } from "../../utils/uuid";
import { 
  KEY_PREFIXES,
  ENTITY_TYPES,
  SORT_KEYS,
  ATTRIBUTE_NAMES,
  INDEX_NAMES,
  buildApiKeyKey,
  buildOrgKey,
  buildApiKeyHashGSIKey,
  buildApiKeyPrefixGSIKey,
  buildStatusGSIKey
} from "../../constants/db-keys";

const getTableName = (): string => process.env.TABLE_NAME || "";

export type APIKeyStatus = 'ACTIVE' | 'REVOKED' | 'EXPIRED' | 'SUSPENDED';

export type APIKeyType = 'LIVE' | 'TEST';

export interface APIKeyRateLimit {
  requestsPerSecond?: number;
  requestsPerMinute?: number;
  requestsPerHour?: number;
  requestsPerDay?: number;
}

export interface APIKeyPermissions {
  allowedEndpoints?: string[];
  allowedMethods?: string[];
  ipWhitelist?: string[];
  customPermissions?: Record<string, any>;
}

export interface APIKeyMetadata {
  lastUsedAt?: string;
  lastUsedIp?: string;
  lastUsedUserAgent?: string;
  usageCount?: number;
  createdByUserId?: string;
  createdByEmail?: string;
  revokedByUserId?: string;
  revokedAt?: string;
  revokedReason?: string;
}

/**
 * API Key entity for partner/organization API authentication
 */
export class APIKey extends BaseEntity {
  id: string;
  organizationId: string;
  keyPrefix: string; // First 8 chars for display: ckpl_xxxx
  keyHash: string; // SHA-256 hash of the full key
  label?: string; // User-defined name/description
  type: APIKeyType;
  status: APIKeyStatus;
  expiresAt?: string; // Optional expiration date
  rateLimit?: APIKeyRateLimit;
  permissions?: APIKeyPermissions;
  metadata?: APIKeyMetadata;

  constructor(
    tenantId: string,
    organizationId: string,
    keyHash: string,
    keyPrefix: string,
    type: APIKeyType = 'LIVE',
    options: {
      id?: string;
      label?: string;
      status?: APIKeyStatus;
      expiresAt?: string;
      rateLimit?: APIKeyRateLimit;
      permissions?: APIKeyPermissions;
      metadata?: APIKeyMetadata;
      createdAt?: string;
      updatedAt?: string;
    } = {}
  ) {
    super(tenantId);
    this.id = options.id || generateUUIDv7();
    this.organizationId = organizationId;
    this.keyHash = keyHash;
    this.keyPrefix = keyPrefix;
    this.type = type;
    this.label = options.label;
    this.status = options.status || 'ACTIVE';
    this.expiresAt = options.expiresAt;
    this.rateLimit = options.rateLimit;
    this.permissions = options.permissions;
    this.metadata = options.metadata;
    
    if (options.createdAt) this.createdAt = options.createdAt;
    if (options.updatedAt) this.updatedAt = options.updatedAt;
  }

  get entityType(): string {
    return ENTITY_TYPES.APIKEY;
  }

  get pk(): string {
    return buildApiKeyKey(this.tenantId, this.organizationId, this.id);
  }

  get sk(): string {
    return SORT_KEYS.METADATA;
  }

  /**
   * Get uniqueness constraints for this API key
   */
  getUniquenessConstraints(): UniquenessConstraint[] {
    const constraints: UniquenessConstraint[] = [];

    // Ensure key hash is globally unique
    constraints.push({
      constraintType: "APIKEY_HASH",
      constraintKey: this.keyHash,
      entityPK: this.pk,
      entitySK: this.sk,
      entityType: this.entityType,
      organizationId: this.organizationId,
      tenantId: this.tenantId,
    });

    // Index by prefix for quick lookup
    constraints.push({
      constraintType: "APIKEY_PREFIX",
      constraintKey: this.keyPrefix,
      entityPK: this.pk,
      entitySK: this.sk,
      entityType: this.entityType,
      organizationId: this.organizationId,
      tenantId: this.tenantId,
    });

    return constraints;
  }

  /**
   * Check if the API key is expired
   */
  get isExpired(): boolean {
    if (!this.expiresAt) return false;
    return new Date(this.expiresAt) < new Date();
  }

  /**
   * Check if the API key is valid for use
   */
  get isValid(): boolean {
    return this.status === 'ACTIVE' && !this.isExpired;
  }

  /**
   * Get display name for the API key
   */
  get displayName(): string {
    return this.label || `${this.type} Key (${this.keyPrefix}****)`;
  }

  /**
   * Revoke the API key
   */
  revoke(userId: string, reason?: string): void {
    this.status = 'REVOKED';
    this.metadata = {
      ...this.metadata,
      revokedByUserId: userId,
      revokedAt: new Date().toISOString(),
      revokedReason: reason,
    };
    this.updateTimestamp();
  }

  /**
   * Update usage metadata
   */
  updateUsage(ip?: string, userAgent?: string): void {
    const now = new Date().toISOString();
    this.metadata = {
      ...this.metadata,
      lastUsedAt: now,
      lastUsedIp: ip,
      lastUsedUserAgent: userAgent,
      usageCount: (this.metadata?.usageCount || 0) + 1,
    };
    this.updateTimestamp();
  }

  toItem(): DynamoDB.AttributeMap {
    const item: DynamoDB.AttributeMap = {
      ...this.baseFields(),
      [ATTRIBUTE_NAMES.PK]: { S: this.pk },
      [ATTRIBUTE_NAMES.SK]: { S: this.sk },
      id: { S: this.id },
      [ATTRIBUTE_NAMES.ORGANIZATION_ID]: { S: this.organizationId },
      [ATTRIBUTE_NAMES.KEY_PREFIX]: { S: this.keyPrefix },
      [ATTRIBUTE_NAMES.KEY_HASH]: { S: this.keyHash },
      [ATTRIBUTE_NAMES.TYPE]: { S: this.type },
      [ATTRIBUTE_NAMES.STATUS]: { S: this.status },
      [ATTRIBUTE_NAMES.ENTITY_TYPE]: { S: this.entityType },
      
      // GSI attributes
      [ATTRIBUTE_NAMES.GSI1PK]: { S: `${buildOrgKey(this.tenantId, this.organizationId)}#${KEY_PREFIXES.APIKEYS}` },
      [ATTRIBUTE_NAMES.GSI1SK]: { S: buildStatusGSIKey(this.status, this.createdAt) },
      [ATTRIBUTE_NAMES.GSI2PK]: { S: buildApiKeyPrefixGSIKey(this.keyPrefix) },
      [ATTRIBUTE_NAMES.GSI2SK]: { S: SORT_KEYS.METADATA },
      [ATTRIBUTE_NAMES.GSI3PK]: { S: buildApiKeyHashGSIKey(this.keyHash) },
      [ATTRIBUTE_NAMES.GSI3SK]: { S: SORT_KEYS.METADATA },
    };

    if (this.label) item[ATTRIBUTE_NAMES.LABEL] = { S: this.label };
    if (this.expiresAt) item[ATTRIBUTE_NAMES.EXPIRES_AT] = { S: this.expiresAt };
    if (this.rateLimit) {
      item[ATTRIBUTE_NAMES.RATE_LIMIT] = {
        M: Object.entries(this.rateLimit).reduce((acc, [key, value]) => {
          acc[key] = { N: value.toString() };
          return acc;
        }, {} as DynamoDB.AttributeMap),
      };
    }
    if (this.permissions) {
      item[ATTRIBUTE_NAMES.PERMISSIONS] = {
        M: {
          ...(this.permissions.allowedEndpoints && {
            allowedEndpoints: {
              L: this.permissions.allowedEndpoints.map(e => ({ S: e })),
            },
          }),
          ...(this.permissions.allowedMethods && {
            allowedMethods: {
              L: this.permissions.allowedMethods.map(m => ({ S: m })),
            },
          }),
          ...(this.permissions.ipWhitelist && {
            ipWhitelist: {
              L: this.permissions.ipWhitelist.map(ip => ({ S: ip })),
            },
          }),
          ...(this.permissions.customPermissions && {
            customPermissions: {
              M: Object.entries(this.permissions.customPermissions).reduce((acc, [k, v]) => {
                acc[k] = { S: JSON.stringify(v) };
                return acc;
              }, {} as DynamoDB.AttributeMap),
            },
          }),
        },
      };
    }
    if (this.metadata) {
      item[ATTRIBUTE_NAMES.METADATA] = {
        M: Object.entries(this.metadata).reduce((acc, [key, value]) => {
          if (typeof value === 'number') {
            acc[key] = { N: value.toString() };
          } else if (typeof value === 'string') {
            acc[key] = { S: value };
          }
          return acc;
        }, {} as DynamoDB.AttributeMap),
      };
    }

    return item;
  }

  toDocClientItem(): Record<string, any> {
    const item: Record<string, any> = {
      ...this.baseDocClientFields(),
      [ATTRIBUTE_NAMES.PK]: this.pk,
      [ATTRIBUTE_NAMES.SK]: this.sk,
      id: this.id,
      [ATTRIBUTE_NAMES.ORGANIZATION_ID]: this.organizationId,
      [ATTRIBUTE_NAMES.KEY_PREFIX]: this.keyPrefix,
      [ATTRIBUTE_NAMES.KEY_HASH]: this.keyHash,
      [ATTRIBUTE_NAMES.TYPE]: this.type,
      [ATTRIBUTE_NAMES.STATUS]: this.status,
      [ATTRIBUTE_NAMES.ENTITY_TYPE]: this.entityType,
      
      // GSI attributes
      [ATTRIBUTE_NAMES.GSI1PK]: `${buildOrgKey(this.tenantId, this.organizationId)}#${KEY_PREFIXES.APIKEYS}`,
      [ATTRIBUTE_NAMES.GSI1SK]: buildStatusGSIKey(this.status, this.createdAt),
      [ATTRIBUTE_NAMES.GSI2PK]: buildApiKeyPrefixGSIKey(this.keyPrefix),
      [ATTRIBUTE_NAMES.GSI2SK]: SORT_KEYS.METADATA,
      [ATTRIBUTE_NAMES.GSI3PK]: buildApiKeyHashGSIKey(this.keyHash),
      [ATTRIBUTE_NAMES.GSI3SK]: SORT_KEYS.METADATA,
    };

    if (this.label) item[ATTRIBUTE_NAMES.LABEL] = this.label;
    if (this.expiresAt) item[ATTRIBUTE_NAMES.EXPIRES_AT] = this.expiresAt;
    if (this.rateLimit) item[ATTRIBUTE_NAMES.RATE_LIMIT] = this.rateLimit;
    if (this.permissions) item[ATTRIBUTE_NAMES.PERMISSIONS] = this.permissions;
    if (this.metadata) item[ATTRIBUTE_NAMES.METADATA] = this.metadata;

    return item;
  }

  static fromItem(item?: DynamoDB.AttributeMap): APIKey {
    if (!item) {
      throw new Error("Cannot create APIKey from undefined item");
    }

    const tenantId = item[ATTRIBUTE_NAMES.TENANT_ID]?.S;
    const organizationId = item[ATTRIBUTE_NAMES.ORGANIZATION_ID]?.S;
    const keyHash = item[ATTRIBUTE_NAMES.KEY_HASH]?.S;
    const keyPrefix = item[ATTRIBUTE_NAMES.KEY_PREFIX]?.S;

    if (!tenantId || !organizationId || !keyHash || !keyPrefix) {
      throw new Error("Missing required fields for APIKey");
    }

    const apiKey = new APIKey(
      tenantId,
      organizationId,
      keyHash,
      keyPrefix,
      (item[ATTRIBUTE_NAMES.TYPE]?.S as APIKeyType) || 'LIVE',
      {
        id: item.id?.S,
        label: item[ATTRIBUTE_NAMES.LABEL]?.S,
        status: (item[ATTRIBUTE_NAMES.STATUS]?.S as APIKeyStatus) || 'ACTIVE',
        expiresAt: item[ATTRIBUTE_NAMES.EXPIRES_AT]?.S,
        createdAt: item[ATTRIBUTE_NAMES.CREATED_AT]?.S,
        updatedAt: item[ATTRIBUTE_NAMES.UPDATED_AT]?.S,
      }
    );

    // Parse rate limit
    const rateLimitMap = item[ATTRIBUTE_NAMES.RATE_LIMIT]?.M;
    if (rateLimitMap) {
      apiKey.rateLimit = Object.entries(rateLimitMap).reduce((acc, [key, value]) => {
        if (value.N) {
          acc[key as keyof APIKeyRateLimit] = parseInt(value.N, 10);
        }
        return acc;
      }, {} as APIKeyRateLimit);
    }

    // Parse permissions
    const permissionsAttributeMap = item[ATTRIBUTE_NAMES.PERMISSIONS]?.M;
    if (permissionsAttributeMap) {
      const perms: APIKeyPermissions = {};
      
      if (permissionsAttributeMap.allowedEndpoints?.L) {
        perms.allowedEndpoints = permissionsAttributeMap.allowedEndpoints.L
          .map(v => v.S)
          .filter(Boolean) as string[];
      }
      if (permissionsAttributeMap.allowedMethods?.L) {
        perms.allowedMethods = permissionsAttributeMap.allowedMethods.L
          .map(v => v.S)
          .filter(Boolean) as string[];
      }
      if (permissionsAttributeMap.ipWhitelist?.L) {
        perms.ipWhitelist = permissionsAttributeMap.ipWhitelist.L
          .map(v => v.S)
          .filter(Boolean) as string[];
      }
      if (permissionsAttributeMap.customPermissions?.M) {
        perms.customPermissions = Object.entries(permissionsAttributeMap.customPermissions.M)
          .reduce((acc, [k, v]) => {
            if (v.S) {
              try {
                acc[k] = JSON.parse(v.S);
              } catch {
                acc[k] = v.S;
              }
            }
            return acc;
          }, {} as Record<string, any>);
      }
      apiKey.permissions = perms;
    }

    // Parse metadata
    const metadataMap = item[ATTRIBUTE_NAMES.METADATA]?.M;
    if (metadataMap) {
      apiKey.metadata = Object.entries(metadataMap).reduce((acc, [key, value]) => {
        if (value.N) {
          acc[key as keyof APIKeyMetadata] = parseInt(value.N, 10) as any;
        } else if (value.S) {
          acc[key as keyof APIKeyMetadata] = value.S as any;
        }
        return acc;
      }, {} as APIKeyMetadata);
    }

    return apiKey;
  }

  static fromDocClientItem(item?: Record<string, any>): APIKey {
    if (!item) {
      throw new Error("Cannot create APIKey from undefined item");
    }

    const { tenantId, organizationId, keyHash, keyPrefix } = item;

    if (!tenantId || !organizationId || !keyHash || !keyPrefix) {
      throw new Error("Missing required fields for APIKey");
    }

    return new APIKey(
      tenantId,
      organizationId,
      keyHash,
      keyPrefix,
      item.type || 'LIVE',
      {
        id: item.id,
        label: item.label,
        status: item.status || 'ACTIVE',
        expiresAt: item.expiresAt,
        rateLimit: item.rateLimit,
        permissions: item.permissions,
        metadata: item.metadata,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      }
    );
  }

  /**
   * Get API key by ID
   */
  static async getById(tenantId: string, organizationId: string, keyId: string): Promise<APIKey> {
    const client = new DynamoDB();
    const params: DynamoDB.GetItemInput = {
      TableName: getTableName(),
      Key: {
        [ATTRIBUTE_NAMES.PK]: { S: buildApiKeyKey(tenantId, organizationId, keyId) },
        [ATTRIBUTE_NAMES.SK]: { S: SORT_KEYS.METADATA },
      },
    };

    const result = await client.getItem(params).promise();
    if (!result.Item) {
      throw new Error(`APIKey not found: ${keyId}`);
    }

    return APIKey.fromItem(result.Item);
  }

  /**
   * Get API key by hash (for validation)
   */
  static async getByHash(keyHash: string): Promise<APIKey | null> {
    const client = new DynamoDB();
    const params: DynamoDB.QueryInput = {
      TableName: getTableName(),
      IndexName: INDEX_NAMES.GSI3,
      KeyConditionExpression: `${ATTRIBUTE_NAMES.GSI3PK} = :pk AND ${ATTRIBUTE_NAMES.GSI3SK} = :sk`,
      ExpressionAttributeValues: {
        ":pk": { S: buildApiKeyHashGSIKey(keyHash) },
        ":sk": { S: SORT_KEYS.METADATA },
      },
      Limit: 1,
    };

    const result = await client.query(params).promise();
    if (!result.Items || result.Items.length === 0) {
      return null;
    }

    return APIKey.fromItem(result.Items[0]);
  }

  /**
   * List API keys for an organization
   */
  static async listByOrganization(
    tenantId: string,
    organizationId: string,
    status?: APIKeyStatus,
    limit: number = 50
  ): Promise<APIKey[]> {
    const client = new DynamoDB();
    const params: DynamoDB.QueryInput = {
      TableName: getTableName(),
      IndexName: INDEX_NAMES.GSI1,
      KeyConditionExpression: `${ATTRIBUTE_NAMES.GSI1PK} = :pk`,
      ExpressionAttributeValues: {
        ":pk": { S: `${buildOrgKey(tenantId, organizationId)}#${KEY_PREFIXES.APIKEYS}` },
      },
      Limit: limit,
    };

    if (status) {
      params.FilterExpression = `#status = :status`;
      params.ExpressionAttributeNames = { "#status": ATTRIBUTE_NAMES.STATUS };
      params.ExpressionAttributeValues![":status"] = { S: status };
    }

    const result = await client.query(params).promise();
    return (result.Items || []).map(item => APIKey.fromItem(item));
  }

  /**
   * Create API key with uniqueness constraints
   */
  async create(): Promise<this> {
    const constraints = this.getUniquenessConstraints();
    const { createEntityWithConstraints } = await import("../uniqueness-constraints");
    
    await createEntityWithConstraints(this.toItem(), constraints);
    return this;
  }

  /**
   * Delete API key and remove constraints
   */
  async delete(): Promise<void> {
    const constraints = this.getUniquenessConstraints();
    const { removeConstraints } = await import("../uniqueness-constraints");
    
    await super.delete();
    await removeConstraints(constraints);
  }
}

// Helper functions for external use
export const createAPIKey = async (apiKey: APIKey): Promise<APIKey> => {
  return apiKey.create();
};

export const getAPIKey = async (
  tenantId: string,
  organizationId: string,
  keyId: string
): Promise<APIKey> => {
  return APIKey.getById(tenantId, organizationId, keyId);
};

export const getAPIKeyByHash = async (keyHash: string): Promise<APIKey | null> => {
  return APIKey.getByHash(keyHash);
};

export const listAPIKeysByOrganization = async (
  tenantId: string,
  organizationId: string,
  status?: APIKeyStatus,
  limit?: number
): Promise<APIKey[]> => {
  return APIKey.listByOrganization(tenantId, organizationId, status, limit);
};