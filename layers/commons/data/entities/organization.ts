import { DynamoDB } from "aws-sdk";
import { BaseEntity } from "./base-entity";
import { getClient, getDocumentClient } from "../client";
import { v4 as uuidv4 } from "uuid";
import { Logger } from "@aws-lambda-powertools/logger";
import { 
  KEY_PREFIXES,
  ENTITY_TYPES,
  SORT_KEYS,
  ATTRIBUTE_NAMES,
  INDEX_NAMES,
  buildTenantKey,
  buildOrgKey,
  buildLicenseGSIKey,
  buildTypeGSIKey
} from "../../constants/db-keys";

export type OrganizationType =
  | "AUTHORITY"
  | "SERVICE_PROVIDER"
  | "PROPERTY_MANAGEMENT";

export type OrganizationStatus =
  | "ACTIVE"
  | "INACTIVE"
  | "PENDING"
  | "SUSPENDED";

const logger = new Logger({ serviceName: "organization-service" });
const getTableName = (): string => process.env.TABLE_NAME || "";

/**
 * Organization class for representing different types of organizations in FireGuard
 * This class follows DynamoDB single-table design principles with appropriate
 * access patterns
 */
export interface ContactInfo {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
}

export class Organization extends BaseEntity {
  id: string;
  name: string;
  type: OrganizationType;
  status: OrganizationStatus;
  description?: string;
  address?: string;
  contactInfo?: ContactInfo;
  contactEmail?: string;
  contactPhone?: string;
  website?: string;
  logoUrl?: string;
  licenseNumber?: string;
  additionalDetails?: string;

  constructor(
    tenantId: string,
    id: string,
    name: string,
    type: OrganizationType,
    status: OrganizationStatus = "ACTIVE",
    options: {
      description?: string;
      address?: string;
      contactInfo?: ContactInfo;
      contactEmail?: string;
      contactPhone?: string;
      website?: string;
      logoUrl?: string;
      licenseNumber?: string;
      additionalDetails?: string;
    } = {}
  ) {
    super(tenantId);
    this.id = id || uuidv4();
    this.name = name;
    this.type = type;
    this.status = status;
    this.description = options.description;
    this.address = options.address;
    this.contactInfo = options.contactInfo;
    this.contactEmail = options.contactEmail;
    this.contactPhone = options.contactPhone;
    this.website = options.website;
    this.logoUrl = options.logoUrl;
    this.licenseNumber = options.licenseNumber;
    this.additionalDetails = options.additionalDetails;
  }

  /**
   * Returns the entity type for this organization
   */
  get entityType(): string {
    return ENTITY_TYPES.ORGANIZATION;
  }

  /**
   * Returns the partition key for this organization
   * Access Pattern #6: Get organization by ID
   * PK: TENANT#{tenantId}, SK: ORG#{orgId}
   */
  get pk(): string {
    return buildTenantKey(this.tenantId);
  }

  /**
   * Returns the sort key for this organization
   * Access Pattern #6: Get organization by ID
   * PK: TENANT#{tenantId}, SK: ORG#{orgId}
   */
  get sk(): string {
    return `${KEY_PREFIXES.ORG}${this.id}`;
  }

  /**
   * Converts this object to a DynamoDB item with appropriate AttributeMap format for the raw DynamoDB client
   */
  toItem(): DynamoDB.AttributeMap {
    const item: DynamoDB.AttributeMap = {
      ...this.baseFields(),
      [ATTRIBUTE_NAMES.PK]: { S: this.pk },
      [ATTRIBUTE_NAMES.SK]: { S: this.sk },
      id: { S: this.id },
      [ATTRIBUTE_NAMES.NAME]: { S: this.name },
      [ATTRIBUTE_NAMES.TYPE]: { S: this.type },
      [ATTRIBUTE_NAMES.STATUS]: { S: this.status },
      ...(this.description && {
        [ATTRIBUTE_NAMES.DESCRIPTION]: { S: this.description },
      }),
      ...(this.address && {
        [ATTRIBUTE_NAMES.ADDRESS]: { S: this.address },
      }),
      ...(this.contactEmail && {
        [ATTRIBUTE_NAMES.CONTACT_EMAIL]: { S: this.contactEmail },
      }),
      ...(this.contactPhone && {
        [ATTRIBUTE_NAMES.CONTACT_PHONE]: { S: this.contactPhone },
      }),
      ...(this.website && {
        website: { S: this.website },
      }),
      ...(this.logoUrl && {
        logoUrl: { S: this.logoUrl },
      }),
      ...(this.additionalDetails && {
        additionalDetails: { S: this.additionalDetails },
      }),
    };

    // Add contactInfo if it exists
    if (this.contactInfo) {
      item[ATTRIBUTE_NAMES.CONTACT_INFO] = {
        M: {
          ...(this.contactInfo.name && { name: { S: this.contactInfo.name } }),
          ...(this.contactInfo.email && {
            email: { S: this.contactInfo.email },
          }),
          ...(this.contactInfo.phone && {
            phone: { S: this.contactInfo.phone },
          }),
          ...(this.contactInfo.address && {
            address: { S: this.contactInfo.address },
          }),
        },
      };
    }

    // Add Type Lookup for Access Pattern #7: List organizations by type
    // PK: TENANT#{tenantId}, SK: TYPE#{type}#ORG#{orgId}
    item[ATTRIBUTE_NAMES.GSI1PK] = { S: buildTenantKey(this.tenantId) };
    item[ATTRIBUTE_NAMES.GSI1SK] = {
      S: `${KEY_PREFIXES.TYPE}${this.type}#${KEY_PREFIXES.ORG}${this.id}`,
    };

    // Add License Lookup for Access Pattern #9: Get organization by license (if license provided)
    if (this.licenseNumber) {
      item[ATTRIBUTE_NAMES.GSI2PK] = { S: buildLicenseGSIKey(this.licenseNumber) };
      item[ATTRIBUTE_NAMES.GSI2SK] = { S: buildTenantKey(this.tenantId) };
      item[ATTRIBUTE_NAMES.LICENSE_NUMBER] = { S: this.licenseNumber };
    }

    return item;
  }

  /**
   * Converts this object to a plain JavaScript object for the DocumentClient
   */
  toDocClientItem(): Record<string, any> {
    const item: Record<string, any> = {
      ...this.baseDocClientFields(),
      [ATTRIBUTE_NAMES.PK]: this.pk,
      [ATTRIBUTE_NAMES.SK]: this.sk,
      id: this.id,
      [ATTRIBUTE_NAMES.NAME]: this.name,
      [ATTRIBUTE_NAMES.TYPE]: this.type,
      [ATTRIBUTE_NAMES.STATUS]: this.status,
    };

    // Add optional fields if they exist
    if (this.description) item[ATTRIBUTE_NAMES.DESCRIPTION] = this.description;
    if (this.address) item[ATTRIBUTE_NAMES.ADDRESS] = this.address;
    if (this.contactEmail) item[ATTRIBUTE_NAMES.CONTACT_EMAIL] = this.contactEmail;
    if (this.contactPhone) item[ATTRIBUTE_NAMES.CONTACT_PHONE] = this.contactPhone;
    if (this.website) item.website = this.website;
    if (this.logoUrl) item.logoUrl = this.logoUrl;
    if (this.additionalDetails) item.additionalDetails = this.additionalDetails;

    // Add contactInfo if it exists
    if (this.contactInfo) {
      item[ATTRIBUTE_NAMES.CONTACT_INFO] = {
        ...(this.contactInfo.name && { name: this.contactInfo.name }),
        ...(this.contactInfo.email && { email: this.contactInfo.email }),
        ...(this.contactInfo.phone && { phone: this.contactInfo.phone }),
        ...(this.contactInfo.address && { address: this.contactInfo.address }),
      };
    }

    // Add Type Lookup for Access Pattern #7: List organizations by type
    item[ATTRIBUTE_NAMES.GSI1PK] = buildTenantKey(this.tenantId);
    item[ATTRIBUTE_NAMES.GSI1SK] = `${KEY_PREFIXES.TYPE}${this.type}#${KEY_PREFIXES.ORG}${this.id}`;

    // Add License Lookup for Access Pattern #9: Get organization by license (if license provided)
    if (this.licenseNumber) {
      item[ATTRIBUTE_NAMES.GSI2PK] = buildLicenseGSIKey(this.licenseNumber);
      item[ATTRIBUTE_NAMES.GSI2SK] = buildTenantKey(this.tenantId);
      item[ATTRIBUTE_NAMES.LICENSE_NUMBER] = this.licenseNumber;
    }

    return item;
  }

  /**
   * Creates an Organization object from a DynamoDB item
   * @param item DynamoDB item
   * @returns Organization object
   */
  static fromItem(item?: DynamoDB.AttributeMap): Organization {
    if (!item) throw new Error("No organization item found!");

    try {
      // Add validation for required fields
      const id = item.id?.S;
      const name = item[ATTRIBUTE_NAMES.NAME]?.S;
      const type = item[ATTRIBUTE_NAMES.TYPE]?.S as OrganizationType;
      const status = item[ATTRIBUTE_NAMES.STATUS]?.S as OrganizationStatus;
      const tenantId = item[ATTRIBUTE_NAMES.TENANT_ID]?.S;

      if (!id) throw new Error("Invalid item: Missing or invalid id");
      if (!name) throw new Error("Invalid item: Missing or invalid name");
      if (
        !type ||
        !["AUTHORITY", "SERVICE_PROVIDER", "PROPERTY_MANAGEMENT"].includes(type)
      ) {
        throw new Error("Invalid item: Missing or invalid type");
      }
      if (
        !status ||
        !["ACTIVE", "INACTIVE", "PENDING", "SUSPENDED"].includes(status)
      ) {
        throw new Error("Invalid item: Missing or invalid status");
      }
      if (!tenantId)
        throw new Error("Invalid item: Missing or invalid tenantId");

      // Extract contactInfo if it exists
      let contactInfo: ContactInfo | undefined;
      const contactInfoMap = item[ATTRIBUTE_NAMES.CONTACT_INFO]?.M;
      if (contactInfoMap) {
        contactInfo = {
          name: contactInfoMap.name?.S,
          email: contactInfoMap.email?.S,
          phone: contactInfoMap.phone?.S,
          address: contactInfoMap.address?.S,
        };
      }

      return new Organization(tenantId, id, name, type, status, {
        // Optional fields with safe access
        description: item[ATTRIBUTE_NAMES.DESCRIPTION]?.S,
        address: item[ATTRIBUTE_NAMES.ADDRESS]?.S,
        contactEmail: item[ATTRIBUTE_NAMES.CONTACT_EMAIL]?.S,
        contactPhone: item[ATTRIBUTE_NAMES.CONTACT_PHONE]?.S,
        website: item.website?.S,
        logoUrl: item.logoUrl?.S,
        licenseNumber: item[ATTRIBUTE_NAMES.LICENSE_NUMBER]?.S,
        additionalDetails: item.additionalDetails?.S,
        contactInfo,
      });
    } catch (error) {
      // Catch specific validation errors or log/rethrow generic errors
      if (error instanceof Error && error.message.startsWith("Invalid item:")) {
        logger.error("Error creating organization from invalid item", {
          validationError: error.message,
          item,
        });
        throw error; // Re-throw validation error
      }
      // Log original error if it wasn't one of our specific validation errors
      logger.error("Unexpected error creating organization from item", {
        error,
        item,
      });
      // Throw a generic error or the original one
      throw new Error(
        `Failed to create organization from item: ${error instanceof Error ? error.message : error}`
      );
    }
  }

  /**
   * Creates an Organization object from a DocumentClient item
   * @param item DocumentClient item (plain JS object)
   * @returns Organization object
   */
  static fromDocClientItem(item?: Record<string, any>): Organization {
    if (!item) throw new Error("No organization item found!");

    try {
      // Add validation for required fields
      const id = item.id;
      const name = item[ATTRIBUTE_NAMES.NAME];
      const type = item[ATTRIBUTE_NAMES.TYPE] as OrganizationType;
      const status = item[ATTRIBUTE_NAMES.STATUS] as OrganizationStatus;
      const tenantId = item[ATTRIBUTE_NAMES.TENANT_ID];

      if (!id) throw new Error("Invalid item: Missing or invalid id");
      if (!name) throw new Error("Invalid item: Missing or invalid name");
      if (
        !type ||
        !["AUTHORITY", "SERVICE_PROVIDER", "PROPERTY_MANAGEMENT"].includes(type)
      ) {
        throw new Error("Invalid item: Missing or invalid type");
      }
      if (
        !status ||
        !["ACTIVE", "INACTIVE", "PENDING", "SUSPENDED"].includes(status)
      ) {
        throw new Error("Invalid item: Missing or invalid status");
      }
      if (!tenantId)
        throw new Error("Invalid item: Missing or invalid tenantId");

      // Extract contactInfo if it exists
      let contactInfo: ContactInfo | undefined;
      const contactInfoData = item[ATTRIBUTE_NAMES.CONTACT_INFO];
      if (contactInfoData) {
        contactInfo = {
          name: contactInfoData.name,
          email: contactInfoData.email,
          phone: contactInfoData.phone,
          address: contactInfoData.address,
        };
      }

      return new Organization(tenantId, id, name, type, status, {
        // Optional fields with safe access
        description: item[ATTRIBUTE_NAMES.DESCRIPTION],
        address: item[ATTRIBUTE_NAMES.ADDRESS],
        contactEmail: item[ATTRIBUTE_NAMES.CONTACT_EMAIL],
        contactPhone: item[ATTRIBUTE_NAMES.CONTACT_PHONE],
        website: item.website,
        logoUrl: item.logoUrl,
        licenseNumber: item[ATTRIBUTE_NAMES.LICENSE_NUMBER],
        additionalDetails: item.additionalDetails,
        contactInfo,
      });
    } catch (error) {
      // Catch specific validation errors or log/rethrow generic errors
      if (error instanceof Error && error.message.startsWith("Invalid item:")) {
        logger.error("Error creating organization from invalid item", {
          validationError: error.message,
          item,
        });
        throw error; // Re-throw validation error
      }
      // Log original error if it wasn't one of our specific validation errors
      logger.error("Unexpected error creating organization from item", {
        error,
        item,
      });
      // Throw a generic error or the original one
      throw new Error(
        `Failed to create organization from item: ${error instanceof Error ? error.message : error}`
      );
    }
  }

  /**
   * Get organization by ID
   * Access Pattern #6: Get organization by ID
   * @param tenantId Tenant ID
   * @param organizationId Organization ID
   * @returns Organization
   */
  static async getById(
    tenantId: string,
    organizationId: string
  ): Promise<Organization> {
    if (!tenantId) throw new Error("Tenant ID is required");
    if (!organizationId) throw new Error("Organization ID is required");

    try {
      const client = getDocumentClient();
      const params = {
        TableName: getTableName(),
        Key: {
          [ATTRIBUTE_NAMES.PK]: buildTenantKey(tenantId),
          [ATTRIBUTE_NAMES.SK]: `${KEY_PREFIXES.ORG}${organizationId}`,
        },
      };

      const response = await client.get(params).promise();

      if (!response.Item) {
        throw new Error(`Organization not found: ${organizationId}`);
      }

      return Organization.fromDocClientItem(response.Item);
    } catch (error) {
      logger.error("Error getting organization by ID", {
        error,
        tenantId,
        organizationId,
      });
      throw error;
    }
  }

  /**
   * List organizations by tenant and type
   * Access Pattern #7: List organizations by type
   * @param tenantId Tenant ID
   * @param type Organization type
   * @returns Array of organizations
   */
  static async listByType(
    tenantId: string,
    type: OrganizationType
  ): Promise<Organization[]> {
    if (!tenantId) throw new Error("Tenant ID is required");
    if (!type) throw new Error("Organization type is required");

    const client = getDocumentClient();

    try {
      const response = await client
        .query({
          TableName: getTableName(),
          IndexName: INDEX_NAMES.GSI1,
          KeyConditionExpression:
            `${ATTRIBUTE_NAMES.GSI1PK} = :pk AND begins_with(${ATTRIBUTE_NAMES.GSI1SK}, :typePrefix)`,
          ExpressionAttributeValues: {
            ":pk": buildTenantKey(tenantId),
            ":typePrefix": `${KEY_PREFIXES.TYPE}${type}#`,
          },
        })
        .promise();

      return (response.Items || []).map((item) =>
        Organization.fromDocClientItem(item)
      );
    } catch (error) {
      logger.error("Error listing organizations by type", {
        error,
        tenantId,
        type,
      });
      throw error;
    }
  }

  /**
   * List organizations by tenant and status
   * Access Pattern #8: List organizations by status
   * @param tenantId Tenant ID
   * @param status Organization status
   * @returns Array of organizations
   */
  static async listByStatus(
    tenantId: string,
    status: OrganizationStatus
  ): Promise<Organization[]> {
    if (!tenantId) throw new Error("Tenant ID is required");
    if (!status) throw new Error("Organization status is required");

    const client = getDocumentClient();

    try {
      const response = await client
        .query({
          TableName: getTableName(),
          KeyConditionExpression: `${ATTRIBUTE_NAMES.PK} = :tenantPrefix`,
          FilterExpression: `${ATTRIBUTE_NAMES.STATUS} = :status AND begins_with(${ATTRIBUTE_NAMES.SK}, :orgPrefix)`,
          ExpressionAttributeValues: {
            ":tenantPrefix": buildTenantKey(tenantId),
            ":status": status,
            ":orgPrefix": KEY_PREFIXES.ORG,
          },
        })
        .promise();

      return (response.Items || []).map((item) =>
        Organization.fromDocClientItem(item)
      );
    } catch (error) {
      logger.error("Error listing organizations by status", {
        error,
        tenantId,
        status,
      });
      throw error;
    }
  }

  /**
   * Get organization by license number
   * Access Pattern #9: Get organization by license
   * @param licenseNumber License number
   * @returns Organization if found
   */
  static async getByLicense(
    licenseNumber: string
  ): Promise<Organization | null> {
    if (!licenseNumber) throw new Error("License number is required");

    const client = getDocumentClient();

    try {
      const response = await client
        .query({
          TableName: getTableName(),
          IndexName: INDEX_NAMES.GSI2,
          KeyConditionExpression: `${ATTRIBUTE_NAMES.GSI2PK} = :licenseKey`,
          ExpressionAttributeValues: {
            ":licenseKey": buildLicenseGSIKey(licenseNumber),
          },
          Limit: 1,
        })
        .promise();

      if (!response.Items || response.Items.length === 0) {
        return null;
      }

      return Organization.fromDocClientItem(response.Items[0]);
    } catch (error) {
      logger.error("Error getting organization by license", {
        error,
        licenseNumber,
      });
      throw error;
    }
  }

  /**
   * Updates an existing organization in DynamoDB
   * @returns The updated organization
   */
  async update(): Promise<this> {
    try {
      this.updateTimestamp();
      await super.update();
      return this;
    } catch (error) {
      logger.error("Error updating organization", {
        error,
        organizationId: this.id,
        tenantId: this.tenantId,
      });
      throw error;
    }
  }
}

/**
 * Convenience function to create a new organization
 * @param organization Organization to create
 * @returns Created organization
 */
export const createOrganization = async (
  organization: Organization
): Promise<Organization> => {
  return organization.create();
};

/**
 * Convenience function to get an organization by ID
 * @param tenantId Tenant ID
 * @param organizationId Organization ID
 * @returns Organization
 */
export const getOrganization = async (
  tenantId: string,
  organizationId: string
): Promise<Organization> => {
  return Organization.getById(tenantId, organizationId);
};

/**
 * Convenience function to list organizations by tenant and type
 * @param tenantId Tenant ID
 * @param type Organization type
 * @returns Array of organizations
 */
export const listOrganizations = async (
  tenantId: string,
  type: OrganizationType
): Promise<Organization[]> => {
  return Organization.listByType(tenantId, type);
};

/**
 * Convenience function to get an organization by license number
 * @param licenseNumber License number
 * @returns Organization if found
 */
export const getOrganizationByLicense = async (
  licenseNumber: string
): Promise<Organization | null> => {
  return Organization.getByLicense(licenseNumber);
};
