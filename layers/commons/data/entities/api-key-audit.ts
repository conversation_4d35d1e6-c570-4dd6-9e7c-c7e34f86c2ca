import { DynamoDB } from "aws-sdk";
import { BaseEntity } from "./base-entity";
import { generateUUIDv7 } from "../../utils/uuid";
import { 
  KEY_PREFIXES,
  ENTITY_TYPES,
  SORT_KEYS,
  ATTRIBUTE_NAMES,
  INDEX_NAMES,
  buildApiKeyKey,
  buildOrgKey,
  buildAuditGSIKey,
  buildTypeGSIKey
} from "../../constants/db-keys";

const getTableName = (): string => process.env.TABLE_NAME || "";

export type APIKeyAuditAction = 
  | 'CREATED' 
  | 'ACCESSED' 
  | 'FAILED_ACCESS' 
  | 'REVOKED' 
  | 'UPDATED' 
  | 'EXPIRED'
  | 'RATE_LIMITED';

export interface APIKeyAuditMetadata {
  ipAddress?: string;
  userAgent?: string;
  endpoint?: string;
  method?: string;
  statusCode?: number;
  errorMessage?: string;
  requestId?: string;
  userId?: string;
  reason?: string;
  changes?: Record<string, any>;
}

/**
 * API Key Audit Log entity for tracking all API key activities
 */
export class APIKeyAudit extends BaseEntity {
  id: string;
  organizationId: string;
  apiKeyId: string;
  action: APIKeyAuditAction;
  keyPrefix: string; // For display purposes
  performedBy?: string; // User ID who performed the action (for admin actions)
  metadata?: APIKeyAuditMetadata;
  timestamp: string;

  constructor(
    tenantId: string,
    organizationId: string,
    apiKeyId: string,
    action: APIKeyAuditAction,
    keyPrefix: string,
    options: {
      id?: string;
      performedBy?: string;
      metadata?: APIKeyAuditMetadata;
      timestamp?: string;
    } = {}
  ) {
    super(tenantId);
    this.id = options.id || generateUUIDv7();
    this.organizationId = organizationId;
    this.apiKeyId = apiKeyId;
    this.action = action;
    this.keyPrefix = keyPrefix;
    this.performedBy = options.performedBy;
    this.metadata = options.metadata;
    this.timestamp = options.timestamp || new Date().toISOString();
  }

  get entityType(): string {
    return ENTITY_TYPES.APIKEY_AUDIT;
  }

  get pk(): string {
    return buildApiKeyKey(this.tenantId, this.organizationId, this.apiKeyId);
  }

  get sk(): string {
    return `${KEY_PREFIXES.AUDIT}${this.timestamp}#${this.id}`;
  }

  toItem(): DynamoDB.AttributeMap {
    const item: DynamoDB.AttributeMap = {
      ...this.baseFields(),
      [ATTRIBUTE_NAMES.PK]: { S: this.pk },
      [ATTRIBUTE_NAMES.SK]: { S: this.sk },
      id: { S: this.id },
      [ATTRIBUTE_NAMES.ORGANIZATION_ID]: { S: this.organizationId },
      [ATTRIBUTE_NAMES.API_KEY_ID]: { S: this.apiKeyId },
      [ATTRIBUTE_NAMES.ACTION]: { S: this.action },
      [ATTRIBUTE_NAMES.KEY_PREFIX]: { S: this.keyPrefix },
      [ATTRIBUTE_NAMES.TIMESTAMP]: { S: this.timestamp },
      [ATTRIBUTE_NAMES.ENTITY_TYPE]: { S: this.entityType },
      
      // GSI attributes for different query patterns
      [ATTRIBUTE_NAMES.GSI1PK]: { S: buildOrgKey(this.tenantId, this.organizationId) },
      [ATTRIBUTE_NAMES.GSI1SK]: { S: buildAuditGSIKey(this.timestamp, this.apiKeyId) },
      [ATTRIBUTE_NAMES.GSI2PK]: { S: `${KEY_PREFIXES.TYPE}${this.action}#${KEY_PREFIXES.TENANT}${this.tenantId}` },
      [ATTRIBUTE_NAMES.GSI2SK]: { S: this.timestamp },
    };

    if (this.performedBy) {
      item[ATTRIBUTE_NAMES.PERFORMED_BY] = { S: this.performedBy };
    }

    if (this.metadata) {
      item[ATTRIBUTE_NAMES.METADATA] = {
        M: Object.entries(this.metadata).reduce((acc, [key, value]) => {
          if (value === undefined) return acc;
          
          if (typeof value === 'string') {
            acc[key] = { S: value };
          } else if (typeof value === 'number') {
            acc[key] = { N: value.toString() };
          } else if (typeof value === 'object') {
            acc[key] = { S: JSON.stringify(value) };
          }
          return acc;
        }, {} as DynamoDB.AttributeMap),
      };
    }

    return item;
  }

  toDocClientItem(): Record<string, any> {
    const item: Record<string, any> = {
      ...this.baseDocClientFields(),
      [ATTRIBUTE_NAMES.PK]: this.pk,
      [ATTRIBUTE_NAMES.SK]: this.sk,
      id: this.id,
      [ATTRIBUTE_NAMES.ORGANIZATION_ID]: this.organizationId,
      [ATTRIBUTE_NAMES.API_KEY_ID]: this.apiKeyId,
      [ATTRIBUTE_NAMES.ACTION]: this.action,
      [ATTRIBUTE_NAMES.KEY_PREFIX]: this.keyPrefix,
      [ATTRIBUTE_NAMES.TIMESTAMP]: this.timestamp,
      [ATTRIBUTE_NAMES.ENTITY_TYPE]: this.entityType,
      
      // GSI attributes
      [ATTRIBUTE_NAMES.GSI1PK]: buildOrgKey(this.tenantId, this.organizationId),
      [ATTRIBUTE_NAMES.GSI1SK]: buildAuditGSIKey(this.timestamp, this.apiKeyId),
      [ATTRIBUTE_NAMES.GSI2PK]: `${KEY_PREFIXES.TYPE}${this.action}#${KEY_PREFIXES.TENANT}${this.tenantId}`,
      [ATTRIBUTE_NAMES.GSI2SK]: this.timestamp,
    };

    if (this.performedBy) {
      item[ATTRIBUTE_NAMES.PERFORMED_BY] = this.performedBy;
    }

    if (this.metadata) {
      item[ATTRIBUTE_NAMES.METADATA] = this.metadata;
    }

    return item;
  }

  static fromItem(item?: DynamoDB.AttributeMap): APIKeyAudit {
    if (!item) {
      throw new Error("Cannot create APIKeyAudit from undefined item");
    }

    const tenantId = item[ATTRIBUTE_NAMES.TENANT_ID]?.S;
    const organizationId = item[ATTRIBUTE_NAMES.ORGANIZATION_ID]?.S;
    const apiKeyId = item[ATTRIBUTE_NAMES.API_KEY_ID]?.S;
    const action = item[ATTRIBUTE_NAMES.ACTION]?.S as APIKeyAuditAction;
    const keyPrefix = item[ATTRIBUTE_NAMES.KEY_PREFIX]?.S;

    if (!tenantId || !organizationId || !apiKeyId || !action || !keyPrefix) {
      throw new Error("Missing required fields for APIKeyAudit");
    }

    const audit = new APIKeyAudit(
      tenantId,
      organizationId,
      apiKeyId,
      action,
      keyPrefix,
      {
        id: item.id?.S,
        performedBy: item[ATTRIBUTE_NAMES.PERFORMED_BY]?.S,
        timestamp: item[ATTRIBUTE_NAMES.TIMESTAMP]?.S,
      }
    );

    // Parse metadata
    const metadataMap = item[ATTRIBUTE_NAMES.METADATA]?.M;
    if (metadataMap) {
      audit.metadata = Object.entries(metadataMap).reduce((acc, [key, value]) => {
        if (value.S) {
          // Try to parse JSON strings
          if (key === 'changes' || key.includes('Object') || key.includes('Array')) {
            try {
              acc[key as keyof APIKeyAuditMetadata] = JSON.parse(value.S) as any;
            } catch {
              acc[key as keyof APIKeyAuditMetadata] = value.S as any;
            }
          } else {
            acc[key as keyof APIKeyAuditMetadata] = value.S as any;
          }
        } else if (value.N) {
          acc[key as keyof APIKeyAuditMetadata] = parseInt(value.N, 10) as any;
        }
        return acc;
      }, {} as APIKeyAuditMetadata);
    }

    return audit;
  }

  static fromDocClientItem(item?: Record<string, any>): APIKeyAudit {
    if (!item) {
      throw new Error("Cannot create APIKeyAudit from undefined item");
    }

    const { tenantId, organizationId, apiKeyId, action, keyPrefix } = item;

    if (!tenantId || !organizationId || !apiKeyId || !action || !keyPrefix) {
      throw new Error("Missing required fields for APIKeyAudit");
    }

    return new APIKeyAudit(
      tenantId,
      organizationId,
      apiKeyId,
      action,
      keyPrefix,
      {
        id: item.id,
        performedBy: item.performedBy,
        metadata: item.metadata,
        timestamp: item.timestamp,
      }
    );
  }

  /**
   * List audit logs for a specific API key
   */
  static async listByApiKey(
    tenantId: string,
    organizationId: string,
    apiKeyId: string,
    limit: number = 100,
    startTimestamp?: string
  ): Promise<APIKeyAudit[]> {
    const client = new DynamoDB();
    const params: DynamoDB.QueryInput = {
      TableName: getTableName(),
      KeyConditionExpression: `${ATTRIBUTE_NAMES.PK} = :pk AND begins_with(${ATTRIBUTE_NAMES.SK}, :skPrefix)`,
      ExpressionAttributeValues: {
        ":pk": { S: buildApiKeyKey(tenantId, organizationId, apiKeyId) },
        ":skPrefix": { S: KEY_PREFIXES.AUDIT },
      },
      ScanIndexForward: false, // Most recent first
      Limit: limit,
    };

    if (startTimestamp) {
      params.ExclusiveStartKey = {
        [ATTRIBUTE_NAMES.PK]: { S: buildApiKeyKey(tenantId, organizationId, apiKeyId) },
        [ATTRIBUTE_NAMES.SK]: { S: `${KEY_PREFIXES.AUDIT}${startTimestamp}` },
      };
    }

    const result = await client.query(params).promise();
    return (result.Items || []).map(item => APIKeyAudit.fromItem(item));
  }

  /**
   * List audit logs for an organization
   */
  static async listByOrganization(
    tenantId: string,
    organizationId: string,
    limit: number = 100,
    startTimestamp?: string
  ): Promise<APIKeyAudit[]> {
    const client = new DynamoDB();
    const params: DynamoDB.QueryInput = {
      TableName: getTableName(),
      IndexName: INDEX_NAMES.GSI1,
      KeyConditionExpression: `${ATTRIBUTE_NAMES.GSI1PK} = :pk`,
      ExpressionAttributeValues: {
        ":pk": { S: buildOrgKey(tenantId, organizationId) },
      },
      FilterExpression: `${ATTRIBUTE_NAMES.ENTITY_TYPE} = :entityType`,
      ScanIndexForward: false, // Most recent first
      Limit: limit,
    };

    if (!params.ExpressionAttributeValues) {
      params.ExpressionAttributeValues = {};
    }
    params.ExpressionAttributeValues[":entityType"] = { S: ENTITY_TYPES.APIKEY_AUDIT };

    if (startTimestamp) {
      params.ExclusiveStartKey = {
        [ATTRIBUTE_NAMES.GSI1PK]: { S: buildOrgKey(tenantId, organizationId) },
        [ATTRIBUTE_NAMES.GSI1SK]: { S: buildAuditGSIKey(startTimestamp, "") },
      };
    }

    const result = await client.query(params).promise();
    return (result.Items || []).map(item => APIKeyAudit.fromItem(item));
  }

  /**
   * List audit logs by action type
   */
  static async listByAction(
    tenantId: string,
    action: APIKeyAuditAction,
    limit: number = 100,
    startTimestamp?: string
  ): Promise<APIKeyAudit[]> {
    const client = new DynamoDB();
    const params: DynamoDB.QueryInput = {
      TableName: getTableName(),
      IndexName: INDEX_NAMES.GSI2,
      KeyConditionExpression: `${ATTRIBUTE_NAMES.GSI2PK} = :pk`,
      ExpressionAttributeValues: {
        ":pk": { S: `${KEY_PREFIXES.TYPE}${action}#${KEY_PREFIXES.TENANT}${tenantId}` },
      },
      ScanIndexForward: false, // Most recent first
      Limit: limit,
    };

    if (startTimestamp) {
      params.ExpressionAttributeValues![":timestamp"] = { S: startTimestamp };
      params.KeyConditionExpression += ` AND ${ATTRIBUTE_NAMES.GSI2SK} < :timestamp`;
    }

    const result = await client.query(params).promise();
    return (result.Items || []).map(item => APIKeyAudit.fromItem(item));
  }

  /**
   * Create an audit log entry
   */
  async create(): Promise<this> {
    await super.create();
    return this;
  }
}

// Helper functions for creating audit logs
export const logAPIKeyAccess = async (
  tenantId: string,
  organizationId: string,
  apiKeyId: string,
  keyPrefix: string,
  metadata: APIKeyAuditMetadata
): Promise<APIKeyAudit> => {
  const audit = new APIKeyAudit(
    tenantId,
    organizationId,
    apiKeyId,
    'ACCESSED',
    keyPrefix,
    { metadata }
  );
  return audit.create();
};

export const logAPIKeyFailedAccess = async (
  tenantId: string,
  organizationId: string,
  apiKeyId: string,
  keyPrefix: string,
  metadata: APIKeyAuditMetadata
): Promise<APIKeyAudit> => {
  const audit = new APIKeyAudit(
    tenantId,
    organizationId,
    apiKeyId,
    'FAILED_ACCESS',
    keyPrefix,
    { metadata }
  );
  return audit.create();
};

export const logAPIKeyCreation = async (
  tenantId: string,
  organizationId: string,
  apiKeyId: string,
  keyPrefix: string,
  performedBy: string,
  metadata?: APIKeyAuditMetadata
): Promise<APIKeyAudit> => {
  const audit = new APIKeyAudit(
    tenantId,
    organizationId,
    apiKeyId,
    'CREATED',
    keyPrefix,
    { performedBy, metadata }
  );
  return audit.create();
};

export const logAPIKeyRevocation = async (
  tenantId: string,
  organizationId: string,
  apiKeyId: string,
  keyPrefix: string,
  performedBy: string,
  reason?: string
): Promise<APIKeyAudit> => {
  const audit = new APIKeyAudit(
    tenantId,
    organizationId,
    apiKeyId,
    'REVOKED',
    keyPrefix,
    { 
      performedBy, 
      metadata: { reason } 
    }
  );
  return audit.create();
}; 