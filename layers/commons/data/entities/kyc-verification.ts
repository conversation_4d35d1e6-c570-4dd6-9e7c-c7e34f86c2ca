import { DynamoDB } from "aws-sdk";
import { BaseEntity } from "./base-entity";
import { getDocumentClient } from "../client";
import { 
  KYCStatus, 
  KYCProvider, 
  KYCVerificationLevel, 
  KYCIndividual, 
  KYCVerificationMetadata,
  KYC_CONSTANTS 
} from "../../types/kyc-types";
import { UUID } from "../../utils/uuid";

const getTableName = (): string => process.env.TABLE_NAME || "";

/**
 * KYC Verification Entity
 * 
 * Stores KYC verification records in DynamoDB with support for multiple providers
 * and comprehensive verification tracking.
 */
export class KYCVerification extends BaseEntity {
  id: string;
  userId: string;
  clerkUserId: string;
  status: KYCStatus;
  verificationLevel: KYCVerificationLevel;
  provider: KYCProvider;
  individual?: KYCIndividual;
  metadata: KYCVerificationMetadata;
  verifiedAt?: string;
  expiresAt?: string;
  failureReason?: string;
  retryCount: number;
  maxRetries: number;
  createdBy?: string;
  reviewedBy?: string;
  reviewNotes?: string;

  constructor(
    tenantId: string,
    userId: string,
    clerkUserId: string,
    provider: KYCProvider,
    verificationLevel: KYCVerificationLevel,
    metadata: KYCVerificationMetadata,
    options: {
      id?: string;
      status?: KYCStatus;
      individual?: KYCIndividual;
      verifiedAt?: string;
      expiresAt?: string;
      failureReason?: string;
      retryCount?: number;
      maxRetries?: number;
      createdBy?: string;
      reviewedBy?: string;
      reviewNotes?: string;
      createdAt?: string;
      updatedAt?: string;
    } = {}
  ) {
    super(tenantId);
    this.id = options.id || UUID.v7();
    this.userId = userId;
    this.clerkUserId = clerkUserId;
    this.status = options.status || "NOT_STARTED";
    this.verificationLevel = verificationLevel;
    this.provider = provider;
    this.individual = options.individual;
    this.metadata = metadata;
    this.verifiedAt = options.verifiedAt;
    this.expiresAt = options.expiresAt;
    this.failureReason = options.failureReason;
    this.retryCount = options.retryCount || 0;
    this.maxRetries = options.maxRetries || KYC_CONSTANTS.DEFAULT_MAX_RETRIES;
    this.createdBy = options.createdBy;
    this.reviewedBy = options.reviewedBy;
    this.reviewNotes = options.reviewNotes;

    if (options.createdAt) this.createdAt = options.createdAt;
    if (options.updatedAt) this.updatedAt = options.updatedAt;
  }

  get entityType(): string {
    return "KYC_VERIFICATION";
  }

  get pk(): string {
    return `TENANT#${this.tenantId}#USER#${this.userId}#KYC#${this.id}`;
  }

  get sk(): string {
    return "METADATA";
  }

  /**
   * Check if verification is expired
   */
  get isExpired(): boolean {
    if (!this.expiresAt) return false;
    return new Date() > new Date(this.expiresAt);
  }

  /**
   * Check if verification can be retried
   */
  get canRetry(): boolean {
    return this.retryCount < this.maxRetries && 
           (this.status === "FAILED" || this.status === "EXPIRED");
  }

  /**
   * Get display name for the individual
   */
  get individualDisplayName(): string {
    if (!this.individual) return "Unknown";
    return `${this.individual.firstName || ""} ${this.individual.lastName || ""}`.trim();
  }

  /**
   * Mark verification as verified
   */
  markAsVerified(verifiedAt?: string): void {
    this.status = "VERIFIED";
    this.verifiedAt = verifiedAt || new Date().toISOString();
    
    // Set expiration date
    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + KYC_CONSTANTS.DEFAULT_EXPIRATION_DAYS);
    this.expiresAt = expirationDate.toISOString();
    
    this.updateTimestamp();
  }

  /**
   * Mark verification as failed
   */
  markAsFailed(reason: string): void {
    this.status = "FAILED";
    this.failureReason = reason;
    this.retryCount += 1;
    this.updateTimestamp();
  }

  /**
   * Update verification status
   */
  updateStatus(status: KYCStatus, reason?: string): void {
    this.status = status;
    if (reason) {
      this.failureReason = reason;
    }
    this.updateTimestamp();
  }

  /**
   * Add review notes
   */
  addReviewNotes(reviewedBy: string, notes: string): void {
    this.reviewedBy = reviewedBy;
    this.reviewNotes = notes;
    this.updateTimestamp();
  }

  toItem(): DynamoDB.AttributeMap {
    return {
      PK: { S: this.pk },
      SK: { S: this.sk },
      
      // User KYC Lookup: Query by user across all verifications
      UserKycLookupPK: { S: `TENANT#${this.tenantId}#USER#${this.userId}#KYC` },
      UserKycLookupSK: { S: `STATUS#${this.status}#CREATED#${this.createdAt}` },
      
      // Provider KYC Lookup: Query by provider and status
      ProviderKycLookupPK: { S: `TENANT#${this.tenantId}#KYC#PROVIDER#${this.provider}` },
      ProviderKycLookupSK: { S: `STATUS#${this.status}#LEVEL#${this.verificationLevel}#${this.createdAt}` },
      
      // Clerk User KYC Lookup: Query by Clerk user ID
      ClerkUserKycLookupPK: { S: `CLERK_USER#${this.clerkUserId}#KYC` },
      ClerkUserKycLookupSK: { S: `STATUS#${this.status}#${this.createdAt}` },

      ...this.baseFields(),
      id: { S: this.id },
      userId: { S: this.userId },
      clerkUserId: { S: this.clerkUserId },
      status: { S: this.status },
      verificationLevel: { S: this.verificationLevel },
      provider: { S: this.provider },
      retryCount: { N: this.retryCount.toString() },
      maxRetries: { N: this.maxRetries.toString() },
      
      ...(this.individual && { 
        individual: { 
          M: this.marshallIndividual(this.individual) 
        } 
      }),
      
      metadata: { 
        M: this.marshallMetadata(this.metadata) 
      },
      
      ...(this.verifiedAt && { verifiedAt: { S: this.verifiedAt } }),
      ...(this.expiresAt && { expiresAt: { S: this.expiresAt } }),
      ...(this.failureReason && { failureReason: { S: this.failureReason } }),
      ...(this.createdBy && { createdBy: { S: this.createdBy } }),
      ...(this.reviewedBy && { reviewedBy: { S: this.reviewedBy } }),
      ...(this.reviewNotes && { reviewNotes: { S: this.reviewNotes } }),
    };
  }

  toDocClientItem(): Record<string, any> {
    return {
      PK: this.pk,
      SK: this.sk,
      
      // User KYC Lookup: Query by user across all verifications
      UserKycLookupPK: `TENANT#${this.tenantId}#USER#${this.userId}#KYC`,
      UserKycLookupSK: `STATUS#${this.status}#CREATED#${this.createdAt}`,
      
      // Provider KYC Lookup: Query by provider and status
      ProviderKycLookupPK: `TENANT#${this.tenantId}#KYC#PROVIDER#${this.provider}`,
      ProviderKycLookupSK: `STATUS#${this.status}#LEVEL#${this.verificationLevel}#${this.createdAt}`,
      
      // Clerk User KYC Lookup: Query by Clerk user ID
      ClerkUserKycLookupPK: `CLERK_USER#${this.clerkUserId}#KYC`,
      ClerkUserKycLookupSK: `STATUS#${this.status}#${this.createdAt}`,

      ...this.baseDocClientFields(),
      id: this.id,
      userId: this.userId,
      clerkUserId: this.clerkUserId,
      status: this.status,
      verificationLevel: this.verificationLevel,
      provider: this.provider,
      retryCount: this.retryCount,
      maxRetries: this.maxRetries,
      
      ...(this.individual && { individual: this.individual }),
      metadata: this.metadata,
      
      ...(this.verifiedAt && { verifiedAt: this.verifiedAt }),
      ...(this.expiresAt && { expiresAt: this.expiresAt }),
      ...(this.failureReason && { failureReason: this.failureReason }),
      ...(this.createdBy && { createdBy: this.createdBy }),
      ...(this.reviewedBy && { reviewedBy: this.reviewedBy }),
      ...(this.reviewNotes && { reviewNotes: this.reviewNotes }),
    };
  }

  private marshallIndividual(individual: KYCIndividual): DynamoDB.AttributeMap {
    const result: DynamoDB.AttributeMap = {
      firstName: { S: individual.firstName },
      lastName: { S: individual.lastName },
      dateOfBirth: { S: individual.dateOfBirth },
    };

    if (individual.ssn) result.ssn = { S: individual.ssn };
    if (individual.phoneNumber) result.phoneNumber = { S: individual.phoneNumber };
    
    if (individual.emailAddresses && individual.emailAddresses.length > 0) {
      result.emailAddresses = { 
        L: individual.emailAddresses.map(email => ({ S: email })) 
      };
    }
    
    if (individual.addresses && individual.addresses.length > 0) {
      result.addresses = {
        L: individual.addresses.map(addr => ({
          M: {
            street: { S: addr.street },
            city: { S: addr.city },
            region: { S: addr.region },
            postalCode: { S: addr.postalCode },
            ...(addr.extendedAddress && { extendedAddress: { S: addr.extendedAddress } }),
            ...(addr.country && { country: { S: addr.country } }),
          }
        }))
      };
    }

    return result;
  }

  private marshallMetadata(metadata: KYCVerificationMetadata): DynamoDB.AttributeMap {
    const result: DynamoDB.AttributeMap = {
      provider: { S: metadata.provider },
      verificationLevel: { S: metadata.verificationLevel },
    };

    if (metadata.providerTransactionId) {
      result.providerTransactionId = { S: metadata.providerTransactionId };
    }
    if (metadata.correlationId) {
      result.correlationId = { S: metadata.correlationId };
    }
    if (metadata.verificationMethod) {
      result.verificationMethod = { S: metadata.verificationMethod };
    }
    if (metadata.ipAddress) {
      result.ipAddress = { S: metadata.ipAddress };
    }
    if (metadata.userAgent) {
      result.userAgent = { S: metadata.userAgent };
    }
    if (metadata.deviceFingerprint) {
      result.deviceFingerprint = { S: metadata.deviceFingerprint };
    }
    if (metadata.riskScore !== undefined) {
      result.riskScore = { N: metadata.riskScore.toString() };
    }
    if (metadata.confidenceScore !== undefined) {
      result.confidenceScore = { N: metadata.confidenceScore.toString() };
    }
    if (metadata.documentTypes && metadata.documentTypes.length > 0) {
      result.documentTypes = { 
        L: metadata.documentTypes.map(type => ({ S: type })) 
      };
    }
    if (metadata.flags && metadata.flags.length > 0) {
      result.flags = { 
        L: metadata.flags.map(flag => ({ S: flag })) 
      };
    }
    if (metadata.biometricData) {
      result.biometricData = {
        M: {
          ...(metadata.biometricData.faceMatch !== undefined && {
            faceMatch: { BOOL: metadata.biometricData.faceMatch }
          }),
          ...(metadata.biometricData.livenessCheck !== undefined && {
            livenessCheck: { BOOL: metadata.biometricData.livenessCheck }
          }),
        }
      };
    }
    if (metadata.rawProviderResponse) {
      result.rawProviderResponse = { 
        S: JSON.stringify(metadata.rawProviderResponse) 
      };
    }

    return result;
  }

  static fromItem(item?: DynamoDB.AttributeMap): KYCVerification {
    if (!item) throw new Error("No KYC verification item found!");

    const individual = item.individual?.M ? 
      KYCVerification.unmarshallIndividual(item.individual.M) : undefined;
    
    const metadata = KYCVerification.unmarshallMetadata(item.metadata.M!);

    return new KYCVerification(
      item.tenantId.S!,
      item.userId.S!,
      item.clerkUserId.S!,
      item.provider.S! as KYCProvider,
      item.verificationLevel.S! as KYCVerificationLevel,
      metadata,
      {
        id: item.id.S!,
        status: item.status.S! as KYCStatus,
        individual,
        verifiedAt: item.verifiedAt?.S,
        expiresAt: item.expiresAt?.S,
        failureReason: item.failureReason?.S,
        retryCount: parseInt(item.retryCount.N!),
        maxRetries: parseInt(item.maxRetries.N!),
        createdBy: item.createdBy?.S,
        reviewedBy: item.reviewedBy?.S,
        reviewNotes: item.reviewNotes?.S,
        createdAt: item.createdAt.S!,
        updatedAt: item.updatedAt.S!,
      }
    );
  }

  static fromDocClientItem(item?: Record<string, any>): KYCVerification {
    if (!item) throw new Error("No KYC verification item found!");

    return new KYCVerification(
      item.tenantId,
      item.userId,
      item.clerkUserId,
      item.provider as KYCProvider,
      item.verificationLevel as KYCVerificationLevel,
      item.metadata as KYCVerificationMetadata,
      {
        id: item.id,
        status: item.status as KYCStatus,
        individual: item.individual as KYCIndividual,
        verifiedAt: item.verifiedAt,
        expiresAt: item.expiresAt,
        failureReason: item.failureReason,
        retryCount: item.retryCount,
        maxRetries: item.maxRetries,
        createdBy: item.createdBy,
        reviewedBy: item.reviewedBy,
        reviewNotes: item.reviewNotes,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      }
    );
  }

  private static unmarshallIndividual(item: DynamoDB.AttributeMap): KYCIndividual {
    const individual: KYCIndividual = {
      firstName: item.firstName.S!,
      lastName: item.lastName.S!,
      dateOfBirth: item.dateOfBirth.S!,
    };

    if (item.ssn?.S) individual.ssn = item.ssn.S;
    if (item.phoneNumber?.S) individual.phoneNumber = item.phoneNumber.S;
    
    if (item.emailAddresses?.L) {
      individual.emailAddresses = item.emailAddresses.L.map(email => email.S!);
    }
    
    if (item.addresses?.L) {
      individual.addresses = item.addresses.L.map(addr => ({
        street: addr.M!.street.S!,
        city: addr.M!.city.S!,
        region: addr.M!.region.S!,
        postalCode: addr.M!.postalCode.S!,
        ...(addr.M!.extendedAddress?.S && { extendedAddress: addr.M!.extendedAddress.S }),
        ...(addr.M!.country?.S && { country: addr.M!.country.S }),
      }));
    }

    return individual;
  }

  private static unmarshallMetadata(item: DynamoDB.AttributeMap): KYCVerificationMetadata {
    const metadata: KYCVerificationMetadata = {
      provider: item.provider.S! as KYCProvider,
      verificationLevel: item.verificationLevel.S! as KYCVerificationLevel,
    };

    if (item.providerTransactionId?.S) {
      metadata.providerTransactionId = item.providerTransactionId.S;
    }
    if (item.correlationId?.S) {
      metadata.correlationId = item.correlationId.S;
    }
    if (item.verificationMethod?.S) {
      metadata.verificationMethod = item.verificationMethod.S;
    }
    if (item.ipAddress?.S) {
      metadata.ipAddress = item.ipAddress.S;
    }
    if (item.userAgent?.S) {
      metadata.userAgent = item.userAgent.S;
    }
    if (item.deviceFingerprint?.S) {
      metadata.deviceFingerprint = item.deviceFingerprint.S;
    }
    if (item.riskScore?.N) {
      metadata.riskScore = parseFloat(item.riskScore.N);
    }
    if (item.confidenceScore?.N) {
      metadata.confidenceScore = parseFloat(item.confidenceScore.N);
    }
    if (item.documentTypes?.L) {
      metadata.documentTypes = item.documentTypes.L.map(type => type.S!);
    }
    if (item.flags?.L) {
      metadata.flags = item.flags.L.map(flag => flag.S!);
    }
    if (item.biometricData?.M) {
      metadata.biometricData = {
        ...(item.biometricData.M.faceMatch?.BOOL !== undefined && {
          faceMatch: item.biometricData.M.faceMatch.BOOL
        }),
        ...(item.biometricData.M.livenessCheck?.BOOL !== undefined && {
          livenessCheck: item.biometricData.M.livenessCheck.BOOL
        }),
      };
    }
    if (item.rawProviderResponse?.S) {
      try {
        metadata.rawProviderResponse = JSON.parse(item.rawProviderResponse.S);
      } catch (error) {
        console.warn("Failed to parse rawProviderResponse:", error);
      }
    }

    return metadata;
  }

  /**
   * Get KYC verification by ID
   */
  static async getById(tenantId: string, userId: string, verificationId: string): Promise<KYCVerification> {
    const verification = new KYCVerification(
      tenantId, 
      userId, 
      "", 
      "PROVE", 
      "BASIC", 
      { provider: "PROVE", verificationLevel: "BASIC" },
      { id: verificationId }
    );
    
    const client = getDocumentClient();

    try {
      const response = await client.get({
        TableName: getTableName(),
        Key: verification.docClientKeys(),
      }).promise();

      if (!response.Item) {
        throw new Error(`KYC verification not found: ${verificationId}`);
      }

      return KYCVerification.fromDocClientItem(response.Item);
    } catch (error) {
      console.error("Error getting KYC verification by ID:", error);
      throw error;
    }
  }

  /**
   * Get latest KYC verification for a user
   */
  static async getLatestByUser(tenantId: string, userId: string): Promise<KYCVerification | null> {
    const client = getDocumentClient();

    try {
      const response = await client.query({
        TableName: getTableName(),
                  IndexName: "UserKycLookupIndex",
                  KeyConditionExpression: "UserKycLookupPK = :userKycLookupPK",
          ExpressionAttributeValues: {
            ":userKycLookupPK": `TENANT#${tenantId}#USER#${userId}#KYC`,
        },
        ScanIndexForward: false, // Get latest first
        Limit: 1,
      }).promise();

      if (!response.Items || response.Items.length === 0) {
        return null;
      }

      return KYCVerification.fromDocClientItem(response.Items[0]);
    } catch (error) {
      console.error("Error getting latest KYC verification:", error);
      throw error;
    }
  }

  /**
   * Get KYC verification by Clerk user ID
   */
  static async getByClerkUserId(clerkUserId: string): Promise<KYCVerification | null> {
    const client = getDocumentClient();

    try {
      const response = await client.query({
        TableName: getTableName(),
                  IndexName: "ClerkUserKycLookupIndex",
                  KeyConditionExpression: "ClerkUserKycLookupPK = :clerkUserKycLookupPK",
          ExpressionAttributeValues: {
            ":clerkUserKycLookupPK": `CLERK_USER#${clerkUserId}#KYC`,
        },
        ScanIndexForward: false, // Get latest first
        Limit: 1,
      }).promise();

      if (!response.Items || response.Items.length === 0) {
        return null;
      }

      return KYCVerification.fromDocClientItem(response.Items[0]);
    } catch (error) {
      console.error("Error getting KYC verification by Clerk user ID:", error);
      throw error;
    }
  }

  /**
   * List KYC verifications by provider and status
   */
  static async listByProviderAndStatus(
    tenantId: string,
    provider: KYCProvider,
    status?: KYCStatus,
    limit: number = 50
  ): Promise<KYCVerification[]> {
    const client = getDocumentClient();

    try {
      const keyCondition = status 
        ? "ProviderKycLookupPK = :providerKycLookupPK AND begins_with(ProviderKycLookupSK, :statusPrefix)"
        : "ProviderKycLookupPK = :providerKycLookupPK";

      const expressionValues: Record<string, any> = {
        ":providerKycLookupPK": `TENANT#${tenantId}#KYC#PROVIDER#${provider}`,
      };

      if (status) {
        expressionValues[":statusPrefix"] = `STATUS#${status}`;
      }

      const response = await client.query({
        TableName: getTableName(),
                  IndexName: "ProviderKycLookupIndex",
        KeyConditionExpression: keyCondition,
        ExpressionAttributeValues: expressionValues,
        ScanIndexForward: false, // Get latest first
        Limit: limit,
      }).promise();

      return (response.Items || []).map(item => KYCVerification.fromDocClientItem(item));
    } catch (error) {
      console.error("Error listing KYC verifications by provider and status:", error);
      throw error;
    }
  }
}

// Convenience functions
export const createKYCVerification = async (verification: KYCVerification): Promise<KYCVerification> => {
  return verification.create();
};

export const getKYCVerification = async (
  tenantId: string, 
  userId: string, 
  verificationId: string
): Promise<KYCVerification> => {
  return KYCVerification.getById(tenantId, userId, verificationId);
};

export const getLatestKYCVerification = async (
  tenantId: string, 
  userId: string
): Promise<KYCVerification | null> => {
  return KYCVerification.getLatestByUser(tenantId, userId);
};

export const updateKYCVerification = async (verification: KYCVerification): Promise<KYCVerification> => {
  return verification.update();
}; 