{"description": "Lambda layer with common utils for CLKK Payment System", "main": "index.ts", "name": "layer-commons", "version": "1.0.0", "dependencies": {"@aws-lambda-powertools/logger": "^2.11.0", "@aws-lambda-powertools/metrics": "^2.20.0", "@aws-lambda-powertools/tracer": "^2.20.0", "@aws-sdk/client-dynamodb": "^3.705.0", "@aws-sdk/client-sns": "^3.734.0", "@aws-sdk/lib-dynamodb": "^3.705.0", "@clerk/types": "^4.40.2", "aws-lambda": "^1.0.7", "axios": "^1.7.9", "joi": "^17.13.3", "uuid": "^10.0.0"}, "devDependencies": {"@types/aws-lambda": "^8.10.148", "@types/jest": "^29.5.14", "@types/node": "^18.19.103", "@types/uuid": "^10.0.0", "jest": "^29.7.0", "ts-jest": "^29.3.4", "typescript": "^5.0.3"}, "scripts": {"build": "node_modules/typescript/bin/tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}}