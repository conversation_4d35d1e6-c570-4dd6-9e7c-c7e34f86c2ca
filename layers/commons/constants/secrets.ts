/**
 * AWS Secrets Manager Secret Names and Configurations
 *
 * Centralized configuration for all secrets used in the application.
 * This ensures consistency and makes it easy to manage secret names.
 */

/**
 * Get environment-specific secret names
 * @param environment - The environment (dev, staging, prod)
 */
export function getSecretNames(environment: string = process.env.ENVIRONMENT || 'dev') {
  return {
    // Clerk Configuration Secrets
    CLERK_WEBHOOK_SECRET: `${environment}/clkk/clerk/webhook-secret`,
    CLERK_JWT_PUBLIC_KEY: `${environment}/clkk/clerk/jwt-public-key`,

    // KYC Provider Secrets
    PROVE_API_CREDENTIALS: `${environment}/clkk/prove/api-credentials`,

    // Payment Provider Secrets (for future use)
    CASHAPP_API_CREDENTIALS: `${environment}/clkk/cashapp/api-credentials`,
    STRIPE_API_CREDENTIALS: `${environment}/clkk/stripe/api-credentials`,
  } as const;
}

// Default secret names for current environment
export const SECRET_NAMES = getSecretNames();

/**
 * Secret ARN builder helper
 * @param region - AWS region
 * @param accountId - AWS account ID
 * @param secretName - Secret name from SECRET_NAMES
 * @returns Full secret ARN
 */
export function buildSecretArn(region: string, accountId: string, secretName: string): string {
  return `arn:aws:secretsmanager:${region}:${accountId}:secret:${secretName}`;
}

/**
 * Environment-specific secret name builder
 * @param environment - Environment (dev, staging, prod)
 * @param secretName - Base secret name
 * @returns Environment-specific secret name
 */
export function buildEnvironmentSecretName(environment: string, secretName: string): string {
  return `${environment}/${secretName}`;
}

/**
 * Secret descriptions for CloudFormation templates
 */
export const SECRET_DESCRIPTIONS = {
  [SECRET_NAMES.CLERK_WEBHOOK_SECRET]: 'Clerk webhook secret for verifying webhook signatures',
  [SECRET_NAMES.CLERK_JWT_PUBLIC_KEY]: 'Clerk JWT public key for token verification',
  [SECRET_NAMES.PROVE_API_CREDENTIALS]: 'Prove API credentials (client ID and secret)',
  [SECRET_NAMES.CASHAPP_API_CREDENTIALS]: 'CashApp API credentials',
  [SECRET_NAMES.STRIPE_API_CREDENTIALS]: 'Stripe API credentials',
} as const;

/**
 * Secret JSON structure definitions for type safety
 */
export interface ClerkWebhookSecretValue {
  webhookSecret: string;
}

// Note: Clerk JWT Public Key is stored as plain text, not JSON
// So we don't need an interface for it - it's just a string

export interface ProveApiCredentialsValue {
  clientId: string;
  clientSecret: string;
  environment: 'SANDBOX' | 'PRODUCTION';
}

export interface CashAppApiCredentialsValue {
  clientId: string;
  clientSecret: string;
  environment: 'SANDBOX' | 'PRODUCTION';
}

export interface StripeApiCredentialsValue {
  publishableKey: string;
  secretKey: string;
  webhookSecret: string;
  environment: 'test' | 'live';
}