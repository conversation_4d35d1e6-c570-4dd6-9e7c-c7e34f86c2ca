/**
 * Centralized DynamoDB key patterns and constants
 * This file contains all key construction patterns used across entities
 * to ensure consistency and maintainability
 */

// ============================================
// KEY PREFIXES
// ============================================

export const KEY_PREFIXES = {
  // Primary prefixes
  TENANT: 'TENANT#',
  ORG: 'ORG#',
  USER: 'USER#',
  APIKEY: 'APIKEY#',
  APP: 'APP#',
  PAYMENT: 'PAYMENT#',
  KYC: 'KYC#',
  VAULT: 'VAULT#',
  RECEIVER: 'RECEIVER#',
  BUSINESS: 'BUSINESS#',
  
  // Secondary prefixes
  METADATA: 'METADATA',
  AUDIT: 'AUDIT#',
  STATUS: 'STATUS#',
  TYPE: 'TYPE#',
  CREATED: 'CREATED#',
  NAME: 'NAME#',
  EMAIL: 'EMAIL#',
  PHONE: 'PHONE#',
  USERNAME: 'USERNAME#',
  EXTERNAL_ID: 'EXTERNAL_ID#',
  CODE: 'CODE#',
  LICENSE: 'LICENSE#',
  ROLE: 'ROLE#',
  
  // Lookup prefixes
  APIKEY_HASH: 'APIKEY_HASH#',
  APIKEY_PREFIX: 'APIKEY_PREFIX#',
  CONSTRAINT: 'CONSTRAINT#',
  REFERENCE: 'REFERENCE#',
  PROVIDER: 'PROVIDER#',
  CUSTOMER: 'CUSTOMER#',
  CLERK: 'CLERK#',
  
  // Composite prefixes
  ORGS: 'ORGS',
  USERS: 'USERS',
  APIKEYS: 'APIKEYS',
  PAYMENTS: 'PAYMENTS',
  MEMBERS: 'MEMBERS',
  CODES: 'CODES',
  
  // Action prefixes
  REVOKED: 'REVOKED',
  EXPIRED: 'EXPIRED',
  ACTIVE: 'ACTIVE',
  PENDING: 'PENDING',
  
  // Special markers
  VERIFICATIONS: 'VERIFICATIONS',
} as const;

// ============================================
// ENTITY TYPES
// ============================================

export const ENTITY_TYPES = {
  USER: 'USER',
  ORGANIZATION: 'ORGANIZATION',
  APIKEY: 'APIKey',
  APIKEY_AUDIT: 'APIKeyAudit',
  APPLICATION: 'APPLICATION',
  PAYMENT: 'PAYMENT',
  KYC_VERIFICATION: 'KycVerification',
  RECEIVER: 'RECEIVER',
  PAYMENT_VAULT: 'PaymentVault',
} as const;

// ============================================
// SORT KEY VALUES
// ============================================

export const SORT_KEYS = {
  METADATA: 'METADATA',
  DETAILS: 'DETAILS',
  CONFIG: 'CONFIG',
  SETTINGS: 'SETTINGS',
} as const;

// ============================================
// KEY BUILDERS
// ============================================

/**
 * Builds a tenant-scoped key
 */
export function buildTenantKey(tenantId: string): string {
  return `${KEY_PREFIXES.TENANT}${tenantId}`;
}

/**
 * Builds an organization-scoped key
 */
export function buildOrgKey(tenantId: string, orgId: string): string {
  return `${buildTenantKey(tenantId)}#${KEY_PREFIXES.ORG}${orgId}`;
}

/**
 * Builds a user-scoped key
 */
export function buildUserKey(userId: string): string {
  return `${KEY_PREFIXES.USER}${userId}`;
}

/**
 * Builds a tenant-user-scoped key
 */
export function buildTenantUserKey(tenantId: string, userId: string): string {
  return `${buildTenantKey(tenantId)}#${KEY_PREFIXES.USER}${userId}`;
}

/**
 * Builds an API key-scoped key
 */
export function buildApiKeyKey(tenantId: string, orgId: string, keyId: string): string {
  return `${buildOrgKey(tenantId, orgId)}#${KEY_PREFIXES.APIKEY}${keyId}`;
}

/**
 * Builds a payment-scoped key
 */
export function buildPaymentKey(orgId: string, paymentId: string): string {
  return `${KEY_PREFIXES.ORG}${orgId}#${KEY_PREFIXES.PAYMENT}${paymentId}`;
}

/**
 * Builds a KYC-scoped key
 */
export function buildKycKey(tenantId: string, userId: string, kycId: string): string {
  return `${buildTenantUserKey(tenantId, userId)}#${KEY_PREFIXES.KYC}${kycId}`;
}

/**
 * Builds an application-scoped key
 */
export function buildApplicationKey(appId: string): string {
  return `${KEY_PREFIXES.APP}${appId}`;
}

/**
 * Builds a constraint key for uniqueness checks
 */
export function buildConstraintKey(constraintType: string, constraintValue: string): string {
  return `${KEY_PREFIXES.CONSTRAINT}${constraintType}#${constraintValue}`;
}

/**
 * Builds a vault-scoped key
 */
export function buildVaultKey(orgId: string, vaultId: string): string {
  return `${KEY_PREFIXES.ORG}${orgId}#${KEY_PREFIXES.VAULT}${vaultId}`;
}

/**
 * Builds a receiver-scoped key
 */
export function buildReceiverKey(type: 'USER' | 'BUSINESS', receiverId: string): string {
  const prefix = type === 'USER' ? KEY_PREFIXES.USER : KEY_PREFIXES.BUSINESS;
  return `${KEY_PREFIXES.RECEIVER}#${prefix}${receiverId}`;
}

// ============================================
// GSI KEY BUILDERS
// ============================================

/**
 * Builds a GSI key for listing entities by type
 */
export function buildTypeGSIKey(typeOrTenantId: string, entityTypeOrTenantId?: string): string {
  // Support both signatures for backward compatibility
  if (entityTypeOrTenantId) {
    // Original signature: (tenantId, entityType)
    return `${KEY_PREFIXES.TENANT}${typeOrTenantId}#${KEY_PREFIXES.TYPE}${entityTypeOrTenantId}`;
  } else {
    // For audit logs: just the type
    return `${KEY_PREFIXES.TYPE}${typeOrTenantId}`;
  }
}

/**
 * Builds a GSI key for listing by status
 */
export function buildStatusGSIKey(status: string, timestamp: string): string {
  return `${KEY_PREFIXES.STATUS}${status}#${KEY_PREFIXES.CREATED}${timestamp}`;
}

/**
 * Builds a GSI key for API key hash lookup
 */
export function buildApiKeyHashGSIKey(keyHash: string): string {
  return `${KEY_PREFIXES.APIKEY_HASH}${keyHash}`;
}

/**
 * Builds a GSI key for API key prefix lookup
 */
export function buildApiKeyPrefixGSIKey(keyPrefix: string): string {
  return `${KEY_PREFIXES.APIKEY_PREFIX}${keyPrefix}`;
}

/**
 * Builds a GSI key for email lookup
 */
export function buildEmailGSIKey(email: string): string {
  return `${KEY_PREFIXES.EMAIL}${email.toLowerCase()}`;
}

/**
 * Builds a GSI key for username lookup
 */
export function buildUsernameGSIKey(username: string): string {
  return `${KEY_PREFIXES.USERNAME}${username.toLowerCase()}`;
}

/**
 * Builds a GSI key for external ID lookup
 */
export function buildExternalIdGSIKey(externalId: string): string {
  return `${KEY_PREFIXES.EXTERNAL_ID}${externalId}`;
}

/**
 * Builds a GSI key for Clerk ID lookup
 */
export function buildClerkIdGSIKey(clerkId: string): string {
  return `${KEY_PREFIXES.CLERK}${clerkId}`;
}

/**
 * Build a GSI sort key for payment status queries
 */
export const buildPaymentStatusGSIKey = (status: string, date: string): string => {
  return `${KEY_PREFIXES.STATUS}${status}#${date}`;
};

/**
 * Build a GSI sort key for audit log queries
 */
export const buildAuditGSIKey = (timestamp: string, entityId: string): string => {
  return `${KEY_PREFIXES.AUDIT}${timestamp}#${entityId}`;
};

/**
 * Build a GSI key for license lookup queries
 */
export const buildLicenseGSIKey = (licenseNumber: string): string => {
  return `${KEY_PREFIXES.LICENSE}${licenseNumber}`;
};

// ============================================
// ATTRIBUTE NAMES
// ============================================

export const ATTRIBUTE_NAMES = {
  // Primary keys
  PK: 'PK',
  SK: 'SK',
  
  // GSI keys
  GSI1PK: 'GSI1PK',
  GSI1SK: 'GSI1SK',
  GSI2PK: 'GSI2PK',
  GSI2SK: 'GSI2SK',
  GSI3PK: 'GSI3PK',
  GSI3SK: 'GSI3SK',
  
  // Common attributes
  ENTITY_TYPE: 'entityType',
  TENANT_ID: 'tenantId',
  ORGANIZATION_ID: 'organizationId',
  USER_ID: 'userId',
  STATUS: 'status',
  TYPE: 'type',
  CREATED_AT: 'createdAt',
  UPDATED_AT: 'updatedAt',
  TTL: 'TTL',
  
  // Organization attributes
  NAME: 'name' as const,
  DESCRIPTION: 'description',
  LICENSE_NUMBER: 'licenseNumber',
  CONTACT_EMAIL: 'contactEmail',
  CONTACT_PHONE: 'phone' as const,
  
  // User attributes
  EMAIL: 'email' as const,
  USERNAME: 'username' as const,
  FIRST_NAME: 'firstName',
  LAST_NAME: 'lastName',
  ROLE: 'role',
  
  // API Key attributes
  KEY_HASH: 'keyHash' as const,
  KEY_PREFIX: 'keyPrefix' as const,
  LABEL: 'label' as const,
  EXPIRES_AT: 'expiresAt' as const,
  RATE_LIMIT: 'rateLimit' as const,
  PERMISSIONS: 'permissions' as const,
  
  // Payment attributes
  AMOUNT: 'amount',
  CURRENCY: 'currency',
  REFERENCE: 'reference',
  PROVIDER: 'provider',
  
  // Metadata
  METADATA: 'metadata',
  PUBLIC_METADATA: 'publicMetadata',
  PRIVATE_METADATA: 'privateMetadata' as const,
  
  // APIKeyAudit attributes
  API_KEY_ID: 'apiKeyId' as const,
  ACTION: 'action' as const,
  TIMESTAMP: 'timestamp' as const,
  PERFORMED_BY: 'performedBy' as const,
  
  // Organization attributes
  OWNER_ID: 'ownerId' as const,
  CONTACT_INFO: 'contactInfo' as const,
  ADDRESS: 'address' as const,
} as const;

// ============================================
// INDEX NAMES
// ============================================

export const INDEX_NAMES = {
  GSI1: 'GSI1',
  GSI2: 'GSI2',
  GSI3: 'GSI3',
  TYPE_LOOKUP: 'TypeLookupIndex',
  STATUS_LOOKUP: 'StatusLookupIndex',
  EMAIL_LOOKUP: 'EmailLookupIndex',
  USERNAME_LOOKUP: 'UsernameLookupIndex',
  LICENSE_LOOKUP: 'LicenseLookupIndex',
  CLERK_LOOKUP: 'ClerkLookupIndex',
} as const;

// ============================================
// TYPE EXPORTS
// ============================================

export type KeyPrefix = typeof KEY_PREFIXES[keyof typeof KEY_PREFIXES];
export type EntityType = typeof ENTITY_TYPES[keyof typeof ENTITY_TYPES];
export type SortKey = typeof SORT_KEYS[keyof typeof SORT_KEYS];
export type AttributeName = typeof ATTRIBUTE_NAMES[keyof typeof ATTRIBUTE_NAMES];
export type IndexName = typeof INDEX_NAMES[keyof typeof INDEX_NAMES]; 