/**
 * Common entity types shared across lambdas
 */

// Export webhook types
export * from "./clerk-webhook-types";

// Export all payment-related types
export * from "./payment-types";

// Export all cashapp-related types
export * from "./cashapp-types";

// Export all KYC-related types
export * from "./kyc-types";

/**
 * User types for the financial payment system
 */
export type UserType = "super_admin" | "org_admin" | "agent" | "user";

/**
 * Application types for the financial payment system
 */
export type ApplicationType = "org_admin" | "agent";

/**
 * Application status
 */
export type ApplicationStatus = "PENDING_APPROVAL" | "APPROVED" | "REJECTED";

/**
 * User entity in DynamoDB
 * Partition Key: USER#{clerkUserId}
 * Sort Key: METADATA
 */
export interface User {
  PK: string; // USER#{clerkUserId}
  SK: string; // METADATA
  UserTypeLookupPK: string; // USER#{userType}
  UserTypeLookupSK: string; // CREATED#{timestamp}
  UsernameLookupPK: string; // USERNAME#{username} - for username lookups
  UsernameLookupSK: string; // METADATA

  clerkUserId: string; // Clerk user ID from authentication (primary identifier)
  username: string; // Human-readable, unique username (this IS the external ID)
  userType: UserType;
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  profileImageUrl?: string;
  isActive: boolean;
  organizationId?: string; // For org_admin and agent users
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
}

/**
 * Application entity for financial payment system
 * Supports both org_admin and agent applications
 */
export interface Application {
  PK: string; // APP#{applicationId}
  SK: string; // METADATA
  ApplicationTypeLookupPK: string; // APP_TYPE#{applicationType}
  ApplicationTypeLookupSK: string; // STATUS#{status}#CREATED#{timestamp}
  ApplicationEmailLookupPK?: string; // EMAIL#{email} - for email lookups
  ApplicationEmailLookupSK?: string; // STATUS#{status}

  applicationId: string;
  applicationType: ApplicationType; // "org_admin" | "agent"
  status: ApplicationStatus;
  applicantClerkUserId: string;
  applicantUsername: string; // Username of the applicant (this IS their external ID)
  applicantEmail: string;
  
  // For org_admin applications
  organizationName?: string;
  organizationDescription?: string;
  
  // For agent applications
  affiliateUsername?: string; // Username of the org admin they want to join
  targetOrganizationId?: string; // Organization they want to join
  
  // Common fields
  notes?: string;
  adminNotes?: string; // Notes from approver
  approvedBy?: string; // Clerk User ID of approver
  approvedAt?: string;
  rejectedReason?: string;
  
  // Result fields (populated upon approval)
  resultClerkOrganizationId?: string; // Clerk organization ID (pending webhook processing)
  resultUserType?: UserType; // Assigned user type
  
  createdAt: string;
  updatedAt: string;
}

/**
 * Organization entity for financial payment system
 * Simplified for payment/financial context
 */
export interface Organization {
  PK: string; // ORG#{clerkOrganizationId}
  SK: string; // METADATA
  OrganizationStatusLookupPK: string; // ORG_STATUS#{status}
  OrganizationStatusLookupSK: string; // CREATED#{timestamp}

  clerkOrganizationId: string; // Clerk organization ID (primary identifier)
  name: string;
  description?: string;
  status: "PENDING" | "ACTIVE" | "INACTIVE" | "SUSPENDED"; // PENDING until webhook confirms
  
  // Financial/Payment related fields (nullable for future use)
  paymentLimits?: {
    dailyLimit?: number;
    monthlyLimit?: number;
    transactionLimit?: number;
  };
  
  // Organization admin
  adminClerkUserId: string; // Clerk User ID of the org admin
  adminUsername: string; // Username of the org admin
  
  // Member management
  memberCount: number;
  maxMembers?: number; // Optional limit on organization size
  
  // Metadata
  logoUrl?: string;
  website?: string;
  phone?: string;
  email?: string;
  
  createdAt: string;
  updatedAt: string;
  createdBy: string; // Clerk User ID who created the organization
}

/**
 * Organization membership entity
 * Tracks users within organizations
 */
export interface OrganizationMembership {
  PK: string; // ORG#{organizationId}#MEMBER#{username}
  SK: string; // METADATA
  UserMembershipLookupPK: string; // USER#{username}
  UserMembershipLookupSK: string; // ORG#{organizationId}

  organizationId: string;
  username: string;
  userExternalId: string;
  role: "admin" | "agent";
  status: "ACTIVE" | "INACTIVE";
  joinedAt: string;
  invitedBy?: string; // Username of who invited them
}

/**
 * Username validation result
 */
export interface UsernameValidationResult {
  isValid: boolean;
  isAvailable: boolean;
  suggestions?: string[];
  reason?: string;
}

// Legacy types - keeping for backward compatibility but will be phased out

/**
 * Invitation code entity in DynamoDB
 */
export interface InvitationCode {
  PK: string; // TENANT#{tenantId}#CODE#{code}
  SK: string; // METADATA
  InvitationCodeLookupPK: string; // TENANT#{tenantId}#ORG#{orgId}#CODES
  InvitationCodeLookupSK: string; // STATUS#{status}#CREATED#{timestamp}

  tenantId: string;
  code: string;
  organizationId: string;
  organizationType: "AUTHORITY" | "SERVICE_PROVIDER" | "SYSTEM";
  role:
    | "AUTHORITY_ADMIN"
    | "AUTHORITY_USER"
    | "SERVICE_PROVIDER_ADMIN"
    | "FIRE_GUARD"
    | "PROPERTY_OWNER";
  createdBy: string;
  createdAt: string;
  validUntil?: string;
  usageLimit?: number;
  usageCount: number;
  active: boolean;
  description?: string;
}

/**
 * Legacy Organization entity - will be replaced by new Organization interface
 * @deprecated Use the new Organization interface instead
 */
export interface LegacyOrganization {
  PK: string; // TENANT#{tenantId}#ORG#{orgId}
  SK: string; // METADATA
  GSI1PK: string; // TENANT#{tenantId}#ORGS
  GSI1SK: string; // TYPE#{type}#NAME#{name}

  tenantId: string;
  orgId: string;
  name: string;
  type: "AUTHORITY" | "SERVICE_PROVIDER" | "PROPERTY_MANAGEMENT" | "SYSTEM";
  status: "ACTIVE" | "INACTIVE" | "PENDING" | "SUSPENDED";
  licenseNumber?: string;
  licenseExpiryDate?: string;
  address?: Address;
  phone?: string;
  email?: string;
  website?: string;
  primaryContactId?: string;
  logoUrl?: string;
  description?: string;
  complianceRating?: number;
  createdAt: string;
  updatedAt: string;
  verifiedAt?: string;
  verifiedBy?: string;
  settings?: OrganizationSettings;
  serviceArea?: string[];
  serviceTypes?: string[];
  clerkId?: string; // Clerk organization ID
}

/**
 * Organization settings
 */
export interface OrganizationSettings {
  allowGuardSignup: boolean;
  requireAdminApproval: boolean;
  notificationPreferences: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  checkInRequirements?: {
    requirePhoto: boolean;
    requireNotes: boolean;
    requireLocation: boolean;
    checkInInterval: number;
  };
  securitySettings?: {
    mfaRequired: boolean;
    sessionTimeout: number;
    ipRestrictions?: string[];
  };
}

/**
 * Address structure
 */
export interface Address {
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

/**
 * Response for invitation code verification
 */
export interface VerifyInvitationCodeResponse {
  valid: boolean;
  organizationId?: string;
  organizationName?: string;
  role?: string;
  organizationType?: string;
  message?: string;
}

/**
 * Standard error response
 */
export interface ErrorResponse {
  message: string;
  errorCode: string;
  details?: any;
}

/**
 * Request to verify invitation code
 */
export interface VerifyInvitationCodeRequest {
  code: string;
}
