/**
 * KYC (Know Your Customer) Types for Identity Verification
 * 
 * This module defines types for the KYC system that supports multiple providers
 * and maintains verification status and metadata for users.
 */

// KYC Provider Types
export type KYCProvider = "PROVE" | "PERSONA" | "ONFIDO" | "MANUAL";

// KYC Status Types
export type KYCStatus = 
  | "NOT_STARTED"
  | "IN_PROGRESS" 
  | "PENDING_REVIEW"
  | "VERIFIED"
  | "FAILED"
  | "EXPIRED"
  | "REJECTED";

// KYC Verification Level
export type KYCVerificationLevel = 
  | "BASIC"      // Basic identity verification
  | "ENHANCED"   // Enhanced verification with additional checks
  | "PREMIUM";   // Premium verification with full background checks

// Address Information
export interface KYCAddress {
  street: string;
  extendedAddress?: string;
  city: string;
  region: string; // State/Province
  postalCode: string;
  country?: string;
}

// Individual Information for KYC
export interface KYCIndividual {
  firstName: string;
  lastName: string;
  dateOfBirth: string; // ISO 8601 format (YYYY-MM-DD)
  ssn?: string; // Social Security Number (encrypted)
  phoneNumber?: string;
  emailAddresses?: string[];
  addresses?: KYCAddress[];
}

// KYC Verification Metadata
export interface KYCVerificationMetadata {
  provider: KYCProvider;
  providerTransactionId?: string;
  correlationId?: string;
  verificationLevel: KYCVerificationLevel;
  verificationMethod?: string;
  ipAddress?: string;
  userAgent?: string;
  deviceFingerprint?: string;
  documentTypes?: string[]; // Types of documents verified
  biometricData?: {
    faceMatch?: boolean;
    livenessCheck?: boolean;
  };
  riskScore?: number; // 0-100, higher is riskier
  confidenceScore?: number; // 0-100, higher is more confident
  flags?: string[]; // Any warning flags
  rawProviderResponse?: Record<string, any>; // Store full provider response
  completedAt?: string; // ISO timestamp when verification was completed
  verificationResult?: string; // Result of verification
  individual?: KYCIndividual; // Individual data
  providerResponse?: Record<string, any>; // Provider response data
  challengeCompleted?: boolean; // Whether challenge step was completed
  challengeData?: Record<string, any>; // Challenge step data
  retryCount?: number; // Number of retry attempts
  canRetryAfter?: string; // When retry is allowed
}

// KYC Verification Record
export interface KYCVerification {
  id: string;
  userId: string;
  clerkUserId: string; // Clerk user ID for cross-reference
  status: KYCStatus;
  verificationLevel: KYCVerificationLevel;
  provider: KYCProvider;
  individual?: KYCIndividual;
  metadata: KYCVerificationMetadata;
  verifiedAt?: string; // ISO timestamp
  expiresAt?: string; // ISO timestamp
  failureReason?: string;
  retryCount: number;
  maxRetries: number;
  createdAt: string;
  updatedAt: string;
  createdBy?: string; // User ID who initiated verification
  reviewedBy?: string; // Admin user ID who reviewed (for manual reviews)
  reviewNotes?: string;
}

// KYC Configuration
export interface KYCProviderConfig {
  provider: KYCProvider;
  isEnabled: boolean;
  environment: "SANDBOX" | "PRODUCTION";
  credentials: {
    clientId?: string;
    clientSecret?: string;
    apiKey?: string;
    webhookSecret?: string;
  };
  settings: {
    verificationLevel: KYCVerificationLevel;
    maxRetries: number;
    expirationDays: number;
    requireDocuments: boolean;
    requireBiometrics: boolean;
    requireAddressVerification: boolean;
  };
}

// Prove-specific types (for migration from Firebase)
export interface ProveV3StartRequest {
  phoneNumber: string;
  ssn: string; // last 4 digits
  flowType: "desktop" | "mobile";
  finalTargetUrl?: string;
  ipAddress?: string;
}

export interface ProveV3StartResponse {
  correlationId: string;
  authToken?: string;
  nextStep?: string;
  success: boolean;
  message?: string;
}

export interface ProveV3ValidateRequest {
  correlationId: string;
}

export interface ProveV3ValidateResponse {
  correlationId: string;
  isValid: boolean;
  nextStep?: string;
  success: boolean;
  message?: string;
}

export interface ProveV3ChallengeRequest {
  correlationId: string;
  dob?: string;
  last4SSN?: string;
}

export interface ProveV3ChallengeResponse {
  correlationId: string;
  challengeQuestions?: Array<{
    question: string;
    answers: string[];
  }>;
  success: boolean;
  message?: string;
}

export interface ProveV3CompleteRequest {
  correlationId: string;
  individual: KYCIndividual;
  userId: string;
}

export interface ProveV3CompleteResponse {
  correlationId: string;
  success: boolean;
  verificationResult?: {
    verified: boolean;
    riskScore: number;
    confidenceScore: number;
    flags: string[];
  };
  message?: string;
  individual?: KYCIndividual;
}

// KYC Service Request/Response Types
export interface StartKYCVerificationRequest {
  provider: KYCProvider;
  verificationLevel: KYCVerificationLevel;
  individual: Partial<KYCIndividual>;
  metadata?: Partial<KYCVerificationMetadata>;
}

export interface StartKYCVerificationResponse {
  verificationId: string;
  status: KYCStatus;
  nextStep?: string;
  authToken?: string;
  redirectUrl?: string;
  correlationId?: string;
}

export interface UpdateKYCVerificationRequest {
  verificationId: string;
  status?: KYCStatus;
  individual?: Partial<KYCIndividual>;
  metadata?: Partial<KYCVerificationMetadata>;
  failureReason?: string;
}

export interface GetKYCVerificationResponse {
  verification: KYCVerification;
  canRetry: boolean;
  nextRetryAt?: string;
}

// KYC Validation Result
export interface KYCValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// KYC Provider Interface (for implementing different providers)
export interface IKYCProvider {
  readonly name: KYCProvider;
  readonly isEnabled: boolean;
  
  startVerification(request: StartKYCVerificationRequest): Promise<StartKYCVerificationResponse>;
  validateStep(verificationId: string, data: Record<string, any>): Promise<any>;
  challengeStep(verificationId: string, data: Record<string, any>): Promise<any>;
  completeVerification(verificationId: string, data: Record<string, any>): Promise<any>;
  getVerificationStatus(verificationId: string): Promise<KYCStatus>;
  cancelVerification(verificationId: string): Promise<boolean>;
}

// Error types
export class KYCError extends Error {
  constructor(
    message: string,
    public code: string,
    public provider?: KYCProvider,
    public verificationId?: string,
    public retryable: boolean = false
  ) {
    super(message);
    this.name = "KYCError";
  }
}

export class KYCProviderError extends KYCError {
  constructor(
    message: string,
    public providerCode: string,
    provider: KYCProvider,
    verificationId?: string,
    retryable: boolean = false
  ) {
    super(message, `PROVIDER_ERROR_${providerCode}`, provider, verificationId, retryable);
    this.name = "KYCProviderError";
  }
}

// Constants
export const KYC_CONSTANTS = {
  DEFAULT_MAX_RETRIES: 3,
  DEFAULT_EXPIRATION_DAYS: 90,
  VERIFICATION_TIMEOUT_MINUTES: 30,
  CORRELATION_ID_LENGTH: 32,
  
  // Prove-specific constants
  PROVE: {
    ENVIRONMENTS: {
      SANDBOX: "uat-us",
      PRODUCTION: "prod-us"
    },
    FLOW_TYPES: ["desktop", "mobile"] as const,
    MAX_SSN_LENGTH: 4,
    PHONE_REGEX: /^\+?[1-9]\d{1,14}$/,
  }
} as const; 