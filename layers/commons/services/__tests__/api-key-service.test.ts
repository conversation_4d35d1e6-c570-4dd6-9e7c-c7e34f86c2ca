import { APIKeyService } from "../api-key-service";
import { <PERSON><PERSON><PERSON>, createAP<PERSON><PERSON><PERSON>, getAP<PERSON><PERSON><PERSON>, getAPIKeyByHash, listAPIKeysByOrganization } from "../../data/entities/api-key";
import { APIKeyAudit } from "../../data/entities/api-key-audit";
import { Organization } from "../../data/entities/organization";
import { logger } from "../../utils/logger";

// Mock dependencies
jest.mock("../../data/entities/api-key");
jest.mock("../../data/entities/api-key-audit");
jest.mock("../../data/entities/organization");
jest.mock("../../utils/logger");

describe("APIKeyService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("generateAPIKey", () => {
    const mockRequest = {
      organizationId: "org_123",
      tenantId: "tenant_123",
      userId: "user_123",
      userEmail: "<EMAIL>",
      type: "LIVE" as const,
      label: "Production API Key",
    };

    it("should generate a new API key successfully", async () => {
      // Mock organization validation
      (Organization.getById as jest.Mock).mockResolvedValue({
        id: "org_123",
        status: "ACTIVE",
      });

      // Mock API key creation
      const mockApiKey = {
        id: "key_123",
        keyPrefix: "ckpl_12345678",
        type: "LIVE",
        label: "Production API Key",
        createdAt: new Date().toISOString(),
      };
      (createAPIKey as jest.Mock).mockResolvedValue(mockApiKey);

      // Mock audit log
      (APIKeyAudit.logCreation as jest.Mock).mockResolvedValue(undefined);

      const result = await APIKeyService.generateAPIKey(mockRequest);

      expect(result).toMatchObject({
        keyId: "key_123",
        keyPrefix: "ckpl_12345678",
        type: "LIVE",
        label: "Production API Key",
      });
      expect(result.keySecret).toMatch(/^ckpl_/);
      expect(Organization.getById).toHaveBeenCalledWith("tenant_123", "org_123");
      expect(createAPIKey).toHaveBeenCalled();
      expect(APIKeyAudit.logCreation).toHaveBeenCalled();
    });

    it("should throw error if organization is not active", async () => {
      (Organization.getById as jest.Mock).mockResolvedValue({
        id: "org_123",
        status: "SUSPENDED",
      });

      await expect(APIKeyService.generateAPIKey(mockRequest)).rejects.toThrow("Invalid organization");
      expect(createAPIKey).not.toHaveBeenCalled();
    });

    it("should throw error if organization not found", async () => {
      (Organization.getById as jest.Mock).mockResolvedValue(null);

      await expect(APIKeyService.generateAPIKey(mockRequest)).rejects.toThrow("Invalid organization");
      expect(createAPIKey).not.toHaveBeenCalled();
    });

    it("should use default rate limits if not provided", async () => {
      (Organization.getById as jest.Mock).mockResolvedValue({
        id: "org_123",
        status: "ACTIVE",
      });

      (createAPIKey as jest.Mock).mockImplementation((apiKey) => {
        expect(apiKey.rateLimit).toEqual({
          requestsPerSecond: 10,
          requestsPerMinute: 100,
          requestsPerHour: 1000,
          requestsPerDay: 10000,
        });
        return Promise.resolve(apiKey);
      });

      await APIKeyService.generateAPIKey(mockRequest);
    });
  });

  describe("validateAPIKey", () => {
    const mockRequest = {
      apiKey: "ckpl_test123456789",
      endpoint: "/api/v1/users",
      method: "GET",
      ip: "***********",
      userAgent: "Mozilla/5.0",
    };

    it("should validate a valid API key successfully", async () => {
      const mockApiKey = {
        id: "key_123",
        tenantId: "tenant_123",
        organizationId: "org_123",
        keyPrefix: "ckpl_test1234",
        status: "ACTIVE",
        isValid: true,
        permissions: {
          allowedEndpoints: ["/api/v1/*"],
        },
        rateLimit: {
          requestsPerSecond: 10,
        },
        updateUsage: jest.fn(),
        update: jest.fn(),
      };

      (getAPIKeyByHash as jest.Mock).mockResolvedValue(mockApiKey);
      (APIKeyAudit.logAccess as jest.Mock).mockResolvedValue(undefined);

      const result = await APIKeyService.validateAPIKey(mockRequest);

      expect(result).toEqual({
        isValid: true,
        keyId: "key_123",
        organizationId: "org_123",
        tenantId: "tenant_123",
        permissions: mockApiKey.permissions,
        rateLimit: mockApiKey.rateLimit,
      });
      expect(mockApiKey.updateUsage).toHaveBeenCalledWith("***********", "Mozilla/5.0");
      expect(mockApiKey.update).toHaveBeenCalled();
      expect(APIKeyAudit.logAccess).toHaveBeenCalled();
    });

    it("should reject key with invalid format", async () => {
      const result = await APIKeyService.validateAPIKey({
        ...mockRequest,
        apiKey: "invalid_key_format",
      });

      expect(result).toEqual({
        isValid: false,
        errorCode: "INVALID_FORMAT",
        errorMessage: "Invalid API key format",
      });
      expect(getAPIKeyByHash).not.toHaveBeenCalled();
      expect(APIKeyAudit.logFailedValidation).toHaveBeenCalled();
    });

    it("should reject non-existent key", async () => {
      (getAPIKeyByHash as jest.Mock).mockResolvedValue(null);

      const result = await APIKeyService.validateAPIKey(mockRequest);

      expect(result).toEqual({
        isValid: false,
        errorCode: "KEY_NOT_FOUND",
        errorMessage: "API key not found",
      });
      expect(APIKeyAudit.logFailedValidation).toHaveBeenCalled();
    });

    it("should reject inactive key", async () => {
      const mockApiKey = {
        id: "key_123",
        keyPrefix: "ckpl_test1234",
        status: "REVOKED",
        isValid: false,
      };

      (getAPIKeyByHash as jest.Mock).mockResolvedValue(mockApiKey);

      const result = await APIKeyService.validateAPIKey(mockRequest);

      expect(result).toEqual({
        isValid: false,
        keyId: "key_123",
        errorCode: "KEY_INACTIVE",
        errorMessage: "Key is revoked",
      });
      expect(APIKeyAudit.logFailedValidation).toHaveBeenCalled();
    });

    it("should reject expired key", async () => {
      const mockApiKey = {
        id: "key_123",
        keyPrefix: "ckpl_test1234",
        status: "ACTIVE",
        isValid: false,
      };

      (getAPIKeyByHash as jest.Mock).mockResolvedValue(mockApiKey);

      const result = await APIKeyService.validateAPIKey(mockRequest);

      expect(result).toEqual({
        isValid: false,
        keyId: "key_123",
        errorCode: "KEY_EXPIRED",
        errorMessage: "Key is expired",
      });
    });

    it("should check endpoint permissions", async () => {
      const mockApiKey = {
        id: "key_123",
        keyPrefix: "ckpl_test1234",
        status: "ACTIVE",
        isValid: true,
        permissions: {
          allowedEndpoints: ["/api/v2/*"],
        },
        updateUsage: jest.fn(),
        update: jest.fn(),
      };

      (getAPIKeyByHash as jest.Mock).mockResolvedValue(mockApiKey);

      const result = await APIKeyService.validateAPIKey(mockRequest);

      expect(result).toEqual({
        isValid: false,
        keyId: "key_123",
        errorCode: "ENDPOINT_NOT_ALLOWED",
        errorMessage: "This API key is not authorized for this endpoint",
      });
    });

    it("should check IP whitelist", async () => {
      const mockApiKey = {
        id: "key_123",
        keyPrefix: "ckpl_test1234",
        status: "ACTIVE",
        isValid: true,
        permissions: {
          ipWhitelist: ["********"],
        },
        updateUsage: jest.fn(),
        update: jest.fn(),
      };

      (getAPIKeyByHash as jest.Mock).mockResolvedValue(mockApiKey);

      const result = await APIKeyService.validateAPIKey(mockRequest);

      expect(result).toEqual({
        isValid: false,
        keyId: "key_123",
        errorCode: "IP_NOT_ALLOWED",
        errorMessage: "This IP address is not authorized",
      });
    });
  });

  describe("listAPIKeys", () => {
    it("should list all API keys for an organization", async () => {
      const mockKeys = [
        {
          id: "key_1",
          keyPrefix: "ckpl_1234",
          label: "Key 1",
          type: "LIVE",
          status: "ACTIVE",
          createdAt: "2024-01-01T00:00:00Z",
          metadata: {
            lastUsedAt: "2024-01-02T00:00:00Z",
            usageCount: 100,
          },
        },
        {
          id: "key_2",
          keyPrefix: "ckpl_5678",
          label: "Key 2",
          type: "TEST",
          status: "REVOKED",
          createdAt: "2024-01-01T00:00:00Z",
        },
      ];

      (listAPIKeysByOrganization as jest.Mock).mockResolvedValue(mockKeys);

      const result = await APIKeyService.listAPIKeys("tenant_123", "org_123");

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        keyId: "key_1",
        keyPrefix: "ckpl_1234",
        label: "Key 1",
        type: "LIVE",
        status: "ACTIVE",
        lastUsedAt: "2024-01-02T00:00:00Z",
        usageCount: 100,
      });
      expect(listAPIKeysByOrganization).toHaveBeenCalledWith("tenant_123", "org_123", undefined);
    });

    it("should filter by status", async () => {
      (listAPIKeysByOrganization as jest.Mock).mockResolvedValue([]);

      await APIKeyService.listAPIKeys("tenant_123", "org_123", "ACTIVE");

      expect(listAPIKeysByOrganization).toHaveBeenCalledWith("tenant_123", "org_123", "ACTIVE");
    });
  });

  describe("revokeAPIKey", () => {
    it("should revoke an API key successfully", async () => {
      const mockApiKey = {
        id: "key_123",
        status: "ACTIVE",
        revoke: jest.fn(),
        update: jest.fn(),
      };

      (getAPIKey as jest.Mock).mockResolvedValue(mockApiKey);
      (APIKeyAudit.logCreation as jest.Mock).mockResolvedValue(undefined);

      await APIKeyService.revokeAPIKey("tenant_123", "org_123", "key_123", "user_123", "Compromised");

      expect(mockApiKey.revoke).toHaveBeenCalledWith("user_123", "Compromised");
      expect(mockApiKey.update).toHaveBeenCalled();
      expect(APIKeyAudit.logCreation).toHaveBeenCalled();
    });

    it("should skip if key already revoked", async () => {
      const mockApiKey = {
        id: "key_123",
        status: "REVOKED",
        revoke: jest.fn(),
        update: jest.fn(),
      };

      (getAPIKey as jest.Mock).mockResolvedValue(mockApiKey);

      await APIKeyService.revokeAPIKey("tenant_123", "org_123", "key_123", "user_123");

      expect(mockApiKey.revoke).not.toHaveBeenCalled();
      expect(mockApiKey.update).not.toHaveBeenCalled();
      expect(logger.warn).toHaveBeenCalledWith("API key already revoked", { keyId: "key_123" });
    });
  });

  describe("updateAPIKeyLabel", () => {
    it("should update API key label successfully", async () => {
      const mockApiKey = {
        id: "key_123",
        label: "Old Label",
        updateTimestamp: jest.fn(),
        update: jest.fn(),
      };

      (getAPIKey as jest.Mock).mockResolvedValue(mockApiKey);
      (APIKeyAudit.logCreation as jest.Mock).mockResolvedValue(undefined);

      await APIKeyService.updateAPIKeyLabel("tenant_123", "org_123", "key_123", "New Label", "user_123");

      expect(mockApiKey.label).toBe("New Label");
      expect(mockApiKey.updateTimestamp).toHaveBeenCalled();
      expect(mockApiKey.update).toHaveBeenCalled();
      expect(APIKeyAudit.logCreation).toHaveBeenCalledWith(
        "tenant_123",
        "org_123",
        "key_123",
        "user_123",
        undefined,
        {
          action: "UPDATED",
          changes: {
            label: { old: "Old Label", new: "New Label" },
          },
        }
      );
    });
  });

  describe("getAPIKeyDetails", () => {
    it("should get API key details successfully", async () => {
      const mockApiKey = {
        id: "key_123",
        keyPrefix: "ckpl_1234",
        label: "Test Key",
        type: "LIVE",
        status: "ACTIVE",
        createdAt: "2024-01-01T00:00:00Z",
        expiresAt: "2025-01-01T00:00:00Z",
        metadata: {
          lastUsedAt: "2024-01-02T00:00:00Z",
          usageCount: 50,
        },
        rateLimit: {
          requestsPerSecond: 10,
        },
        permissions: {
          allowedEndpoints: ["/api/v1/*"],
        },
      };

      (getAPIKey as jest.Mock).mockResolvedValue(mockApiKey);

      const result = await APIKeyService.getAPIKeyDetails("tenant_123", "org_123", "key_123");

      expect(result).toMatchObject({
        keyId: "key_123",
        keyPrefix: "ckpl_1234",
        label: "Test Key",
        type: "LIVE",
        status: "ACTIVE",
        lastUsedAt: "2024-01-02T00:00:00Z",
        usageCount: 50,
        rateLimit: mockApiKey.rateLimit,
        permissions: mockApiKey.permissions,
      });
    });
  });

  describe("getAPIKeyAuditLogs", () => {
    it("should get API key audit logs successfully", async () => {
      const mockAudits = [
        { action: "CREATED", timestamp: "2024-01-01T00:00:00Z" },
        { action: "ACCESSED", timestamp: "2024-01-02T00:00:00Z" },
      ];

      (APIKeyAudit.getByAPIKey as jest.Mock).mockResolvedValue({
        audits: mockAudits,
      });

      const result = await APIKeyService.getAPIKeyAuditLogs("tenant_123", "org_123", "key_123", {
        startTime: "2024-01-01T00:00:00Z",
        endTime: "2024-01-03T00:00:00Z",
        limit: 10,
      });

      expect(result).toEqual(mockAudits);
      expect(APIKeyAudit.getByAPIKey).toHaveBeenCalledWith("tenant_123", "org_123", "key_123", {
        startTime: "2024-01-01T00:00:00Z",
        endTime: "2024-01-03T00:00:00Z",
        limit: 10,
      });
    });
  });

  describe("matchEndpoint", () => {
    it("should match exact endpoints", () => {
      expect(APIKeyService["matchEndpoint"]("/api/v1/users", "/api/v1/users")).toBe(true);
      expect(APIKeyService["matchEndpoint"]("/api/v1/users", "/api/v1/posts")).toBe(false);
    });

    it("should match wildcard patterns", () => {
      expect(APIKeyService["matchEndpoint"]("/api/v1/users", "/api/v1/*")).toBe(true);
      expect(APIKeyService["matchEndpoint"]("/api/v2/users", "/api/v1/*")).toBe(false);
      expect(APIKeyService["matchEndpoint"]("/api/v1/users/123", "/api/v1/users/*")).toBe(true);
      expect(APIKeyService["matchEndpoint"]("/api/v1/posts/123", "/api/*/posts/*")).toBe(true);
    });
  });
}); 