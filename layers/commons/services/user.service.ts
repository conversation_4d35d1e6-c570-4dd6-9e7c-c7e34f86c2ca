/**
 * User Service for Financial Payment System
 * 
 * Handles user creation, username validation, and user management operations.
 * Uses Clerk user ID as primary identifier and username as external ID.
 */

import { DynamoDB } from "aws-sdk";
import { Logger } from "@aws-lambda-powertools/logger";
import { getDocumentClient } from "../data/client";
import { 
  User, 
  UserType, 
  UsernameValidationResult,
  ClerkUserData 
} from "../types";

const logger = new Logger({
  serviceName: "user-service",
  logLevel: process.env.LOG_LEVEL as any || "INFO",
});

/**
 * UserService class for managing users in the financial payment system
 */
export class UserService {
  private static readonly TABLE_NAME = process.env.TABLE_NAME || "clkk-payment-system";
  private static readonly USERNAME_MIN_LENGTH = 3;
  private static readonly USERNAME_MAX_LENGTH = 20;
  private static readonly USERNAME_PATTERN = /^[a-zA-Z0-9_]+$/;
  private static readonly RESERVED_USERNAMES = [
    "admin", "root", "system", "api", "support", "help", "info",
    "clkk", "payment", "cash", "app", "user", "test", "demo"
  ];

  /**
   * Create a new user from Clerk webhook data
   * @param clerkUserData - User data from Clerk webhook
   * @param username - Chosen username (must be validated first) - this IS the external ID
   * @param userType - Type of user (defaults to 'agent')
   * @returns Created user
   */
  static async createUser(
    clerkUserData: ClerkUserData,
    username: string,
    userType: UserType = "agent"
  ): Promise<User> {
    logger.info("Creating new user", { 
      clerkUserId: clerkUserData.id, 
      username, 
      userType 
    });

    // Validate username availability
    const usernameValidation = await this.validateUsername(username);
    if (!usernameValidation.isValid || !usernameValidation.isAvailable) {
      throw new Error(`Username "${username}" is not available: ${usernameValidation.reason}`);
    }

    // Extract primary email
    const primaryEmail = this.extractPrimaryEmail(clerkUserData);
    if (!primaryEmail) {
      throw new Error("User must have a primary email address");
    }

    // Create user entity
    const now = new Date().toISOString();
    const user: User = {
      PK: `USER#${clerkUserData.id}`,
      SK: "METADATA",
      UserTypeLookupPK: `USER#${userType}`,
      UserTypeLookupSK: `CREATED#${now}`,
      UsernameLookupPK: `USERNAME#${username}`,
      UsernameLookupSK: "METADATA",
      
      clerkUserId: clerkUserData.id,
      username, // This IS the external ID
      userType,
      email: primaryEmail,
      firstName: clerkUserData.first_name,
      lastName: clerkUserData.last_name,
      phone: this.extractPrimaryPhone(clerkUserData) || undefined,
      profileImageUrl: clerkUserData.image_url,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    };

    // Save to DynamoDB
    const docClient = getDocumentClient();
    await docClient.put({
      TableName: this.TABLE_NAME,
      Item: user,
      ConditionExpression: "attribute_not_exists(PK)", // Ensure Clerk user ID is unique
    }).promise();

    logger.info("User created successfully", { 
      clerkUserId: clerkUserData.id,
      username, 
      userType 
    });

    return user;
  }

  /**
   * Validate username format and availability
   * @param username - Username to validate
   * @returns Validation result with suggestions if invalid
   */
  static async validateUsername(username: string): Promise<UsernameValidationResult> {
    // Check format
    if (!username || username.length < this.USERNAME_MIN_LENGTH) {
      return {
        isValid: false,
        isAvailable: false,
        reason: `Username must be at least ${this.USERNAME_MIN_LENGTH} characters long`,
        suggestions: await this.generateUsernameSuggestions(username),
      };
    }

    if (username.length > this.USERNAME_MAX_LENGTH) {
      return {
        isValid: false,
        isAvailable: false,
        reason: `Username must be no more than ${this.USERNAME_MAX_LENGTH} characters long`,
        suggestions: await this.generateUsernameSuggestions(username),
      };
    }

    if (!this.USERNAME_PATTERN.test(username)) {
      return {
        isValid: false,
        isAvailable: false,
        reason: "Username can only contain letters, numbers, and underscores",
        suggestions: await this.generateUsernameSuggestions(username),
      };
    }

    // Check reserved usernames
    if (this.RESERVED_USERNAMES.includes(username.toLowerCase())) {
      return {
        isValid: false,
        isAvailable: false,
        reason: "Username is reserved",
        suggestions: await this.generateUsernameSuggestions(username),
      };
    }

    // Check availability in database
    const isAvailable = await this.isUsernameAvailable(username);
    
    if (!isAvailable) {
      return {
        isValid: true,
        isAvailable: false,
        reason: "Username is already taken",
        suggestions: await this.generateUsernameSuggestions(username),
      };
    }

    return {
      isValid: true,
      isAvailable: true,
    };
  }

  /**
   * Check if username is available in the database
   * @param username - Username to check
   * @returns True if available, false if taken
   */
  private static async isUsernameAvailable(username: string): Promise<boolean> {
    try {
      const docClient = getDocumentClient();
      const result = await docClient.query({
        TableName: this.TABLE_NAME,
        IndexName: "UsernameLookupIndex", // Username index
        KeyConditionExpression: "UsernameLookupPK = :username AND UsernameLookupSK = :sk",
        ExpressionAttributeValues: {
          ":username": `USERNAME#${username}`,
          ":sk": "METADATA",
        },
        Limit: 1,
      }).promise();

      return !result.Items || result.Items.length === 0; // Available if no item found
    } catch (error) {
      logger.error("Error checking username availability", { error, username });
      throw new Error("Failed to check username availability");
    }
  }

  /**
   * Generate username suggestions based on a base username
   * @param baseUsername - Base username to generate suggestions from
   * @returns Array of suggested usernames
   */
  private static async generateUsernameSuggestions(baseUsername: string): Promise<string[]> {
    const suggestions: string[] = [];
    const cleanBase = baseUsername.replace(/[^a-zA-Z0-9]/g, "").toLowerCase();
    
    if (cleanBase.length < this.USERNAME_MIN_LENGTH) {
      // If base is too short, suggest some generic options
      const genericSuggestions = ["user", "member", "agent"];
      for (const generic of genericSuggestions) {
        const randomSuffix = Math.floor(Math.random() * 9999).toString().padStart(4, "0");
        suggestions.push(`${generic}${randomSuffix}`);
      }
    } else {
      // Generate variations of the base username
      for (let i = 1; i <= 5; i++) {
        const randomSuffix = Math.floor(Math.random() * 999).toString().padStart(3, "0");
        suggestions.push(`${cleanBase}${randomSuffix}`);
        suggestions.push(`${cleanBase}_${randomSuffix}`);
      }
    }

    // Filter out suggestions that are too long and check availability
    const validSuggestions: string[] = [];
    for (const suggestion of suggestions) {
      if (suggestion.length <= this.USERNAME_MAX_LENGTH) {
        const isAvailable = await this.isUsernameAvailable(suggestion);
        if (isAvailable) {
          validSuggestions.push(suggestion);
        }
      }
      
      // Return first 3 valid suggestions
      if (validSuggestions.length >= 3) {
        break;
      }
    }

    return validSuggestions;
  }

  /**
   * Get user by Clerk user ID
   * @param clerkUserId - Clerk user ID to lookup
   * @returns User if found, null otherwise
   */
  static async getUserByClerkId(clerkUserId: string): Promise<User | null> {
    try {
      const docClient = getDocumentClient();
      const result = await docClient.get({
        TableName: this.TABLE_NAME,
        Key: {
          PK: `USER#${clerkUserId}`,
          SK: "METADATA",
        },
      }).promise();

      return result.Item as User || null;
    } catch (error) {
      logger.error("Error getting user by Clerk ID", { error, clerkUserId });
      throw new Error("Failed to get user by Clerk ID");
    }
  }

  /**
   * Get user by username (external ID)
   * @param username - Username to lookup
   * @returns User if found, null otherwise
   */
  static async getUserByUsername(username: string): Promise<User | null> {
    try {
      const docClient = getDocumentClient();
      const result = await docClient.query({
        TableName: this.TABLE_NAME,
        IndexName: "UsernameLookupIndex", // Username index
        KeyConditionExpression: "UsernameLookupPK = :username AND UsernameLookupSK = :sk",
        ExpressionAttributeValues: {
          ":username": `USERNAME#${username}`,
          ":sk": "METADATA",
        },
        Limit: 1,
      }).promise();

      return result.Items?.[0] as User || null;
    } catch (error) {
      logger.error("Error getting user by username", { error, username });
      throw new Error("Failed to get user by username");
    }
  }

  /**
   * Update user's organization membership
   * @param clerkUserId - Clerk user ID of the user
   * @param organizationId - Organization ID to assign
   * @param userType - New user type
   * @returns Updated user
   */
  static async updateUserOrganization(
    clerkUserId: string,
    organizationId: string,
    userType: UserType
  ): Promise<User> {
    try {
      const docClient = getDocumentClient();
      const now = new Date().toISOString();

      const result = await docClient.update({
        TableName: this.TABLE_NAME,
        Key: {
          PK: `USER#${clerkUserId}`,
          SK: "METADATA",
        },
        UpdateExpression: "SET organizationId = :orgId, userType = :userType, updatedAt = :updatedAt, UserTypeLookupPK = :userTypeLookupPK",
        ExpressionAttributeValues: {
          ":orgId": organizationId,
          ":userType": userType,
          ":updatedAt": now,
          ":userTypeLookupPK": `USER#${userType}`,
        },
        ReturnValues: "ALL_NEW",
      }).promise();

      return result.Attributes as User;
    } catch (error) {
      logger.error("Error updating user organization", { error, clerkUserId, organizationId });
      throw new Error("Failed to update user organization");
    }
  }

  /**
   * Extract primary email from Clerk user data
   * @param clerkUserData - Clerk user data
   * @returns Primary email address or null
   */
  private static extractPrimaryEmail(clerkUserData: ClerkUserData): string | null {
    if (!clerkUserData.email_addresses || clerkUserData.email_addresses.length === 0) {
      return null;
    }

    // Find primary email
    const primaryEmail = clerkUserData.email_addresses.find(
      email => email.id === clerkUserData.primary_email_address_id
    );

    if (primaryEmail) {
      return primaryEmail.email_address;
    }

    // Fallback to first email if no primary is set
    return clerkUserData.email_addresses[0]?.email_address || null;
  }

  /**
   * Extract primary phone from Clerk user data
   * @param clerkUserData - Clerk user data
   * @returns Primary phone number or null
   */
  private static extractPrimaryPhone(clerkUserData: ClerkUserData): string | null {
    if (!clerkUserData.phone_numbers || clerkUserData.phone_numbers.length === 0) {
      return null;
    }

    // Find primary phone
    const primaryPhone = clerkUserData.phone_numbers.find(
      phone => phone.id === clerkUserData.primary_phone_number_id
    );

    if (primaryPhone) {
      return primaryPhone.phone_number;
    }

    // Fallback to first phone if no primary is set
    return clerkUserData.phone_numbers[0]?.phone_number || null;
  }

  /**
   * List users by type
   * @param userType - Type of users to list
   * @param limit - Maximum number of users to return
   * @returns Array of users
   */
  static async listUsersByType(userType: UserType, limit: number = 50): Promise<User[]> {
    try {
      const docClient = getDocumentClient();
      
      const result = await docClient.query({
        TableName: this.TABLE_NAME,
        IndexName: "UserTypeLookupIndex", // User type index
        KeyConditionExpression: "UserTypeLookupPK = :userType",
        ExpressionAttributeValues: {
          ":userType": `USER#${userType}`,
        },
        Limit: limit,
        ScanIndexForward: false, // Most recent first
      }).promise();

      return result.Items as User[] || [];
    } catch (error) {
      logger.error("Error listing users by type", { error, userType });
      throw new Error("Failed to list users by type");
    }
  }

  /**
   * Update user's KYC status
   * @param userId - User ID
   * @param clerkUserId - Clerk user ID
   * @param status - KYC status
   * @param level - Verification level
   * @param provider - KYC provider
   * @returns Updated user
   */
  static async updateKYCStatus(
    userId: string,
    clerkUserId: string,
    status: string,
    level: string,
    provider: string
  ): Promise<User> {
    try {
      const docClient = getDocumentClient();
      const now = new Date().toISOString();

      const result = await docClient.update({
        TableName: this.TABLE_NAME,
        Key: {
          PK: `USER#${clerkUserId}`,
          SK: "METADATA",
        },
        UpdateExpression: "SET kycStatus = :status, kycLevel = :level, kycProvider = :provider, kycVerifiedAt = :verifiedAt, updatedAt = :updatedAt",
        ExpressionAttributeValues: {
          ":status": status,
          ":level": level,
          ":provider": provider,
          ":verifiedAt": now,
          ":updatedAt": now,
        },
        ReturnValues: "ALL_NEW",
      }).promise();

      logger.info("User KYC status updated", { 
        clerkUserId, 
        status, 
        level, 
        provider 
      });

      return result.Attributes as User;
    } catch (error) {
      logger.error("Error updating user KYC status", { error, clerkUserId, status });
      throw new Error("Failed to update user KYC status");
    }
  }
} 