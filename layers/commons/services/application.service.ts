/**
 * Application Service for Financial Payment System
 *
 * Handles both org_admin and agent applications with proper validation,
 * workflow management, and approval processes.
 */

import { DynamoDB } from "aws-sdk";
import { Logger } from "@aws-lambda-powertools/logger";
import { getDocumentClient } from "../data/client";
import {
  Application,
  ApplicationType,
  ApplicationStatus,
  UserType
} from "../types";
import { UUID } from "../utils/uuid";
import { UserService } from "./user.service";
import { ClerkService } from "./clerk-service";
import { User, getUserByEmail } from "../data/entities/user";
import { Organization } from "../data/entities/organization";

const logger = new Logger({
  serviceName: "application-service",
  logLevel: process.env.LOG_LEVEL as any || "INFO",
});

/**
 * Request interface for submitting org_admin application
 */
export interface SubmitOrgAdminApplicationRequest {
  organizationName: string;
  organizationDescription?: string;
  notes?: string;
}

/**
 * Request interface for submitting agent application
 */
export interface SubmitAgentApplicationRequest {
  affiliateUsername: string; // Username of the org admin they want to join
  notes?: string;
}

/**
 * Request interface for approving applications
 */
export interface ApproveApplicationRequest {
  applicationId: string;
  approved: boolean;
  adminNotes?: string;
  rejectionReason?: string;
}

/**
 * ApplicationService class for managing applications in the financial payment system
 */
export class ApplicationService {
  private static readonly TABLE_NAME = process.env.TABLE_NAME || "clkk-payment-system";

  /**
   * Submit an org_admin application
   * @param userId - User ID of the applicant (from Entity system)
   * @param clerkUserId - Clerk user ID of the applicant
   * @param email - Email of the applicant
   * @param request - Application request data
   * @returns Created application
   */
  static async submitOrgAdminApplication(
    userId: string,
    clerkUserId: string,
    email: string,
    request: SubmitOrgAdminApplicationRequest
  ): Promise<Application> {
    logger.info("Submitting org_admin application", {
      userId,
      clerkUserId,
      organizationName: request.organizationName
    });

    // Validate that user doesn't already have a pending application
    await this.validateNoPendingApplicationByUserId(userId);

    // Validate organization name uniqueness (basic check)
    await this.validateOrganizationNameAvailability(request.organizationName);

    // Create application
    const now = new Date().toISOString();
    const applicationId = UUID.v7();

    const application: Application = {
      PK: `APP#${applicationId}`,
      SK: "METADATA",
      ApplicationTypeLookupPK: `APP_TYPE#org_admin`,
      ApplicationTypeLookupSK: `STATUS#PENDING_APPROVAL#CREATED#${now}`,
      ApplicationUserLookupPK: `USER#${userId}`,
      ApplicationUserLookupSK: `STATUS#PENDING_APPROVAL`,

      applicationId,
      applicationType: "org_admin",
      status: "PENDING_APPROVAL",
      applicantUserId: userId,
      applicantClerkUserId: clerkUserId,
      applicantEmail: email,
      organizationName: request.organizationName,
      organizationDescription: request.organizationDescription,
      notes: request.notes,
      createdAt: now,
      updatedAt: now,
    };

    // Save to DynamoDB
    const docClient = getDocumentClient();
    await docClient.put({
      TableName: this.TABLE_NAME,
      Item: application,
    }).promise();

    logger.info("Org admin application submitted successfully", {
      applicationId,
      organizationName: request.organizationName
    });

    return application;
  }

  /**
   * Submit an agent application
   * @param userId - User ID of the applicant (from Entity system)
   * @param clerkUserId - Clerk user ID of the applicant
   * @param email - Email of the applicant
   * @param request - Application request data
   * @returns Created application
   */
  static async submitAgentApplication(
    userId: string,
    clerkUserId: string,
    email: string,
    request: SubmitAgentApplicationRequest
  ): Promise<Application> {
    logger.info("Submitting agent application", {
      userId,
      clerkUserId,
      affiliateUsername: request.affiliateUsername
    });

    // Validate that user doesn't already have a pending application
    await this.validateNoPendingApplicationByUserId(userId);

    // Validate that the affiliate username exists and is an org_admin
    const affiliateUser = await this.validateAffiliateUsername(request.affiliateUsername);

    // Create application
    const now = new Date().toISOString();
    const applicationId = UUID.v7();

    const application: Application = {
      PK: `APP#${applicationId}`,
      SK: "METADATA",
      ApplicationTypeLookupPK: `APP_TYPE#agent`,
      ApplicationTypeLookupSK: `STATUS#PENDING_APPROVAL#CREATED#${now}`,
      ApplicationUserLookupPK: `USER#${userId}`,
      ApplicationUserLookupSK: `STATUS#PENDING_APPROVAL`,

      applicationId,
      applicationType: "agent",
      status: "PENDING_APPROVAL",
      applicantUserId: userId,
      applicantClerkUserId: clerkUserId,
      applicantEmail: email,
      affiliateUsername: request.affiliateUsername,
      targetOrganizationId: affiliateUser.organizationId,
      notes: request.notes,
      createdAt: now,
      updatedAt: now,
    };

    // Save to DynamoDB
    const docClient = getDocumentClient();
    await docClient.put({
      TableName: this.TABLE_NAME,
      Item: application,
    }).promise();

    logger.info("Agent application submitted successfully", {
      applicationId,
      affiliateUsername: request.affiliateUsername
    });

    return application;
  }

  /**
   * Approve or reject an application
   * @param request - Approval request data
   * @param approverUserId - User ID of the approver
   * @returns Updated application
   */
  static async approveApplication(
    request: ApproveApplicationRequest,
    approverUserId: string
  ): Promise<Application> {
    logger.info("Processing application approval", {
      applicationId: request.applicationId,
      approved: request.approved,
      approverUserId
    });

    // Get the application
    const application = await this.getApplicationById(request.applicationId);
    if (!application) {
      throw new Error(`Application not found: ${request.applicationId}`);
    }

    if (application.status !== "PENDING_APPROVAL") {
      throw new Error(`Application is not pending approval. Current status: ${application.status}`);
    }

    const now = new Date().toISOString();
    const newStatus: ApplicationStatus = request.approved ? "APPROVED" : "REJECTED";

    if (request.approved) {
      // Process approval based on application type
      if (application.applicationType === "org_admin") {
        await this.processOrgAdminApproval(application, approverUserId);
      } else if (application.applicationType === "agent") {
        await this.processAgentApproval(application, approverUserId);
      }
    }

    // Update application status
    const docClient = getDocumentClient();
    const updateResult = await docClient.update({
      TableName: this.TABLE_NAME,
      Key: {
        PK: application.PK,
        SK: application.SK,
      },
      UpdateExpression: "SET #status = :status, adminNotes = :adminNotes, approvedBy = :approvedBy, approvedAt = :approvedAt, updatedAt = :updatedAt, GSI1SK = :gsi1sk, GSI2SK = :gsi2sk" +
        (request.rejectionReason ? ", rejectionReason = :rejectionReason" : ""),
      ExpressionAttributeNames: {
        "#status": "status",
      },
      ExpressionAttributeValues: {
        ":status": newStatus,
        ":adminNotes": request.adminNotes || "",
        ":approvedBy": approverUserId,
        ":approvedAt": now,
        ":updatedAt": now,
        ":gsi1sk": `STATUS#${newStatus}#CREATED#${application.createdAt}`,
        ":gsi2sk": `STATUS#${newStatus}`,
        ...(request.rejectionReason && { ":rejectionReason": request.rejectionReason }),
      },
      ReturnValues: "ALL_NEW",
    }).promise();

    const updatedApplication = updateResult.Attributes as Application;

    logger.info("Application processed successfully", {
      applicationId: request.applicationId,
      status: newStatus
    });

    return updatedApplication;
  }

  /**
   * Process org_admin application approval
   * Creates organization and updates existing user's role
   */
  private static async processOrgAdminApproval(
    application: Application,
    approverUserId: string
  ): Promise<void> {
    logger.info("Processing org admin approval", {
      applicationId: application.applicationId,
      organizationName: application.organizationName,
      applicantUserId: application.applicantUserId
    });

    if (!application.organizationName) {
      throw new Error("Organization name is required for org admin applications");
    }

    if (!application.applicantUserId) {
      throw new Error("Applicant user ID is required for org admin applications");
    }

    try {
      // Get the existing user from User entity system
      const existingUser = await User.getById(application.applicantUserId!);
      if (!existingUser) {
        throw new Error(`User not found: ${application.applicantUserId}`);
      }

      // Create organization using Organization entity
      const organization = new Organization(
        existingUser.tenantId,
        UUID.v7(),
        application.organizationName,
        "AUTHORITY", // Default organization type
        "ACTIVE",
        {
          description: application.organizationDescription,
          contactEmail: existingUser.email
        }
      );
      await organization.create();

      // Update user's role to org_admin and associate with organization
      existingUser.role = 'org_admin';
      existingUser.organizationId = organization.id;
      await existingUser.update();

      // Update application with result
      const docClient = getDocumentClient();
      await docClient.update({
        TableName: this.TABLE_NAME,
        Key: {
          PK: application.PK,
          SK: application.SK,
        },
        UpdateExpression: "SET resultOrganizationId = :orgId, resultUserType = :userType",
        ExpressionAttributeValues: {
          ":orgId": organization.id,
          ":userType": "org_admin",
        },
      }).promise();

      logger.info("Org admin approval processed successfully", {
        applicationId: application.applicationId,
        organizationId: organization.id,
        userId: existingUser.id
      });

    } catch (error) {
      logger.error("Failed to create Clerk organization", {
        error,
        applicationId: application.applicationId,
        organizationName: application.organizationName
      });
      throw new Error(`Failed to create organization: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process agent application approval
   * Updates existing user's role and associates with organization
   */
  private static async processAgentApproval(
    application: Application,
    approverUserId: string
  ): Promise<void> {
    logger.info("Processing agent approval", {
      applicationId: application.applicationId,
      targetOrganizationId: application.targetOrganizationId,
      applicantUserId: application.applicantUserId
    });

    if (!application.targetOrganizationId) {
      throw new Error("Target organization ID is required for agent applications");
    }

    if (!application.applicantUserId) {
      throw new Error("Applicant user ID is required for agent applications");
    }

    try {
      // Get the existing user from User entity system
      const existingUser = await User.getById(application.applicantUserId!);
      if (!existingUser) {
        throw new Error(`User not found: ${application.applicantUserId}`);
      }

      // Update user's role to agent and associate with organization
      existingUser.role = 'agent';
      existingUser.organizationId = application.targetOrganizationId;
      await existingUser.update();

      // Update application with result
      const docClient = getDocumentClient();
      await docClient.update({
        TableName: this.TABLE_NAME,
        Key: {
          PK: application.PK,
          SK: application.SK,
        },
        UpdateExpression: "SET resultOrganizationId = :orgId, resultUserType = :userType",
        ExpressionAttributeValues: {
          ":orgId": application.targetOrganizationId,
          ":userType": "agent",
        },
      }).promise();

      logger.info("Agent approval processed successfully", {
        applicationId: application.applicationId,
        organizationId: application.targetOrganizationId,
        userId: existingUser.id
      });

    } catch (error) {
      logger.error("Failed to add user to Clerk organization", {
        error,
        applicationId: application.applicationId,
        targetOrganizationId: application.targetOrganizationId,
        applicantClerkUserId: application.applicantClerkUserId
      });
      throw new Error(`Failed to add user to organization: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get application by ID
   * @param applicationId - Application ID to lookup
   * @returns Application if found, null otherwise
   */
  static async getApplicationById(applicationId: string): Promise<Application | null> {
    try {
      const docClient = getDocumentClient();
      const result = await docClient.get({
        TableName: this.TABLE_NAME,
        Key: {
          PK: `APP#${applicationId}`,
          SK: "METADATA",
        },
      }).promise();

      return result.Item as Application || null;
    } catch (error) {
      logger.error("Error getting application by ID", { error, applicationId });
      throw new Error("Failed to get application by ID");
    }
  }

  /**
   * List applications by type and status
   * @param applicationType - Type of applications to list
   * @param status - Status to filter by (optional)
   * @param limit - Maximum number of applications to return
   * @returns Array of applications
   */
  static async listApplications(
    applicationType?: ApplicationType,
    status?: ApplicationStatus,
    limit: number = 50
  ): Promise<Application[]> {
    try {
      const docClient = getDocumentClient();

      if (applicationType) {
        // Query by application type
        let keyConditionExpression = "ApplicationTypeLookupPK = :appType";
        const expressionAttributeValues: any = {
          ":appType": `APP_TYPE#${applicationType}`,
        };

        if (status) {
          keyConditionExpression += " AND begins_with(ApplicationTypeLookupSK, :statusPrefix)";
          expressionAttributeValues[":statusPrefix"] = `STATUS#${status}#`;
        }

        const result = await docClient.query({
          TableName: this.TABLE_NAME,
          IndexName: "ApplicationTypeLookupIndex",
          KeyConditionExpression: keyConditionExpression,
          ExpressionAttributeValues: expressionAttributeValues,
          Limit: limit,
          ScanIndexForward: false, // Most recent first
        }).promise();

        return result.Items as Application[] || [];
      } else {
        // Scan all applications (less efficient, use sparingly)
        const result = await docClient.scan({
          TableName: this.TABLE_NAME,
          FilterExpression: "begins_with(PK, :appPrefix)",
          ExpressionAttributeValues: {
            ":appPrefix": "APP#",
          },
          Limit: limit,
        }).promise();

        return result.Items as Application[] || [];
      }
    } catch (error) {
      logger.error("Error listing applications", { error, applicationType, status });
      throw new Error("Failed to list applications");
    }
  }

  /**
   * Validate that user doesn't have a pending application
   * @param email - Email to check
   */
  private static async validateNoPendingApplication(email: string): Promise<void> {
    const docClient = getDocumentClient();

    const result = await docClient.query({
      TableName: this.TABLE_NAME,
      IndexName: "ApplicationEmailLookupIndex",
      KeyConditionExpression: "ApplicationEmailLookupPK = :email AND ApplicationEmailLookupSK = :status",
      ExpressionAttributeValues: {
        ":email": `EMAIL#${email.toLowerCase()}`,
        ":status": "STATUS#PENDING_APPROVAL",
      },
      Limit: 1,
    }).promise();

    if (result.Items && result.Items.length > 0) {
      throw new Error("User already has a pending application");
    }
  }

  /**
   * Validate that user doesn't have a pending application by user ID
   * @param userId - User ID to check
   */
  private static async validateNoPendingApplicationByUserId(userId: string): Promise<void> {
    const docClient = getDocumentClient();

    const result = await docClient.query({
      TableName: this.TABLE_NAME,
      IndexName: "ApplicationUserLookupIndex",
      KeyConditionExpression: "ApplicationUserLookupPK = :userId AND ApplicationUserLookupSK = :status",
      ExpressionAttributeValues: {
        ":userId": `USER#${userId}`,
        ":status": "STATUS#PENDING_APPROVAL",
      },
      Limit: 1,
    }).promise();

    if (result.Items && result.Items.length > 0) {
      throw new Error(`User ${userId} already has a pending application`);
    }
  }

  /**
   * Get application by user ID
   * @param userId - User ID to search for
   * @returns Application if found, null otherwise
   */
  static async getApplicationByUserId(userId: string): Promise<Application | null> {
    const docClient = getDocumentClient();

    try {
      const result = await docClient.query({
        TableName: this.TABLE_NAME,
        IndexName: "ApplicationUserLookupIndex",
        KeyConditionExpression: "ApplicationUserLookupPK = :userId",
        ExpressionAttributeValues: {
          ":userId": `USER#${userId}`,
        },
        Limit: 1,
      }).promise();

      if (result.Items && result.Items.length > 0) {
        return result.Items[0] as Application;
      }

      return null;
    } catch (error) {
      logger.error("Error getting application by user ID", { error, userId });
      throw error;
    }
  }

  /**
   * Validate organization name availability (basic check)
   * @param organizationName - Organization name to check
   */
  private static async validateOrganizationNameAvailability(organizationName: string): Promise<void> {
    // For now, just check if there's already an approved org_admin application with this name
    const docClient = getDocumentClient();

    const result = await docClient.query({
      TableName: this.TABLE_NAME,
      IndexName: "ApplicationTypeLookupIndex",
      KeyConditionExpression: "ApplicationTypeLookupPK = :appType AND begins_with(ApplicationTypeLookupSK, :statusPrefix)",
      FilterExpression: "organizationName = :orgName",
      ExpressionAttributeValues: {
        ":appType": "APP_TYPE#org_admin",
        ":statusPrefix": "STATUS#APPROVED#",
        ":orgName": organizationName,
      },
      Limit: 1,
    }).promise();

    if (result.Items && result.Items.length > 0) {
      throw new Error(`Organization name "${organizationName}" is already taken`);
    }
  }

  /**
   * Validate affiliate username and return the user
   * @param username - Username to validate
   * @returns User entity if valid org_admin
   */
  private static async validateAffiliateUsername(username: string): Promise<{ organizationId: string }> {
    // For now, we'll use the old UserService to get the user data
    // and return just the organizationId that we need
    const user = await UserService.getUserByUsername(username);

    if (!user) {
      throw new Error(`Affiliate with username "${username}" not found`);
    }

    if (user.userType !== "org_admin") {
      throw new Error(`User with username "${username}" is not an organization admin`);
    }

    if (!user.isActive) {
      throw new Error(`User with username "${username}" is not active`);
    }

    if (!user.organizationId) {
      throw new Error(`Organization admin with username "${username}" does not have an organization`);
    }

    return { organizationId: user.organizationId };
  }

  /**
   * Generate organization slug from name
   * @param organizationName - Organization name
   * @returns URL-friendly slug
   */
  private static generateOrganizationSlug(organizationName: string): string {
    return organizationName
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')
      .substring(0, 50); // Clerk has slug length limits
  }
}