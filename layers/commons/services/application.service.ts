/**
 * Application Service for Financial Payment System
 * 
 * Handles both org_admin and agent applications with proper validation,
 * workflow management, and approval processes.
 */

import { DynamoDB } from "aws-sdk";
import { Logger } from "@aws-lambda-powertools/logger";
import { getDocumentClient } from "../data/client";
import { 
  Application, 
  ApplicationType, 
  ApplicationStatus,
  User,
  UserType 
} from "../types";
import { UUID } from "../utils/uuid";
import { UserService } from "./user.service";
import { ClerkService } from "./clerk-service";

const logger = new Logger({
  serviceName: "application-service",
  logLevel: process.env.LOG_LEVEL as any || "INFO",
});

/**
 * Request interface for submitting org_admin application
 */
export interface SubmitOrgAdminApplicationRequest {
  organizationName: string;
  organizationDescription?: string;
  notes?: string;
}

/**
 * Request interface for submitting agent application
 */
export interface SubmitAgentApplicationRequest {
  affiliateUsername: string; // Username of the org admin they want to join
  notes?: string;
}

/**
 * Request interface for approving applications
 */
export interface ApproveApplicationRequest {
  applicationId: string;
  approved: boolean;
  adminNotes?: string;
  rejectionReason?: string;
}

/**
 * ApplicationService class for managing applications in the financial payment system
 */
export class ApplicationService {
  private static readonly TABLE_NAME = process.env.TABLE_NAME || "clkk-payment-system";

  /**
   * Submit an org_admin application
   * @param clerkUserId - Clerk user ID of the applicant
   * @param username - Username of the applicant
   * @param email - Email of the applicant
   * @param request - Application request data
   * @returns Created application
   */
  static async submitOrgAdminApplication(
    clerkUserId: string,
    username: string,
    email: string,
    request: SubmitOrgAdminApplicationRequest
  ): Promise<Application> {
    logger.info("Submitting org_admin application", { 
      clerkUserId, 
      username, 
      organizationName: request.organizationName 
    });

    // Validate that user doesn't already have a pending application
    await this.validateNoPendingApplication(email);

    // Validate organization name uniqueness (basic check)
    await this.validateOrganizationNameAvailability(request.organizationName);

    // Create application
    const now = new Date().toISOString();
    const applicationId = UUID.v7();
    
    const application: Application = {
      PK: `APP#${applicationId}`,
      SK: "METADATA",
      ApplicationTypeLookupPK: `APP_TYPE#org_admin`,
      ApplicationTypeLookupSK: `STATUS#PENDING_APPROVAL#CREATED#${now}`,
      ApplicationEmailLookupPK: `EMAIL#${email.toLowerCase()}`,
      ApplicationEmailLookupSK: `STATUS#PENDING_APPROVAL`,
      
      applicationId,
      applicationType: "org_admin",
      status: "PENDING_APPROVAL",
      applicantClerkUserId: clerkUserId,
      applicantUsername: username,
      applicantEmail: email,
      organizationName: request.organizationName,
      organizationDescription: request.organizationDescription,
      notes: request.notes,
      createdAt: now,
      updatedAt: now,
    };

    // Save to DynamoDB
    const docClient = getDocumentClient();
    await docClient.put({
      TableName: this.TABLE_NAME,
      Item: application,
    }).promise();

    logger.info("Org admin application submitted successfully", { 
      applicationId, 
      organizationName: request.organizationName 
    });

    return application;
  }

  /**
   * Submit an agent application
   * @param clerkUserId - Clerk user ID of the applicant
   * @param username - Username of the applicant
   * @param email - Email of the applicant
   * @param request - Application request data
   * @returns Created application
   */
  static async submitAgentApplication(
    clerkUserId: string,
    username: string,
    email: string,
    request: SubmitAgentApplicationRequest
  ): Promise<Application> {
    logger.info("Submitting agent application", { 
      clerkUserId, 
      username, 
      affiliateUsername: request.affiliateUsername 
    });

    // Validate that user doesn't already have a pending application
    await this.validateNoPendingApplication(email);

    // Validate that the affiliate username exists and is an org_admin
    const affiliateUser = await this.validateAffiliateUsername(request.affiliateUsername);

    // Create application
    const now = new Date().toISOString();
    const applicationId = UUID.v7();
    
    const application: Application = {
      PK: `APP#${applicationId}`,
      SK: "METADATA",
      ApplicationTypeLookupPK: `APP_TYPE#agent`,
      ApplicationTypeLookupSK: `STATUS#PENDING_APPROVAL#CREATED#${now}`,
      ApplicationEmailLookupPK: `EMAIL#${email.toLowerCase()}`,
      ApplicationEmailLookupSK: `STATUS#PENDING_APPROVAL`,
      
      applicationId,
      applicationType: "agent",
      status: "PENDING_APPROVAL",
      applicantClerkUserId: clerkUserId,
      applicantUsername: username,
      applicantEmail: email,
      affiliateUsername: request.affiliateUsername,
      targetOrganizationId: affiliateUser.organizationId,
      notes: request.notes,
      createdAt: now,
      updatedAt: now,
    };

    // Save to DynamoDB
    const docClient = getDocumentClient();
    await docClient.put({
      TableName: this.TABLE_NAME,
      Item: application,
    }).promise();

    logger.info("Agent application submitted successfully", { 
      applicationId, 
      affiliateUsername: request.affiliateUsername 
    });

    return application;
  }

  /**
   * Approve or reject an application
   * @param request - Approval request data
   * @param approverUserId - User ID of the approver
   * @returns Updated application
   */
  static async approveApplication(
    request: ApproveApplicationRequest,
    approverUserId: string
  ): Promise<Application> {
    logger.info("Processing application approval", { 
      applicationId: request.applicationId, 
      approved: request.approved,
      approverUserId 
    });

    // Get the application
    const application = await this.getApplicationById(request.applicationId);
    if (!application) {
      throw new Error(`Application not found: ${request.applicationId}`);
    }

    if (application.status !== "PENDING_APPROVAL") {
      throw new Error(`Application is not pending approval. Current status: ${application.status}`);
    }

    const now = new Date().toISOString();
    const newStatus: ApplicationStatus = request.approved ? "APPROVED" : "REJECTED";

    if (request.approved) {
      // Process approval based on application type
      if (application.applicationType === "org_admin") {
        await this.processOrgAdminApproval(application, approverUserId);
      } else if (application.applicationType === "agent") {
        await this.processAgentApproval(application, approverUserId);
      }
    }

    // Update application status
    const docClient = getDocumentClient();
    const updateResult = await docClient.update({
      TableName: this.TABLE_NAME,
      Key: {
        PK: application.PK,
        SK: application.SK,
      },
      UpdateExpression: "SET #status = :status, adminNotes = :adminNotes, approvedBy = :approvedBy, approvedAt = :approvedAt, updatedAt = :updatedAt, GSI1SK = :gsi1sk, GSI2SK = :gsi2sk" +
        (request.rejectionReason ? ", rejectionReason = :rejectionReason" : ""),
      ExpressionAttributeNames: {
        "#status": "status",
      },
      ExpressionAttributeValues: {
        ":status": newStatus,
        ":adminNotes": request.adminNotes || "",
        ":approvedBy": approverUserId,
        ":approvedAt": now,
        ":updatedAt": now,
        ":gsi1sk": `STATUS#${newStatus}#CREATED#${application.createdAt}`,
        ":gsi2sk": `STATUS#${newStatus}`,
        ...(request.rejectionReason && { ":rejectionReason": request.rejectionReason }),
      },
      ReturnValues: "ALL_NEW",
    }).promise();

    const updatedApplication = updateResult.Attributes as Application;

    logger.info("Application processed successfully", { 
      applicationId: request.applicationId, 
      status: newStatus 
    });

    return updatedApplication;
  }

  /**
   * Process org_admin application approval
   * Creates Clerk organization and updates user type
   * Organization will be in PENDING status until webhook confirms creation
   */
  private static async processOrgAdminApproval(
    application: Application,
    approverUserId: string
  ): Promise<void> {
    logger.info("Processing org admin approval", { 
      applicationId: application.applicationId,
      organizationName: application.organizationName 
    });

    if (!application.organizationName) {
      throw new Error("Organization name is required for org admin applications");
    }

    try {
      // Create organization in Clerk
      const clerkApiKey = process.env.CLERK_API_KEY;
      if (!clerkApiKey) {
        throw new Error("CLERK_API_KEY environment variable is required");
      }

      const clerkService = new ClerkService(clerkApiKey);
      const clerkOrganization = await clerkService.createOrganization({
        name: application.organizationName,
        slug: this.generateOrganizationSlug(application.organizationName),
        created_by: application.applicantClerkUserId,
        public_metadata: {
          applicationId: application.applicationId,
          description: application.organizationDescription,
          createdViaApplication: true,
        },
      });

      logger.info("Clerk organization created", { 
        clerkOrganizationId: clerkOrganization.id,
        applicationId: application.applicationId 
      });

      // Update application with Clerk organization ID
      // Organization will be created in our DB via webhook with PENDING status
      const docClient = getDocumentClient();
      await docClient.update({
        TableName: this.TABLE_NAME,
        Key: {
          PK: application.PK,
          SK: application.SK,
        },
        UpdateExpression: "SET resultClerkOrganizationId = :clerkOrgId, resultUserType = :userType",
        ExpressionAttributeValues: {
          ":clerkOrgId": clerkOrganization.id,
          ":userType": "org_admin",
        },
      }).promise();

      logger.info("Org admin approval processed - awaiting webhook", { 
        applicationId: application.applicationId,
        clerkOrganizationId: clerkOrganization.id,
        username: application.applicantUsername 
      });

    } catch (error) {
      logger.error("Failed to create Clerk organization", { 
        error, 
        applicationId: application.applicationId,
        organizationName: application.organizationName 
      });
      throw new Error(`Failed to create organization: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process agent application approval
   * Adds user to Clerk organization as member
   * User role will be updated via webhook when membership is confirmed
   */
  private static async processAgentApproval(
    application: Application,
    approverUserId: string
  ): Promise<void> {
    logger.info("Processing agent approval", { 
      applicationId: application.applicationId,
      targetOrganizationId: application.targetOrganizationId 
    });

    if (!application.targetOrganizationId) {
      throw new Error("Target organization ID is required for agent applications");
    }

    try {
      // Add user to Clerk organization
      const clerkApiKey = process.env.CLERK_API_KEY;
      if (!clerkApiKey) {
        throw new Error("CLERK_API_KEY environment variable is required");
      }

      const clerkService = new ClerkService(clerkApiKey);
      const membership = await clerkService.createOrganizationMembership(
        application.targetOrganizationId,
        {
          user_id: application.applicantClerkUserId,
          role: "basic_member", // Default role for agents
        }
      );

      logger.info("Clerk organization membership created", { 
        membershipId: membership.id,
        organizationId: application.targetOrganizationId,
        userId: application.applicantClerkUserId 
      });

      // Update application with result
      // User role will be updated via webhook when membership is confirmed
      const docClient = getDocumentClient();
      await docClient.update({
        TableName: this.TABLE_NAME,
        Key: {
          PK: application.PK,
          SK: application.SK,
        },
        UpdateExpression: "SET resultClerkOrganizationId = :orgId, resultUserType = :userType",
        ExpressionAttributeValues: {
          ":orgId": application.targetOrganizationId,
          ":userType": "agent",
        },
      }).promise();

      logger.info("Agent approval processed - awaiting webhook", { 
        applicationId: application.applicationId,
        organizationId: application.targetOrganizationId,
        username: application.applicantUsername 
      });

    } catch (error) {
      logger.error("Failed to add user to Clerk organization", { 
        error, 
        applicationId: application.applicationId,
        targetOrganizationId: application.targetOrganizationId,
        applicantClerkUserId: application.applicantClerkUserId 
      });
      throw new Error(`Failed to add user to organization: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get application by ID
   * @param applicationId - Application ID to lookup
   * @returns Application if found, null otherwise
   */
  static async getApplicationById(applicationId: string): Promise<Application | null> {
    try {
      const docClient = getDocumentClient();
      const result = await docClient.get({
        TableName: this.TABLE_NAME,
        Key: {
          PK: `APP#${applicationId}`,
          SK: "METADATA",
        },
      }).promise();

      return result.Item as Application || null;
    } catch (error) {
      logger.error("Error getting application by ID", { error, applicationId });
      throw new Error("Failed to get application by ID");
    }
  }

  /**
   * List applications by type and status
   * @param applicationType - Type of applications to list
   * @param status - Status to filter by (optional)
   * @param limit - Maximum number of applications to return
   * @returns Array of applications
   */
  static async listApplications(
    applicationType?: ApplicationType,
    status?: ApplicationStatus,
    limit: number = 50
  ): Promise<Application[]> {
    try {
      const docClient = getDocumentClient();
      
      if (applicationType) {
        // Query by application type
        let keyConditionExpression = "ApplicationTypeLookupPK = :appType";
        const expressionAttributeValues: any = {
          ":appType": `APP_TYPE#${applicationType}`,
        };

        if (status) {
          keyConditionExpression += " AND begins_with(ApplicationTypeLookupSK, :statusPrefix)";
          expressionAttributeValues[":statusPrefix"] = `STATUS#${status}#`;
        }

        const result = await docClient.query({
          TableName: this.TABLE_NAME,
          IndexName: "ApplicationTypeLookupIndex",
          KeyConditionExpression: keyConditionExpression,
          ExpressionAttributeValues: expressionAttributeValues,
          Limit: limit,
          ScanIndexForward: false, // Most recent first
        }).promise();

        return result.Items as Application[] || [];
      } else {
        // Scan all applications (less efficient, use sparingly)
        const result = await docClient.scan({
          TableName: this.TABLE_NAME,
          FilterExpression: "begins_with(PK, :appPrefix)",
          ExpressionAttributeValues: {
            ":appPrefix": "APP#",
          },
          Limit: limit,
        }).promise();

        return result.Items as Application[] || [];
      }
    } catch (error) {
      logger.error("Error listing applications", { error, applicationType, status });
      throw new Error("Failed to list applications");
    }
  }

  /**
   * Validate that user doesn't have a pending application
   * @param email - Email to check
   */
  private static async validateNoPendingApplication(email: string): Promise<void> {
    const docClient = getDocumentClient();
    
    const result = await docClient.query({
      TableName: this.TABLE_NAME,
      IndexName: "ApplicationEmailLookupIndex",
      KeyConditionExpression: "ApplicationEmailLookupPK = :email AND ApplicationEmailLookupSK = :status",
      ExpressionAttributeValues: {
        ":email": `EMAIL#${email.toLowerCase()}`,
        ":status": "STATUS#PENDING_APPROVAL",
      },
      Limit: 1,
    }).promise();

    if (result.Items && result.Items.length > 0) {
      throw new Error("User already has a pending application");
    }
  }

  /**
   * Validate organization name availability (basic check)
   * @param organizationName - Organization name to check
   */
  private static async validateOrganizationNameAvailability(organizationName: string): Promise<void> {
    // For now, just check if there's already an approved org_admin application with this name
    const docClient = getDocumentClient();
    
    const result = await docClient.query({
      TableName: this.TABLE_NAME,
      IndexName: "ApplicationTypeLookupIndex",
      KeyConditionExpression: "ApplicationTypeLookupPK = :appType AND begins_with(ApplicationTypeLookupSK, :statusPrefix)",
      FilterExpression: "organizationName = :orgName",
      ExpressionAttributeValues: {
        ":appType": "APP_TYPE#org_admin",
        ":statusPrefix": "STATUS#APPROVED#",
        ":orgName": organizationName,
      },
      Limit: 1,
    }).promise();

    if (result.Items && result.Items.length > 0) {
      throw new Error(`Organization name "${organizationName}" is already taken`);
    }
  }

  /**
   * Validate affiliate username and return the user
   * @param username - Username to validate
   * @returns User if valid org_admin
   */
  private static async validateAffiliateUsername(username: string): Promise<User> {
    const user = await UserService.getUserByUsername(username);
    
    if (!user) {
      throw new Error(`Affiliate with username "${username}" not found`);
    }

    if (user.userType !== "org_admin") {
      throw new Error(`User with username "${username}" is not an organization admin`);
    }

    if (!user.isActive) {
      throw new Error(`User with username "${username}" is not active`);
    }

    if (!user.organizationId) {
      throw new Error(`Organization admin with username "${username}" does not have an organization`);
    }

    return user;
  }

  /**
   * Generate organization slug from name
   * @param organizationName - Organization name
   * @returns URL-friendly slug
   */
  private static generateOrganizationSlug(organizationName: string): string {
    return organizationName
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')
      .substring(0, 50); // Clerk has slug length limits
  }
} 