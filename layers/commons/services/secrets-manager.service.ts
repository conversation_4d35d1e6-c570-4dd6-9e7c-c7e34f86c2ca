import { logger } from '../utils/logger';

/**
 * AWS Secrets Manager Service
 * 
 * Provides a reusable service for retrieving secrets from AWS Secrets Manager
 * using the AWS Parameters and Secrets Lambda Extension for optimal performance
 * and cost efficiency.
 * 
 * Best Practices Implemented:
 * - Uses AWS Parameters and Secrets Lambda Extension for caching
 * - Implements proper error handling and logging
 * - Supports both individual secrets and batch retrieval
 * - Includes retry logic for transient failures
 */
export class SecretsManagerService {
  private static instance: SecretsManagerService;
  private readonly extensionPort: string;
  private readonly sessionToken: string;
  private readonly cache: Map<string, { value: any; timestamp: number; ttl: number }>;
  private readonly defaultTtl: number = 300000; // 5 minutes in milliseconds

  private constructor() {
    this.extensionPort = process.env.PARAMETERS_SECRETS_EXTENSION_HTTP_PORT || '2773';
    this.sessionToken = process.env.AWS_SESSION_TOKEN || '';
    this.cache = new Map();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): SecretsManagerService {
    if (!SecretsManagerService.instance) {
      SecretsManagerService.instance = new SecretsManagerService();
    }
    return SecretsManagerService.instance;
  }

  /**
   * Retrieve a secret from AWS Secrets Manager
   * @param secretId - The ARN or name of the secret
   * @param versionStage - Optional version stage (default: AWSCURRENT)
   * @param useCache - Whether to use local cache (default: true)
   * @returns Promise<any> - The secret value
   */
  public async getSecret(
    secretId: string, 
    versionStage: string = 'AWSCURRENT',
    useCache: boolean = true
  ): Promise<any> {
    const cacheKey = `${secretId}:${versionStage}`;
    
    // Check cache first
    if (useCache && this.isCacheValid(cacheKey)) {
      logger.debug('Returning cached secret', { secretId });
      return this.cache.get(cacheKey)!.value;
    }

    try {
      logger.debug('Retrieving secret from Secrets Manager', { secretId });

      const url = `http://localhost:${this.extensionPort}/secretsmanager/get?secretId=${encodeURIComponent(secretId)}&versionStage=${versionStage}`;
      
      const response = await this.makeRequest(url);
      
      if (!response.ok) {
        throw new Error(`Failed to retrieve secret: ${response.status} ${response.statusText}`);
      }

      const secretData = await response.json();
      let secretValue;

      // Parse the secret string if it's JSON
      try {
        secretValue = JSON.parse(secretData.SecretString);
      } catch {
        // If not JSON, return as string
        secretValue = secretData.SecretString;
      }

      // Cache the result
      if (useCache) {
        this.cache.set(cacheKey, {
          value: secretValue,
          timestamp: Date.now(),
          ttl: this.defaultTtl
        });
      }

      logger.debug('Successfully retrieved secret', { secretId });
      return secretValue;

    } catch (error) {
      logger.error('Failed to retrieve secret', { 
        secretId, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw new Error(`Failed to retrieve secret ${secretId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Retrieve multiple secrets in batch
   * @param secretIds - Array of secret IDs to retrieve
   * @param useCache - Whether to use local cache (default: true)
   * @returns Promise<Record<string, any>> - Object with secret IDs as keys and values
   */
  public async getSecrets(secretIds: string[], useCache: boolean = true): Promise<Record<string, any>> {
    const results: Record<string, any> = {};
    
    // Use Promise.allSettled to handle partial failures gracefully
    const promises = secretIds.map(async (secretId) => {
      try {
        const value = await this.getSecret(secretId, 'AWSCURRENT', useCache);
        return { secretId, value, success: true };
      } catch (error) {
        logger.error('Failed to retrieve secret in batch', { secretId, error });
        return { secretId, error, success: false };
      }
    });

    const settledResults = await Promise.allSettled(promises);
    
    settledResults.forEach((result) => {
      if (result.status === 'fulfilled' && result.value.success) {
        results[result.value.secretId] = result.value.value;
      }
    });

    return results;
  }

  /**
   * Get a specific key from a JSON secret
   * @param secretId - The ARN or name of the secret
   * @param key - The key to extract from the JSON secret
   * @param useCache - Whether to use local cache (default: true)
   * @returns Promise<string> - The specific value
   */
  public async getSecretValue(secretId: string, key: string, useCache: boolean = true): Promise<string> {
    const secret = await this.getSecret(secretId, 'AWSCURRENT', useCache);
    
    if (typeof secret === 'object' && secret !== null && key in secret) {
      return secret[key];
    }
    
    if (typeof secret === 'string') {
      return secret;
    }
    
    throw new Error(`Key '${key}' not found in secret '${secretId}'`);
  }

  /**
   * Clear the local cache
   */
  public clearCache(): void {
    this.cache.clear();
    logger.debug('Secrets cache cleared');
  }

  /**
   * Clear expired cache entries
   */
  public clearExpiredCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];
    
    this.cache.forEach((value, key) => {
      if (now - value.timestamp > value.ttl) {
        expiredKeys.push(key);
      }
    });
    
    expiredKeys.forEach(key => this.cache.delete(key));
    
    if (expiredKeys.length > 0) {
      logger.debug('Cleared expired cache entries', { count: expiredKeys.length });
    }
  }

  /**
   * Check if cache entry is valid
   * @private
   */
  private isCacheValid(cacheKey: string): boolean {
    const cached = this.cache.get(cacheKey);
    if (!cached) return false;
    
    const isValid = Date.now() - cached.timestamp < cached.ttl;
    if (!isValid) {
      this.cache.delete(cacheKey);
    }
    
    return isValid;
  }

  /**
   * Make HTTP request with retry logic
   * @private
   */
  private async makeRequest(url: string, retries: number = 3): Promise<Response> {
    const headers = {
      'X-Aws-Parameters-Secrets-Token': this.sessionToken
    };

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        const response = await fetch(url, { headers });
        
        if (response.ok) {
          return response;
        }
        
        // Don't retry on 4xx errors (client errors)
        if (response.status >= 400 && response.status < 500) {
          throw new Error(`Client error: ${response.status} ${response.statusText}`);
        }
        
        // Retry on 5xx errors (server errors)
        if (attempt === retries) {
          throw new Error(`Server error after ${retries} attempts: ${response.status} ${response.statusText}`);
        }
        
        logger.warn(`Request failed, retrying (${attempt}/${retries})`, { 
          status: response.status, 
          statusText: response.statusText 
        });
        
        // Exponential backoff
        await this.sleep(Math.pow(2, attempt - 1) * 1000);
        
      } catch (error) {
        if (attempt === retries) {
          throw error;
        }
        
        logger.warn(`Request failed, retrying (${attempt}/${retries})`, { error });
        await this.sleep(Math.pow(2, attempt - 1) * 1000);
      }
    }
    
    throw new Error('Max retries exceeded');
  }

  /**
   * Sleep utility for retry delays
   * @private
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export singleton instance for convenience
export const secretsManager = SecretsManagerService.getInstance(); 