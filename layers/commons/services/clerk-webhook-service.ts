import * as crypto from "crypto";
import { SNSClient, PublishCommand } from "@aws-sdk/client-sns";
import {
  ClerkWebhookEvent,
  isOrganizationEvent,
  isUserEvent,
  isOrganizationMembershipEvent,
} from "../types/clerk-webhook-types";
import { Logger } from "@aws-lambda-powertools/logger";

/**
 * Configuration for the Clerk Webhook service
 */
export interface ClerkWebhookServiceConfig {
  /**
   * The secret used to verify Clerk webhook signatures
   */
  webhookSecret: string;

  /**
   * SNS Topic ARN for organization events
   */
  organizationTopicArn?: string;

  /**
   * SNS Topic ARN for user events
   */
  userTopicArn?: string;

  /**
   * Optional logger instance
   */
  logger?: any;

  /**
   * Optional AWS region for SNS client
   */
  region?: string;
}

/**
 * Service class to handle Clerk webhook events
 */
export class ClerkWebhookService {
  private webhookSecret: string;
  private snsClient: SNSClient;
  private organizationTopicArn?: string;
  private userTopicArn?: string;
  private logger: Logger;

  /**
   * Creates a new ClerkWebhookService
   *
   * @param config - Service configuration
   */
  constructor(config: ClerkWebhookServiceConfig) {
    this.webhookSecret = config.webhookSecret;
    this.organizationTopicArn = config.organizationTopicArn;
    this.userTopicArn = config.userTopicArn;
    this.logger =
      config.logger || new Logger({ serviceName: "clerk-webhook-service" });

    this.snsClient = new SNSClient({
      region: config.region || process.env.AWS_REGION || "us-east-1",
    });
  }

  /**
   * Verifies the Clerk webhook signature using Svix-compatible verification
   *
   * @param body - The raw request body
   * @param svix_id - The Svix ID header
   * @param svix_timestamp - The Svix timestamp header
   * @param svix_signature - The Svix signature header
   * @returns Whether the signature is valid
   */
  public verifyWebhookSignature(
    body: string,
    svix_id: string,
    svix_timestamp: string,
    svix_signature: string
  ): boolean {
    try {
      this.logger.info("Got to here")
      if (!this.webhookSecret) {
        this.logger.error("Webhook secret is not configured");
        return false;
      }

      //log first 10 characters of the webhook secret
      this.logger.info("Webhook secret", {
        secret: this.webhookSecret
      });

      // Validate required headers
      if (!svix_id || !svix_timestamp || !svix_signature) {
        this.logger.error("Missing required Svix headers", {
          hasId: !!svix_id,
          hasTimestamp: !!svix_timestamp,
          hasSignature: !!svix_signature
        });
        return false;
      }

      // Process webhook secret - Clerk/Svix secrets start with 'whsec_' and need to be base64 decoded
      let secretKey: Buffer;
      if (this.webhookSecret.startsWith('whsec_')) {
        // Remove the 'whsec_' prefix and base64 decode
        const base64Secret = this.webhookSecret.substring(6);
        try {
          secretKey = Buffer.from(base64Secret, 'base64');
          this.logger.debug("Processed Svix webhook secret format", {
            originalLength: this.webhookSecret.length,
            decodedLength: secretKey.length
          });
        } catch (decodeError) {
          this.logger.error("Failed to decode base64 webhook secret", { error: decodeError });
          return false;
        }
      } else {
        // Use the secret as-is (UTF-8 encoded)
        secretKey = Buffer.from(this.webhookSecret, "utf8");
        this.logger.debug("Using webhook secret as UTF-8");
      }

      // Create the signed payload exactly as Svix does
      const signedPayload = `${svix_id}.${svix_timestamp}.${body}`;
      
      this.logger.debug("Signature verification details", {
        payloadLength: signedPayload.length,
        timestampLength: svix_timestamp.length,
        bodyLength: body.length,
        svixId: svix_id
      });

      // Parse signatures from the header
      const signatures = svix_signature.split(' ');
      
      this.logger.debug("Processing signatures", {
        signatureCount: signatures.length,
        rawSignature: svix_signature
      });

      for (const versionedSignature of signatures) {
        this.logger.info("Processing signature", { versionedSignature });
        
        const parts = versionedSignature.split(',');
        
        if (parts.length !== 2) {
          this.logger.debug("Invalid signature format", { signature: versionedSignature });
          continue;
        }

        const [version, signature] = parts;
        
        this.logger.info("Signature parts", { version, signature });

        if (version !== 'v1') {
          this.logger.debug("Unsupported signature version", { version });
          continue;
        }

        // Validate signature format - Svix sends base64-encoded signatures, not hex
        if (!signature) {
          this.logger.debug("Empty signature", { signature });
          continue;
        }

        try {
          // Create HMAC-SHA256 signature
          const hmac = crypto.createHmac('sha256', secretKey);
          hmac.update(signedPayload, 'utf8');
          const expectedSignature = hmac.digest('base64'); // Use base64, not hex

          this.logger.info("Signature comparison", {
            expectedSignature,
            receivedSignature: signature,
            signedPayload,
            secretKeyLength: secretKey.length
          });

          // Direct string comparison for base64 signatures
          if (expectedSignature === signature) {
            this.logger.info("Webhook signature verified successfully");
            return true;
          } else {
            this.logger.info("Signature mismatch for version", { 
              version,
              expectedSignature,
              receivedSignature: signature
            });
          }
        } catch (error) {
          this.logger.debug("Error during signature verification", { 
            error: error instanceof Error ? error.message : 'Unknown error',
            version 
          });
          continue;
        }
      }

      this.logger.warn("Webhook signature validation failed - no valid signatures found", {
        svix_id,
        svix_timestamp,
        signatureCount: signatures.length
      });

      return false;
    } catch (error) {
      this.logger.error("Error verifying webhook signature", { 
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      return false;
    }
  }

  /**
   * Check if the webhook service is properly configured
   * @returns boolean indicating if the service is ready
   */
  public isConfigured(): boolean {
    return !!(this.webhookSecret && this.webhookSecret.length > 0);
  }

  /**
   * Process a webhook event
   *
   * @param event - The webhook event to process
   * @returns Whether the event was successfully processed
   */
  public async processWebhookEvent(event: ClerkWebhookEvent): Promise<boolean> {
    try {
      this.logger.info("Processing webhook event", {
        type: event.type,
        timestamp: event.timestamp,
      });

      // Route event to the appropriate SNS topic based on the event type
      if (isOrganizationEvent(event)) {
        if (!this.organizationTopicArn) {
          this.logger.error("Organization topic ARN is not configured");
          return false;
        }

        await this.publishToSNS(this.organizationTopicArn, event);
        return true;
      } else if (isUserEvent(event)) {
        if (!this.userTopicArn) {
          this.logger.error("User topic ARN is not configured");
          return false;
        }

        await this.publishToSNS(this.userTopicArn, event);
        return true;
      } else if (isOrganizationMembershipEvent(event)) {
        // For now, route membership events to the organization topic
        if (!this.organizationTopicArn) {
          this.logger.error("Organization topic ARN is not configured");
          return false;
        }

        await this.publishToSNS(this.organizationTopicArn, event);
        return true;
      } else {
        this.logger.warn("Unsupported event type", { type: event.type });
        return false;
      }
    } catch (error) {
      this.logger.error("Error processing webhook event", { error, event });
      return false;
    }
  }

  /**
   * Publish an event to an SNS topic
   *
   * @param topicArn - The ARN of the SNS topic
   * @param event - The event to publish
   * @returns The SNS publish result
   */
  private async publishToSNS(
    topicArn: string,
    event: ClerkWebhookEvent
  ): Promise<void> {
    try {
      const command = new PublishCommand({
        TopicArn: topicArn,
        Message: JSON.stringify(event),
        MessageAttributes: {
          eventType: {
            DataType: "String",
            StringValue: event.type,
          },
        },
      });

      const result = await this.snsClient.send(command);

      this.logger.debug("Published event to SNS", {
        topicArn,
        messageId: result.MessageId,
        eventType: event.type,
      });
    } catch (error) {
      this.logger.error("Failed to publish to SNS", {
        error,
        topicArn,
        eventType: event.type,
      });
      throw error;
    }
  }
}

/**
 * Factory function to create a ClerkWebhookService instance
 *
 * @param config - Service configuration
 * @returns A new ClerkWebhookService instance
 */
export const createClerkWebhookService = (
  config: ClerkWebhookServiceConfig
): ClerkWebhookService => {
  return new ClerkWebhookService(config);
};

export default ClerkWebhookService;
