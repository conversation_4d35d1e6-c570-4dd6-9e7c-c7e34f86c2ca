import { 
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    K<PERSON>CStatus,
    KYCVerificationLevel,
    StartKYCVerificationRequest,
    StartKYCVerificationResponse,
    ProveV3StartRequest,
    ProveV3StartResponse,
    ProveV3ValidateRequest,
    ProveV3ValidateResponse,
    ProveV3ChallengeRequest,
    ProveV3ChallengeResponse,
    ProveV3CompleteRequest,
    ProveV3CompleteResponse,
    KYCError,
    KYC_CONSTANTS
} from '../../types/kyc-types';
import { logger } from '../../utils/logger';

/**
 * Prove Identity Verification Provider
 * 
 * Implements the IKYCProvider interface for Prove identity verification services.
 * This provider handles the complete Prove verification flow including start, validate,
 * challenge, and complete steps.
 */
export class ProveProvider implements IKYCProvider {
    readonly name: KYCProvider = "PROVE";
    readonly isEnabled: boolean;
    
    private clientId: string;
    private clientSecret: string;
    private environment: string;
    private baseUrl: string;

    constructor(config: {
        clientId: string;
        clientSecret: string;
        environment: "SANDBOX" | "PRODUCTION";
        isEnabled?: boolean;
    }) {
        this.clientId = config.clientId;
        this.clientSecret = config.clientSecret;
        this.environment = config.environment;
        this.isEnabled = config.isEnabled ?? true;
        
        // Set base URL based on environment
        this.baseUrl = config.environment === "PRODUCTION" 
            ? `https://api.${KYC_CONSTANTS.PROVE.ENVIRONMENTS.PRODUCTION}.proveapis.com`
            : `https://api.${KYC_CONSTANTS.PROVE.ENVIRONMENTS.SANDBOX}.proveapis.com`;
    }

    /**
     * Start a new Prove verification
     * @param request - Start verification request
     * @returns Promise<StartKYCVerificationResponse>
     */
    async startVerification(request: StartKYCVerificationRequest): Promise<StartKYCVerificationResponse> {
        try {
            logger.info('Starting Prove verification', {
                provider: this.name,
                verificationLevel: request.verificationLevel
            });

            if (!this.isEnabled) {
                throw new KYCError(
                    'Prove provider is not enabled',
                    'PROVIDER_DISABLED',
                    this.name
                );
            }

            // Validate required fields for Prove
            if (!request.individual.phoneNumber) {
                throw new KYCError(
                    'Phone number is required for Prove verification',
                    'MISSING_PHONE_NUMBER',
                    this.name
                );
            }

            if (!request.individual.ssn) {
                throw new KYCError(
                    'SSN is required for Prove verification',
                    'MISSING_SSN',
                    this.name
                );
            }

            // Prepare Prove API request
            const proveRequest: ProveV3StartRequest = {
                phoneNumber: request.individual.phoneNumber,
                ssn: request.individual.ssn,
                flowType: request.metadata?.deviceFingerprint ? "mobile" : "desktop",
                ipAddress: request.metadata?.ipAddress
            };

            // Call Prove API
            const proveResponse = await this.callProveV3Start(proveRequest);

            if (!proveResponse.success) {
                throw new KYCError(
                    proveResponse.message || 'Prove verification failed to start',
                    'PROVE_START_FAILED',
                    this.name
                );
            }

            return {
                verificationId: proveResponse.correlationId,
                status: "IN_PROGRESS",
                nextStep: proveResponse.nextStep || "validate",
                authToken: proveResponse.authToken,
                correlationId: proveResponse.correlationId
            };

        } catch (error) {
            logger.error('Failed to start Prove verification', error);
            throw error;
        }
    }

    /**
     * Validate a verification step
     * @param verificationId - The verification ID (correlation ID)
     * @param data - Validation data
     * @returns Promise<any>
     */
    async validateStep(verificationId: string, data: Record<string, any>): Promise<any> {
        try {
            logger.info('Validating Prove verification step', {
                verificationId,
                provider: this.name
            });

            const validateRequest: ProveV3ValidateRequest = {
                correlationId: verificationId
            };

            const response = await this.callProveV3Validate(validateRequest);

            return {
                success: response.success,
                isValid: response.isValid,
                nextStep: response.nextStep,
                message: response.message
            };

        } catch (error) {
            logger.error('Failed to validate Prove verification step', error);
            throw error;
        }
    }

    /**
     * Handle challenge step
     * @param verificationId - The verification ID (correlation ID)
     * @param data - Challenge data (DOB, SSN, etc.)
     * @returns Promise<any>
     */
    async challengeStep(verificationId: string, data: Record<string, any>): Promise<any> {
        try {
            logger.info('Processing Prove challenge step', {
                verificationId,
                provider: this.name
            });

            const challengeRequest: ProveV3ChallengeRequest = {
                correlationId: verificationId,
                dob: data.dateOfBirth,
                last4SSN: data.last4SSN
            };

            const response = await this.callProveV3Challenge(challengeRequest);

            return {
                success: response.success,
                challengeQuestions: response.challengeQuestions,
                message: response.message
            };

        } catch (error) {
            logger.error('Failed to process Prove challenge step', error);
            throw error;
        }
    }

    /**
     * Complete the verification
     * @param verificationId - The verification ID (correlation ID)
     * @param data - Completion data
     * @returns Promise<any>
     */
    async completeVerification(verificationId: string, data: Record<string, any>): Promise<any> {
        try {
            logger.info('Completing Prove verification', {
                verificationId,
                provider: this.name
            });

            const completeRequest: ProveV3CompleteRequest = {
                correlationId: verificationId,
                individual: data.individual,
                userId: data.userId
            };

            const response = await this.callProveV3Complete(completeRequest);

            return {
                success: response.success,
                verified: response.verificationResult?.verified || false,
                riskScore: response.verificationResult?.riskScore,
                confidenceScore: response.verificationResult?.confidenceScore,
                flags: response.verificationResult?.flags || [],
                individual: response.individual,
                message: response.message
            };

        } catch (error) {
            logger.error('Failed to complete Prove verification', error);
            throw error;
        }
    }

    /**
     * Get verification status
     * @param verificationId - The verification ID
     * @returns Promise<KYCStatus>
     */
    async getVerificationStatus(verificationId: string): Promise<KYCStatus> {
        try {
            // For Prove, we would need to call their status endpoint
            // This is a placeholder implementation
            logger.info('Getting Prove verification status', {
                verificationId,
                provider: this.name
            });

            return "IN_PROGRESS";
        } catch (error) {
            logger.error('Failed to get Prove verification status', error);
            return "FAILED";
        }
    }

    /**
     * Cancel a verification
     * @param verificationId - The verification ID
     * @returns Promise<boolean>
     */
    async cancelVerification(verificationId: string): Promise<boolean> {
        try {
            logger.info('Cancelling Prove verification', {
                verificationId,
                provider: this.name
            });

            // Prove doesn't have a specific cancel endpoint
            // This would be handled by not completing the flow
            return true;
        } catch (error) {
            logger.error('Failed to cancel Prove verification', error);
            return false;
        }
    }

    /**
     * Call Prove V3 Start API
     * @private
     */
    private async callProveV3Start(request: ProveV3StartRequest): Promise<ProveV3StartResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/v3/start`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${await this.getAccessToken()}`,
                },
                body: JSON.stringify(request)
            });

            if (!response.ok) {
                throw new Error(`Prove API error: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            logger.error('Prove V3 Start API call failed', error);
            throw new KYCError(
                'Failed to call Prove start API',
                'PROVE_API_ERROR',
                this.name,
                undefined,
                true
            );
        }
    }

    /**
     * Call Prove V3 Validate API
     * @private
     */
    private async callProveV3Validate(request: ProveV3ValidateRequest): Promise<ProveV3ValidateResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/v3/validate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${await this.getAccessToken()}`,
                },
                body: JSON.stringify(request)
            });

            if (!response.ok) {
                throw new Error(`Prove API error: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            logger.error('Prove V3 Validate API call failed', error);
            throw new KYCError(
                'Failed to call Prove validate API',
                'PROVE_API_ERROR',
                this.name,
                request.correlationId,
                true
            );
        }
    }

    /**
     * Call Prove V3 Challenge API
     * @private
     */
    private async callProveV3Challenge(request: ProveV3ChallengeRequest): Promise<ProveV3ChallengeResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/v3/challenge`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${await this.getAccessToken()}`,
                },
                body: JSON.stringify(request)
            });

            if (!response.ok) {
                throw new Error(`Prove API error: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            logger.error('Prove V3 Challenge API call failed', error);
            throw new KYCError(
                'Failed to call Prove challenge API',
                'PROVE_API_ERROR',
                this.name,
                request.correlationId,
                true
            );
        }
    }

    /**
     * Call Prove V3 Complete API
     * @private
     */
    private async callProveV3Complete(request: ProveV3CompleteRequest): Promise<ProveV3CompleteResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/v3/complete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${await this.getAccessToken()}`,
                },
                body: JSON.stringify(request)
            });

            if (!response.ok) {
                throw new Error(`Prove API error: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            logger.error('Prove V3 Complete API call failed', error);
            throw new KYCError(
                'Failed to call Prove complete API',
                'PROVE_API_ERROR',
                this.name,
                request.correlationId,
                true
            );
        }
    }

    /**
     * Get access token for Prove API
     * @private
     */
    private async getAccessToken(): Promise<string> {
        try {
            // This would typically involve OAuth2 flow or API key authentication
            // For now, returning the client secret as a placeholder
            // In production, this should implement proper token management
            return this.clientSecret;
        } catch (error) {
            logger.error('Failed to get Prove access token', error);
            throw new KYCError(
                'Failed to authenticate with Prove API',
                'PROVE_AUTH_ERROR',
                this.name
            );
        }
    }
} 