import { ProveProvider } from './prove-provider';
import { secretsManager } from '../secrets-manager.service';
import { SECRET_NAMES, ProveApiCredentialsValue } from '../../constants/secrets';
import { logger } from '../../utils/logger';

/**
 * Factory for creating ProveProvider instances with credentials from Secrets Manager
 */
export class ProveProviderFactory {
  private static cachedProvider: ProveProvider | null = null;
  private static lastInitTime: number = 0;
  private static readonly CACHE_DURATION_MS = 5 * 60 * 1000; // 5 minutes

  /**
   * Create a ProveProvider instance with credentials from Secrets Manager
   * @param useCache - Whether to use cached instance (default: true)
   * @returns Promise<ProveProvider>
   */
  public static async createProvider(useCache: boolean = true): Promise<ProveProvider> {
    const now = Date.now();
    
    // Return cached instance if valid and caching is enabled
    if (useCache && 
        ProveProviderFactory.cachedProvider && 
        now - ProveProviderFactory.lastInitTime < ProveProviderFactory.CACHE_DURATION_MS) {
      logger.debug('Returning cached ProveProvider instance');
      return ProveProviderFactory.cachedProvider;
    }

    try {
      logger.info('Creating ProveProvider instance with credentials from Secrets Manager');
      
      // Retrieve credentials from Secrets Manager
      const credentials = await secretsManager.getSecret(SECRET_NAMES.PROVE_API_CREDENTIALS) as ProveApiCredentialsValue;
      
      if (!credentials.clientId || !credentials.clientSecret) {
        throw new Error('Invalid Prove API credentials: missing clientId or clientSecret');
      }

      // Create new provider instance
      const provider = new ProveProvider({
        clientId: credentials.clientId,
        clientSecret: credentials.clientSecret,
        environment: credentials.environment || 'SANDBOX',
        isEnabled: true // Can be controlled via environment variable if needed
      });

      // Cache the instance if caching is enabled
      if (useCache) {
        ProveProviderFactory.cachedProvider = provider;
        ProveProviderFactory.lastInitTime = now;
      }

      logger.info('ProveProvider instance created successfully', {
        environment: credentials.environment
      });

      return provider;

    } catch (error) {
      logger.error('Failed to create ProveProvider instance', { error });
      throw new Error(`Failed to create ProveProvider: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Clear the cached provider instance
   */
  public static clearCache(): void {
    ProveProviderFactory.cachedProvider = null;
    ProveProviderFactory.lastInitTime = 0;
    logger.debug('ProveProvider cache cleared');
  }

  /**
   * Check if the cached provider is still valid
   */
  public static isCacheValid(): boolean {
    const now = Date.now();
    return ProveProviderFactory.cachedProvider !== null && 
           now - ProveProviderFactory.lastInitTime < ProveProviderFactory.CACHE_DURATION_MS;
  }
} 