import { KYCVerification } from '../data/entities/kyc-verification';
import { User } from '../data/entities/user';
import { 
    KYCProvider, 
    KYCStatus, 
    KYCVerificationLevel, 
    KYCVerificationMetadata,
    KYCError
} from '../types/kyc-types';
import { logger } from '../utils/logger';

/**
 * Core KYC Service for managing verification processes across multiple providers
 * This service provides a unified interface for KYC operations regardless of provider
 */
export class KYCService {
    private tenantId: string;

    constructor(tenantId: string) {
        this.tenantId = tenantId;
    }

    /**
     * Initialize a new KYC verification process
     * @param userId - The user ID to verify
     * @param clerkUserId - The Clerk user ID
     * @param provider - The KYC provider to use
     * @param level - The verification level required
     * @param metadata - Additional metadata for the verification
     * @returns Promise<KYCVerification>
     */
    async initializeVerification(
        userId: string,
        clerkUserId: string,
        provider: KYCProvider,
        level: KYCVerificationLevel,
        metadata: Partial<KYCVerificationMetadata> = {}
    ): Promise<KYCVerification> {
        try {
            logger.info('Initializing KYC verification', {
                userId,
                clerkUserId,
                provider,
                level
            });

            // Check if user already has a pending verification
            const existingVerification = await KYCVerification.getLatestByUser(this.tenantId, userId);
            if (existingVerification && existingVerification.status === "IN_PROGRESS") {
                throw new KYCError(
                    'User already has a verification in progress',
                    'VERIFICATION_IN_PROGRESS'
                );
            }

            // Create new verification record
            const verification = new KYCVerification(
                this.tenantId,
                userId,
                clerkUserId,
                provider,
                level,
                {
                    provider,
                    verificationLevel: level,
                    ...metadata
                },
                {
                    status: "IN_PROGRESS"
                }
            );

            await verification.create();

            logger.info('KYC verification initialized', {
                verificationId: verification.id,
                userId,
                provider
            });

            return verification;
        } catch (error) {
            logger.error('Failed to initialize KYC verification', error);
            throw error;
        }
    }

    /**
     * Update verification status and metadata
     * @param verificationId - The verification ID to update
     * @param userId - The user ID
     * @param status - New status
     * @param metadata - Updated metadata
     * @param updateUser - Whether to update user KYC status
     * @returns Promise<KYCVerification>
     */
    async updateVerification(
        verificationId: string,
        userId: string,
        status: KYCStatus,
        metadata: Partial<KYCVerificationMetadata> = {},
        updateUser: boolean = true
    ): Promise<KYCVerification> {
        try {
            logger.info('Updating KYC verification', {
                verificationId,
                status,
                updateUser
            });

            const verification = await KYCVerification.getById(this.tenantId, userId, verificationId);
            if (!verification) {
                throw new KYCError(
                    'Verification not found',
                    'VERIFICATION_NOT_FOUND'
                );
            }

            // Update verification
            verification.status = status;
            verification.metadata = {
                ...verification.metadata,
                ...metadata
            };

            if (status === "VERIFIED") {
                verification.markAsVerified();
            } else if (status === "FAILED" || status === "REJECTED") {
                verification.markAsFailed('Verification failed');
            }

            await verification.update();

            // Update user KYC status if requested
            if (updateUser) {
                await this.updateUserKYCStatus(verification.userId, status, verification.verificationLevel);
            }

            logger.info('KYC verification updated', {
                verificationId,
                status,
                userId: verification.userId
            });

            return verification;
        } catch (error) {
            logger.error('Failed to update KYC verification', error);
            throw error;
        }
    }

    /**
     * Get verification by ID
     * @param verificationId - The verification ID
     * @param userId - The user ID
     * @returns Promise<KYCVerification | null>
     */
    async getVerification(verificationId: string, userId: string): Promise<KYCVerification | null> {
        try {
            return await KYCVerification.getById(this.tenantId, userId, verificationId);
        } catch (error) {
            logger.error('Failed to get KYC verification', error);
            return null;
        }
    }

    /**
     * Get latest verification for a user
     * @param userId - The user ID
     * @returns Promise<KYCVerification | null>
     */
    async getLatestVerificationByUser(userId: string): Promise<KYCVerification | null> {
        try {
            return await KYCVerification.getLatestByUser(this.tenantId, userId);
        } catch (error) {
            logger.error('Failed to get latest verification for user', error);
            return null;
        }
    }

    /**
     * Get verification by Clerk user ID
     * @param clerkUserId - The Clerk user ID
     * @returns Promise<KYCVerification | null>
     */
    async getVerificationByClerkUserId(clerkUserId: string): Promise<KYCVerification | null> {
        try {
            return await KYCVerification.getByClerkUserId(clerkUserId);
        } catch (error) {
            logger.error('Failed to get verification by Clerk user ID', error);
            return null;
        }
    }

    /**
     * Check if user is verified at a specific level
     * @param userId - The user ID
     * @param requiredLevel - The required verification level
     * @returns Promise<boolean>
     */
    async isUserVerified(userId: string, requiredLevel: KYCVerificationLevel): Promise<boolean> {
        try {
            const verification = await this.getLatestVerificationByUser(userId);
            
            if (!verification || verification.status !== "VERIFIED") {
                return false;
            }

            // Check if verification level meets requirement
            const levelHierarchy = {
                "BASIC": 1,
                "ENHANCED": 2,
                "PREMIUM": 3
            };

            return levelHierarchy[verification.verificationLevel] >= levelHierarchy[requiredLevel];
        } catch (error) {
            logger.error('Failed to check user verification status', error);
            return false;
        }
    }

    /**
     * Update user KYC status in User entity
     * @param userId - The user ID
     * @param status - KYC status
     * @param level - Verification level
     * @private
     */
    private async updateUserKYCStatus(
        userId: string,
        status: KYCStatus,
        level: KYCVerificationLevel
    ): Promise<void> {
        try {
            // This would need to be implemented based on the actual User entity structure
            logger.info('User KYC status update requested', {
                userId,
                status,
                level
            });
            
            // TODO: Implement user update when User entity structure is confirmed
        } catch (error) {
            logger.error('Failed to update user KYC status', error);
            // Don't throw here as this is a secondary operation
        }
    }

    /**
     * Get verification statistics for analytics
     * @param provider - Optional provider filter
     * @param dateRange - Optional date range filter
     * @returns Promise<any>
     */
    async getVerificationStats(
        provider?: KYCProvider,
        dateRange?: { start: string; end: string }
    ): Promise<any> {
        try {
            logger.info('Getting verification statistics', { provider, dateRange });
            
            // Placeholder for analytics implementation
            return {
                totalVerifications: 0,
                successRate: 0,
                averageCompletionTime: 0,
                providerBreakdown: {}
            };
        } catch (error) {
            logger.error('Failed to get verification statistics', error);
            throw error;
        }
    }

    /**
     * Clean up expired or stale verifications
     * @param maxAge - Maximum age in hours for in-progress verifications
     * @returns Promise<number> - Number of cleaned up verifications
     */
    async cleanupStaleVerifications(maxAge: number = 24): Promise<number> {
        try {
            logger.info('Cleaning up stale verifications', { maxAge });
            
            const cutoffDate = new Date();
            cutoffDate.setHours(cutoffDate.getHours() - maxAge);
            
            // This would require a scan operation or GSI query
            // Implementation depends on specific cleanup requirements
            
            return 0; // Placeholder
        } catch (error) {
            logger.error('Failed to cleanup stale verifications', error);
            throw error;
        }
    }
} 