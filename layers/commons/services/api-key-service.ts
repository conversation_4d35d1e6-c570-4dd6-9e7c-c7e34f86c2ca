import { randomBytes, createHash } from "crypto";
import { <PERSON><PERSON>ey, APIKeyStatus, APIKeyType, APIKeyRateLimit, APIKeyPermissions, createAP<PERSON><PERSON>ey, getAPIKey, getAPIKeyByHash, listAPIKeysByOrganization } from "../data/entities/api-key";
import { APIKeyAudit, APIKeyAuditMetadata } from "../data/entities/api-key-audit";
import { Organization } from "../data/entities/organization";
import { logger } from "../utils/logger";

export interface GenerateAPIKeyRequest {
  organizationId: string;
  tenantId: string;
  userId: string;
  userEmail?: string;
  type?: APIKeyType;
  label?: string;
  expiresAt?: string;
  rateLimit?: APIKeyRateLimit;
  permissions?: APIKeyPermissions;
}

export interface GenerateAPIKeyResponse {
  keyId: string;
  keySecret: string; // Full key value - shown only once
  keyPrefix: string; // Display prefix
  type: APIKeyType;
  label?: string;
  createdAt: string;
  expiresAt?: string;
}

export interface ValidateAPIKeyRequest {
  apiKey: string;
  endpoint?: string;
  method?: string;
  ip?: string;
  userAgent?: string;
}

export interface ValidateAPIKeyResponse {
  isValid: boolean;
  keyId?: string;
  organizationId?: string;
  tenantId?: string;
  type?: APIKeyType;
  permissions?: APIKeyPermissions;
  rateLimit?: APIKeyRateLimit;
  errorCode?: string;
  errorMessage?: string;
}

export interface APIKeyListItem {
  keyId: string;
  keyPrefix: string;
  label?: string;
  type: APIKeyType;
  status: APIKeyStatus;
  createdAt: string;
  expiresAt?: string;
  lastUsedAt?: string;
  usageCount?: number;
}

/**
 * Service for managing API keys
 */
export class APIKeyService {
  private static readonly KEY_PREFIX = "ckpl_";
  private static readonly KEY_LENGTH = 32; // 256 bits
  private static readonly HASH_ALGORITHM = "sha256";
  private static readonly DEFAULT_RATE_LIMIT: APIKeyRateLimit = {
    requestsPerSecond: 10,
    requestsPerMinute: 100,
    requestsPerHour: 1000,
    requestsPerDay: 10000,
  };

  /**
   * Generate a new API key
   */
  static async generateAPIKey(request: GenerateAPIKeyRequest): Promise<GenerateAPIKeyResponse> {
    logger.info("Generating new API key", {
      organizationId: request.organizationId,
      type: request.type || 'LIVE',
      label: request.label
    });

    try {
      // Validate organization exists and is active
      await this.validateOrganization(request.tenantId, request.organizationId);

      // Generate cryptographically secure random key
      const keyBytes = randomBytes(this.KEY_LENGTH);
      const keySecret = this.KEY_PREFIX + keyBytes.toString('base64url');

      // Create hash for storage
      const keyHash = this.hashKey(keySecret);

      // Extract prefix for display (first 8 chars after prefix)
      const keyPrefix = keySecret.substring(0, this.KEY_PREFIX.length + 8);

      // Create API key entity
      const apiKey = new APIKey(
        request.tenantId,
        request.organizationId,
        keyHash,
        keyPrefix,
        request.type || 'LIVE',
        {
          label: request.label,
          expiresAt: request.expiresAt,
          rateLimit: request.rateLimit || this.DEFAULT_RATE_LIMIT,
          permissions: request.permissions,
          metadata: {
            createdByUserId: request.userId,
            createdByEmail: request.userEmail,
          },
        }
      );

      // Save to database
      await createAPIKey(apiKey);

      // Create audit log
      await APIKeyAudit.logCreation(
        request.tenantId,
        request.organizationId,
        apiKey.id,
        request.userId,
        request.userEmail,
        {
          type: apiKey.type,
          label: apiKey.label,
        }
      );

      logger.info("API key generated successfully", {
        keyId: apiKey.id,
        keyPrefix: apiKey.keyPrefix
      });

      return {
        keyId: apiKey.id,
        keySecret, // Return full key only once
        keyPrefix: apiKey.keyPrefix,
        type: apiKey.type,
        label: apiKey.label,
        createdAt: apiKey.createdAt,
        expiresAt: apiKey.expiresAt,
      };
    } catch (error) {
      logger.error("Failed to generate API key", error as Error);
      throw error;
    }
  }

  /**
   * Validate an API key
   */
  static async validateAPIKey(request: ValidateAPIKeyRequest): Promise<ValidateAPIKeyResponse> {
    try {
      // Check key format
      if (!request.apiKey.startsWith(this.KEY_PREFIX)) {
        await APIKeyAudit.logFailedValidation(request.apiKey.substring(0, 12), {
          ip: request.ip,
          userAgent: request.userAgent,
          errorMessage: "Invalid key format",
        });
        return {
          isValid: false,
          errorCode: "INVALID_FORMAT",
          errorMessage: "Invalid API key format",
        };
      }

      // Hash the provided key
      const keyHash = this.hashKey(request.apiKey);

      // Look up key by hash
      const apiKey = await getAPIKeyByHash(keyHash);

      if (!apiKey) {
        const keyPrefix = request.apiKey.substring(0, this.KEY_PREFIX.length + 8);
        await APIKeyAudit.logFailedValidation(keyPrefix, {
          ip: request.ip,
          userAgent: request.userAgent,
          errorMessage: "Key not found",
        });
        return {
          isValid: false,
          errorCode: "KEY_NOT_FOUND",
          errorMessage: "API key not found",
        };
      }

      // Check if key is valid
      if (!apiKey.isValid) {
        const errorMessage = apiKey.status !== 'ACTIVE'
          ? `Key is ${apiKey.status.toLowerCase()}`
          : "Key is expired";

        await APIKeyAudit.logFailedValidation(apiKey.keyPrefix, {
          ip: request.ip,
          userAgent: request.userAgent,
          errorMessage,
          endpoint: request.endpoint,
          method: request.method,
        });

        return {
          isValid: false,
          keyId: apiKey.id,
          errorCode: apiKey.status !== 'ACTIVE' ? 'KEY_INACTIVE' : 'KEY_EXPIRED',
          errorMessage,
        };
      }

      // Check permissions if endpoint provided
      if (request.endpoint && apiKey.permissions?.allowedEndpoints) {
        const isAllowed = apiKey.permissions.allowedEndpoints.some(pattern =>
          this.matchEndpoint(request.endpoint!, pattern)
        );

        if (!isAllowed) {
          await APIKeyAudit.logFailedValidation(apiKey.keyPrefix, {
            ip: request.ip,
            userAgent: request.userAgent,
            errorMessage: "Endpoint not allowed",
            endpoint: request.endpoint,
            method: request.method,
          });

          return {
            isValid: false,
            keyId: apiKey.id,
            errorCode: "ENDPOINT_NOT_ALLOWED",
            errorMessage: "This API key is not authorized for this endpoint",
          };
        }
      }

      // Check IP whitelist if configured
      if (apiKey.permissions?.ipWhitelist && request.ip) {
        const isAllowed = apiKey.permissions.ipWhitelist.includes(request.ip);

        if (!isAllowed) {
          await APIKeyAudit.logFailedValidation(apiKey.keyPrefix, {
            ip: request.ip,
            userAgent: request.userAgent,
            errorMessage: "IP not whitelisted",
            endpoint: request.endpoint,
            method: request.method,
          });

          return {
            isValid: false,
            keyId: apiKey.id,
            errorCode: "IP_NOT_ALLOWED",
            errorMessage: "This IP address is not authorized",
          };
        }
      }

      // Update usage metadata
      await apiKey.updateUsage(request.ip, request.userAgent);
      await apiKey.update();

      // Log successful access
      await APIKeyAudit.logAccess(
        apiKey.tenantId,
        apiKey.organizationId,
        apiKey.id,
        {
          ip: request.ip,
          userAgent: request.userAgent,
          endpoint: request.endpoint,
          method: request.method,
        }
      );

      return {
        isValid: true,
        keyId: apiKey.id,
        organizationId: apiKey.organizationId,
        tenantId: apiKey.tenantId,
        type: apiKey.type,
        permissions: apiKey.permissions,
        rateLimit: apiKey.rateLimit,
      };
    } catch (error) {
      logger.error("Failed to validate API key", error as Error);
      return {
        isValid: false,
        errorCode: "VALIDATION_ERROR",
        errorMessage: "Failed to validate API key",
      };
    }
  }

  /**
   * List API keys for an organization
   */
  static async listAPIKeys(
    tenantId: string,
    organizationId: string,
    status?: APIKeyStatus
  ): Promise<APIKeyListItem[]> {
    logger.info("Listing API keys", { tenantId, organizationId, status });

    try {
      const keys = await listAPIKeysByOrganization(tenantId, organizationId, status);

      return keys.map(key => ({
        keyId: key.id,
        keyPrefix: key.keyPrefix,
        label: key.label,
        type: key.type,
        status: key.status,
        createdAt: key.createdAt,
        expiresAt: key.expiresAt,
        lastUsedAt: key.metadata?.lastUsedAt,
        usageCount: key.metadata?.usageCount,
      }));
    } catch (error) {
      logger.error("Failed to list API keys", error as Error);
      throw error;
    }
  }

  /**
   * Revoke an API key
   */
  static async revokeAPIKey(
    tenantId: string,
    organizationId: string,
    keyId: string,
    userId: string,
    reason?: string
  ): Promise<void> {
    logger.info("Revoking API key", { keyId, userId, reason });

    try {
      const apiKey = await getAPIKey(tenantId, organizationId, keyId);

      if (apiKey.status === 'REVOKED') {
        logger.warn("API key already revoked", { keyId });
        return;
      }

      // Revoke the key
      apiKey.revoke(userId, reason);
      await apiKey.update();

      // Create audit log
      await APIKeyAudit.logCreation(
        tenantId,
        organizationId,
        keyId,
        userId,
        undefined,
        {
          action: 'REVOKED',
          reason,
        }
      );

      logger.info("API key revoked successfully", { keyId });
    } catch (error) {
      logger.error("Failed to revoke API key", error as Error);
      throw error;
    }
  }

  /**
   * Update API key label
   */
  static async updateAPIKeyLabel(
    tenantId: string,
    organizationId: string,
    keyId: string,
    label: string,
    userId: string
  ): Promise<void> {
    logger.info("Updating API key label", { keyId, label });

    try {
      const apiKey = await getAPIKey(tenantId, organizationId, keyId);
      const oldLabel = apiKey.label;

      apiKey.label = label;
      apiKey.updateTimestamp();
      await apiKey.update();

      // Create audit log
      await APIKeyAudit.logCreation(
        tenantId,
        organizationId,
        keyId,
        userId,
        undefined,
        {
          action: 'UPDATED',
          changes: {
            label: { old: oldLabel, new: label },
          },
        }
      );

      logger.info("API key label updated successfully", { keyId });
    } catch (error) {
      logger.error("Failed to update API key label", error as Error);
      throw error;
    }
  }

  /**
   * Get API key details
   */
  static async getAPIKeyDetails(
    tenantId: string,
    organizationId: string,
    keyId: string
  ): Promise<APIKeyListItem & {
    rateLimit?: APIKeyRateLimit;
    permissions?: APIKeyPermissions;
  }> {
    logger.info("Getting API key details", { keyId });

    try {
      const apiKey = await getAPIKey(tenantId, organizationId, keyId);

      return {
        keyId: apiKey.id,
        keyPrefix: apiKey.keyPrefix,
        label: apiKey.label,
        type: apiKey.type,
        status: apiKey.status,
        createdAt: apiKey.createdAt,
        expiresAt: apiKey.expiresAt,
        lastUsedAt: apiKey.metadata?.lastUsedAt,
        usageCount: apiKey.metadata?.usageCount,
        rateLimit: apiKey.rateLimit,
        permissions: apiKey.permissions,
      };
    } catch (error) {
      logger.error("Failed to get API key details", error as Error);
      throw error;
    }
  }

  /**
   * Get API key audit logs
   */
  static async getAPIKeyAuditLogs(
    tenantId: string,
    organizationId: string,
    keyId: string,
    options?: {
      startTime?: string;
      endTime?: string;
      limit?: number;
    }
  ): Promise<APIKeyAudit[]> {
    logger.info("Getting API key audit logs", { keyId, ...options });

    try {
      const result = await APIKeyAudit.getByAPIKey(
        tenantId,
        organizationId,
        keyId,
        options
      );

      return result.audits;
    } catch (error) {
      logger.error("Failed to get API key audit logs", error as Error);
      throw error;
    }
  }

  /**
   * Hash an API key using SHA-256
   */
  private static hashKey(key: string): string {
    return createHash(this.HASH_ALGORITHM).update(key).digest('hex');
  }

  /**
   * Validate organization exists and is active
   */
  private static async validateOrganization(tenantId: string, organizationId: string): Promise<void> {
    try {
      const org = await Organization.getById(tenantId, organizationId);
      if (!org || org.status !== 'ACTIVE') {
        throw new Error("Organization not found or inactive");
      }
    } catch (error) {
      logger.error("Organization validation failed", error as Error);
      throw new Error("Invalid organization");
    }
  }

  /**
   * Match an endpoint against a pattern (supports wildcards)
   */
  private static matchEndpoint(endpoint: string, pattern: string): boolean {
    // Convert pattern to regex (e.g., /api/v1/* becomes /^\/api\/v1\/.*$/)
    const regexPattern = pattern
      .replace(/[.+?^${}()|[\]\\]/g, '\\$&') // Escape special chars
      .replace(/\*/g, '.*'); // Replace * with .*

    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(endpoint);
  }

  /**
   * Check rate limits (placeholder - implement with Redis/ElastiCache)
   */
  static async checkRateLimit(
    keyId: string,
    rateLimit: APIKeyRateLimit
  ): Promise<{ allowed: boolean; remainingRequests?: number }> {
    // TODO: Implement with Redis/ElastiCache for distributed rate limiting
    // For now, return allowed
    return { allowed: true };
  }
}

// Export for use in Lambda functions
export const apiKeyService = APIKeyService;