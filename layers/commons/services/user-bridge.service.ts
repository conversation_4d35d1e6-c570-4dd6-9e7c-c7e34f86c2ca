import { User, UserStatus } from "../data/entities/user";
import { UserType } from "../types";
import { ClerkUserData } from "../types/clerk-webhook-types";
import { logger } from "../utils/logger";

/**
 * Service to bridge between Clerk user data and our User entities
 * This helps maintain consistency between webhook-created users and entity-based users
 */
export class UserBridgeService {
  
  /**
   * Convert Clerk user data to our User entity
   * Note: Users from webhooks don't need organization information initially
   */
  static clerkUserToEntity(
    clerkUser: ClerkUserData,
    organizationId?: string,
    role?: UserType
  ): User | null {
    // Extract primary email
    const email = UserBridgeService.getPrimaryEmail(clerkUser);
    if (!email) {
      logger.warn("Cannot create User entity without email", { clerkUserId: clerkUser.id });
      return null;
    }

    // Determine organization ID and role
    const finalOrganizationId = organizationId || 
                               clerkUser.public_metadata?.organizationId || 
                               clerkUser.private_metadata?.organizationId;
    
    const finalRole = role || 
                     (clerkUser.public_metadata?.role as UserType) || 
                     (clerkUser.private_metadata?.role as UserType) || 
                     "user";

    // Create User entity - organization is optional for webhook users
    return new User(
      "default", // Keep minimal tenant for BaseEntity compatibility
      clerkUser.id,
      email,
      finalRole,
      "ACTIVE",
      {
        organizationId: finalOrganizationId,
        firstName: clerkUser.first_name,
        lastName: clerkUser.last_name,
        phoneNumber: UserBridgeService.getPrimaryPhone(clerkUser),
        profilePictureUrl: clerkUser.image_url,
      }
    );
  }

  /**
   * Get primary email from Clerk user data
   */
  static getPrimaryEmail(clerkUser: ClerkUserData): string | undefined {
    if (!clerkUser.email_addresses || clerkUser.email_addresses.length === 0) {
      return undefined;
    }

    // If primary email is set, find that email
    if (clerkUser.primary_email_address_id) {
      const primaryEmail = clerkUser.email_addresses.find(
        (email) => email.id === clerkUser.primary_email_address_id
      );

      if (primaryEmail) {
        return primaryEmail.email_address;
      }
    }

    // Otherwise just return the first email
    return clerkUser.email_addresses[0].email_address;
  }

  /**
   * Get primary phone from Clerk user data
   */
  static getPrimaryPhone(clerkUser: ClerkUserData): string | undefined {
    if (!clerkUser.phone_numbers || clerkUser.phone_numbers.length === 0) {
      return undefined;
    }

    // If primary phone is set, find that phone
    if (clerkUser.primary_phone_number_id) {
      const primaryPhone = clerkUser.phone_numbers.find(
        (phone) => phone.id === clerkUser.primary_phone_number_id
      );

      if (primaryPhone) {
        return primaryPhone.phone_number;
      }
    }

    // Otherwise just return the first phone
    return clerkUser.phone_numbers[0].phone_number;
  }

  /**
   * Check if a Clerk user can be converted to a User entity
   */
  static canConvertToEntity(clerkUser: ClerkUserData): boolean {
    const hasEmail = UserBridgeService.getPrimaryEmail(clerkUser) !== undefined;
    const hasOrganization = !!(
      clerkUser.public_metadata?.organizationId || 
      clerkUser.private_metadata?.organizationId
    );
    
    return hasEmail && hasOrganization;
  }

  /**
   * Get user data from webhook-style user record
   * This helps when we need to work with users created by webhooks
   */
  static async getUserFromWebhookRecord(
    clerkUserId: string
  ): Promise<any | null> {
    try {
      // Import here to avoid circular dependencies
      const { getItem } = await import("../dynamodb/db-operations");
      
      const userRecord = await getItem(`USER#${clerkUserId}`, "PROFILE");
      return userRecord;
    } catch (error) {
      logger.error("Error getting user from webhook record", { 
        error, 
        clerkUserId
      });
      return null;
    }
  }

  /**
   * Convert webhook user record to User entity if possible
   */
  static webhookRecordToEntity(
    webhookRecord: any
  ): User | null {
    if (!webhookRecord || !webhookRecord.email) {
      return null;
    }

    try {
      return new User(
        "default", // Keep minimal tenant for BaseEntity compatibility
        webhookRecord.clerkId,
        webhookRecord.email,
        webhookRecord.role || "user",
        webhookRecord.status || "ACTIVE",
        {
          organizationId: webhookRecord.organizationId,
          firstName: webhookRecord.firstName,
          lastName: webhookRecord.lastName,
          phoneNumber: webhookRecord.phone,
          profilePictureUrl: webhookRecord.imageUrl,
          lastLoginAt: webhookRecord.lastLoginAt,
        }
      );
    } catch (error) {
      logger.error("Error converting webhook record to User entity", { 
        error, 
        webhookRecord 
      });
      return null;
    }
  }

  /**
   * Update User entity with Clerk user data
   */
  static updateEntityFromClerkUser(user: User, clerkUser: ClerkUserData): User {
    // Update basic fields
    const email = UserBridgeService.getPrimaryEmail(clerkUser);
    if (email) {
      user.email = email;
    }

    user.firstName = clerkUser.first_name;
    user.lastName = clerkUser.last_name;
    user.profilePictureUrl = clerkUser.image_url;

    const phone = UserBridgeService.getPrimaryPhone(clerkUser);
    if (phone) {
      user.phoneNumber = phone;
    }

    // Update role if provided in metadata
    const role = clerkUser.public_metadata?.role as UserType;
    if (role && ["super_admin", "org_admin", "agent", "user"].includes(role)) {
      user.role = role;
    }

    // Update organization if provided in metadata
    const organizationId = clerkUser.public_metadata?.organizationId || clerkUser.private_metadata?.organizationId;
    if (organizationId) {
      user.organizationId = organizationId;
    }

    return user;
  }
} 