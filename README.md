# CLKK SaaS Backend

A serverless backend system for CLKK SaaS platform built with AWS SAM, featuring secure secret management, KYC verification, payment processing, and user management.

## 🔐 Security Features

- **AWS Secrets Manager Integration**: All sensitive credentials are stored securely in AWS Secrets Manager
- **AWS Parameters and Secrets Lambda Extension**: Optimized secret retrieval with caching
- **Least Privilege IAM**: Functions only have access to the secrets they need
- **Automatic Secret Rotation**: Support for rotating secrets without code changes

## 🏗️ Architecture

The system uses a modular serverless architecture with:

- **AWS Lambda**: Serverless compute for all business logic
- **AWS Secrets Manager**: Secure storage for API keys and sensitive configuration
- **Amazon DynamoDB**: NoSQL database for application data
- **Amazon API Gateway**: RESTful API endpoints
- **AWS SNS/SQS**: Event-driven messaging for webhook processing
- **AWS CloudWatch**: Monitoring and logging

## 📋 Prerequisites

- AWS CLI configured with appropriate permissions
- Node.js 18.x or later
- AWS SAM CLI
- Docker (for local development)

## 🚀 Quick Start

### 1. Setup Secrets

Before deploying, you need to create the required secrets in AWS Secrets Manager:

```bash
# Run the interactive secrets setup script
./scripts/setup-secrets.sh

# Or specify environment and region
./scripts/setup-secrets.sh -e prod -r us-west-2

# Or provide secrets via environment variables
CLERK_WEBHOOK_SECRET="your-webhook-secret" \
CLERK_JWT_PUBLIC_KEY="your-jwt-public-key" \
PROVE_CLIENT_ID="your-prove-client-id" \
PROVE_CLIENT_SECRET="your-prove-client-secret" \
./scripts/setup-secrets.sh
```

The script will create the following secrets:
- `{environment}/clkk/clerk/webhook-secret`: Clerk webhook verification secret
- `{environment}/clkk/clerk/jwt-public-key`: Clerk JWT public key for token verification
- `{environment}/clkk/prove/api-credentials`: Prove KYC API credentials

### 2. Deploy the Application

```bash
# Build and deploy
sam build
sam deploy --guided

# Or deploy with specific parameters
sam deploy --parameter-overrides \
  Environment=dev \
  ClerkApiKey="your-clerk-api-key" \
  PocketKnightsApiToken="your-pk-token" \
  CashAppMerchantNo="your-cashapp-merchant"
```

### 3. Verify Deployment

```bash
# Check the API endpoint
aws cloudformation describe-stacks \
  --stack-name clkk-saas-backend-dev \
  --query 'Stacks[0].Outputs[?OutputKey==`PaymentApiEndpoint`].OutputValue' \
  --output text
```

## 🔧 Configuration

### Environment Variables

The following environment variables are automatically set by the deployment:

- `ENVIRONMENT`: Deployment environment (dev/staging/prod)
- `TABLE_NAME`: DynamoDB table name
- `PARAMETERS_SECRETS_EXTENSION_*`: AWS Secrets Manager extension configuration

### Secrets Management

All sensitive configuration is stored in AWS Secrets Manager:

#### Clerk Configuration
```json
{
  "webhookSecret": "your-clerk-webhook-secret",
  "publicKey": "your-clerk-jwt-public-key"
}
```

#### Prove KYC Configuration
```json
{
  "clientId": "your-prove-client-id",
  "clientSecret": "your-prove-client-secret",
  "environment": "SANDBOX"
}
```

### Secret Rotation

To rotate secrets:

1. Update the secret value in AWS Secrets Manager
2. The Lambda functions will automatically use the new value on the next cold start
3. For immediate effect, you can clear the cache by redeploying or updating the function

## 🧪 Testing

### Local Development

```bash
# Start local API
sam local start-api

# Test specific function
sam local invoke KYCStartFunction --event events/kyc/start-kyc-verification.json

# Test with environment variables
sam local start-api --env-vars env.json
```

### Integration Tests

```bash
# Run the test suite
cd events/test
npm install
npm test
```

## 📚 API Documentation

### Authentication

All API endpoints require Clerk JWT authentication. Include the token in the Authorization header:

```
Authorization: Bearer <clerk-jwt-token>
```

### Endpoints

#### KYC Verification
- `POST /kyc/start` - Start KYC verification
- `POST /kyc/validate/{correlationId}` - Validate verification step
- `POST /kyc/challenge/{correlationId}` - Handle challenge step
- `POST /kyc/complete/{correlationId}` - Complete verification
- `GET /kyc/status/{verificationId}` - Get verification status

#### Payment Processing
- `POST /payments/cashapp/create` - Create CashApp payment
- `GET /payments/cashapp/status/{paymentId}` - Check payment status

#### Organization Management
- `POST /organizations` - Create organization
- `POST /organizations/applications` - Submit application
- `PUT /organizations/applications/{id}/approve` - Approve application

### Webhooks

#### Clerk Webhooks
The system processes Clerk webhook events for user and organization management:

- `user.created` - Creates user record in DynamoDB
- `user.updated` - Updates user information
- `user.deleted` - Soft deletes user record
- `organization.created` - Creates organization record
- `organization.updated` - Updates organization information

## 🔍 Monitoring

### CloudWatch Metrics

The system automatically publishes custom metrics:
- `WebhookReceived` - Number of webhooks received
- `WebhookProcessed` - Number of successfully processed webhooks
- `WebhookFailed` - Number of failed webhook processing attempts

### Alarms

CloudWatch alarms are configured for:
- Lambda function errors
- DLQ message accumulation
- High latency alerts

### Logging

All functions use structured logging with correlation IDs for tracing requests across services.

## 🛠️ Development

### Project Structure

```
├── lambdas/                    # Lambda function code
│   ├── auth/                   # Authentication functions
│   ├── clerk-webhooks/         # Webhook processors
│   ├── kyc/                    # KYC verification functions
│   ├── organizations/          # Organization management
│   ├── payments/               # Payment processing
│   └── users/                  # User management
├── layers/commons/             # Shared code layer
│   ├── constants/              # Application constants
│   ├── data/                   # Data access layer
│   ├── dynamodb/               # DynamoDB operations
│   ├── services/               # Business logic services
│   ├── types/                  # TypeScript type definitions
│   └── utils/                  # Utility functions
├── iac/                        # Infrastructure as Code
├── scripts/                    # Deployment and utility scripts
└── events/                     # Test events and examples
```

### Adding New Secrets

1. Update `layers/commons/constants/secrets.ts` with the new secret name
2. Add the secret to the `scripts/setup-secrets.sh` script
3. Update the CloudFormation templates with appropriate IAM permissions
4. Use the `secretsManager` service in your Lambda functions

### Best Practices

- Always use the `secretsManager` service for retrieving secrets
- Implement proper error handling for secret retrieval failures
- Use the AWS Parameters and Secrets Lambda Extension for optimal performance
- Follow the principle of least privilege for IAM permissions
- Regularly rotate secrets and monitor access

## 🚨 Troubleshooting

### Common Issues

1. **Secret Not Found**: Ensure secrets are created before deployment
2. **Permission Denied**: Check IAM policies for Secrets Manager access
3. **Cold Start Latency**: The first request may be slower due to secret retrieval

### Debug Commands

```bash
# Check secret exists
aws secretsmanager describe-secret --secret-id "dev/clkk/clerk/webhook-secret"

# View CloudWatch logs
aws logs tail /aws/lambda/clkk-kyc-start-dev --follow

# Test secret retrieval
aws secretsmanager get-secret-value --secret-id "dev/clkk/prove/api-credentials"
```

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

For support and questions, please contact the development team or create an issue in the repository.
