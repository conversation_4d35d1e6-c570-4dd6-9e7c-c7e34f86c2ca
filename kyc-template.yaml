AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: 'CLKK SAAS KYC Verification System - Lambda Functions and Infrastructure'

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues: [dev, staging, prod]
    Description: Environment name

  TableName:
    Type: String
    Description: DynamoDB table name for storing data

  LayerCommons:
    Type: String
    Description: ARN of the shared commons layer

  ClerkAuthorizerArn:
    Type: String
    Description: ARN of the Clerk authorizer function

Globals:
  Function:
    Timeout: 30
    Runtime: nodejs18.x
    MemorySize: 512
    Environment:
      Variables:
        TABLE_NAME: !Ref TableName
        ENVIRONMENT: !Ref Environment
        NODE_ENV: !Ref Environment
        PARAMETERS_SECRETS_EXTENSION_CACHE_ENABLED: false
        PARAMETERS_SECRETS_EXTENSION_CACHE_SIZE: 1000
        SECRETS_MANAGER_TTL: 0
    Layers:
      - !Ref LayerCommons
      - !Sub "arn:aws:lambda:${AWS::Region}:177933569100:layer:AWS-Parameters-and-Secrets-Lambda-Extension-Arm64:11"

Resources:

  # API Gateway for KYC endpoints
  KYCApi:
    Type: AWS::Serverless::Api
    Properties:
      Name: !Sub 'clkk-kyc-api-${Environment}'
      StageName: !Ref Environment
      Cors:
        AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-User-Id,X-Clerk-User-Id,X-Tenant-Id'"
        AllowOrigin: "'*'"
      Auth:
        DefaultAuthorizer: AWS_IAM
      GatewayResponses:
        DEFAULT_4XX:
          ResponseParameters:
            Headers:
              Access-Control-Allow-Origin: "'*'"
              Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-User-Id,X-Clerk-User-Id,X-Tenant-Id'"
        DEFAULT_5XX:
          ResponseParameters:
            Headers:
              Access-Control-Allow-Origin: "'*'"
              Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-User-Id,X-Clerk-User-Id,X-Tenant-Id'"

  # KYC Start Verification Function
  KYCStartFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub 'clkk-kyc-start-${Environment}'
      CodeUri: lambdas/kyc/start-verification/
      Handler: index.handler
      Description: 'Start KYC verification process'
      Events:
        StartKYC:
          Type: Api
          Properties:
            RestApiId: !Ref KYCApi
            Path: /kyc/start
            Method: POST
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref TableName
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - kms:Decrypt
                - kms:Encrypt
                - kms:GenerateDataKey
              Resource: '*'
            - Effect: Allow
              Action:
                - secretsmanager:GetSecretValue
              Resource: !Sub "arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${Environment}/clkk/prove/api-credentials*"
    Metadata:
      BuildMethod: esbuild
      BuildProperties:
        External:
          - commons
        Minify: true
        Target: es2020
        Sourcemap: true
        EntryPoints:
          - index.ts

  # KYC Validate Function
  KYCValidateFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub 'clkk-kyc-validate-${Environment}'
      CodeUri: lambdas/kyc/validate-step/
      Handler: index.handler
      Description: 'Validate KYC verification step'
      Events:
        ValidateKYC:
          Type: Api
          Properties:
            RestApiId: !Ref KYCApi
            Path: /kyc/validate/{correlationId}
            Method: POST
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref TableName
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - secretsmanager:GetSecretValue
              Resource: !Sub "arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${Environment}/clkk/prove/api-credentials*"
    Metadata:
      BuildMethod: esbuild
      BuildProperties:
        External:
          - commons
        Minify: true
        Target: es2020
        Sourcemap: true
        EntryPoints:
          - index.ts

  # KYC Complete Function
  KYCCompleteFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub 'clkk-kyc-complete-${Environment}'
      CodeUri: lambdas/kyc/complete-verification/
      Handler: index.handler
      Description: 'Complete KYC verification process'
      Events:
        CompleteKYC:
          Type: Api
          Properties:
            RestApiId: !Ref KYCApi
            Path: /kyc/complete/{correlationId}
            Method: POST
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref TableName
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - secretsmanager:GetSecretValue
              Resource: !Sub "arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${Environment}/clkk/prove/api-credentials*"
    Metadata:
      BuildMethod: esbuild
      BuildProperties:
        External:
          - commons
        Minify: true
        Target: es2020
        Sourcemap: true
        EntryPoints:
          - index.ts

  # KYC Status Function
  KYCStatusFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub 'clkk-kyc-status-${Environment}'
      CodeUri: lambdas/kyc/get-status/
      Handler: index.handler
      Description: 'Get KYC verification status'
      Events:
        GetKYCStatus:
          Type: Api
          Properties:
            RestApiId: !Ref KYCApi
            Path: /kyc/status/{verificationId}
            Method: GET
        GetUserKYCStatus:
          Type: Api
          Properties:
            RestApiId: !Ref KYCApi
            Path: /kyc/user/status
            Method: GET
      Policies:
        - DynamoDBReadPolicy:
            TableName: !Ref TableName
    Metadata:
      BuildMethod: esbuild
      BuildProperties:
        External:
          - commons
        Minify: true
        Target: es2020
        Sourcemap: true
        EntryPoints:
          - index.ts

  # KYC Challenge Function (for Prove multi-step verification)
  KYCChallengeFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub 'clkk-kyc-challenge-${Environment}'
      CodeUri: lambdas/kyc/challenge-step/
      Handler: index.handler
      Description: 'Handle KYC verification challenge step'
      Events:
        ChallengeKYC:
          Type: Api
          Properties:
            RestApiId: !Ref KYCApi
            Path: /kyc/challenge/{correlationId}
            Method: POST
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref TableName
        - Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Action:
                - secretsmanager:GetSecretValue
              Resource: !Sub "arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${Environment}/clkk/prove/api-credentials*"

  # CloudWatch Log Groups
  KYCStartLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/lambda/clkk-kyc-start-${Environment}'
      RetentionInDays: 14

  KYCValidateLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/lambda/clkk-kyc-validate-${Environment}'
      RetentionInDays: 14

  KYCCompleteLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/lambda/clkk-kyc-complete-${Environment}'
      RetentionInDays: 14

  KYCStatusLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/lambda/clkk-kyc-status-${Environment}'
      RetentionInDays: 14

  KYCChallengeLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/lambda/clkk-kyc-challenge-${Environment}'
      RetentionInDays: 14

Outputs:
  KYCApiUrl:
    Description: 'API Gateway endpoint URL for KYC functions'
    Value: !Sub 'https://${KYCApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}'
    Export:
      Name: !Sub '${AWS::StackName}-KYCApiUrl'

  KYCApiId:
    Description: 'API Gateway ID for KYC functions'
    Value: !Ref KYCApi
    Export:
      Name: !Sub '${AWS::StackName}-KYCApiId'

  CommonsLayerArn:
    Description: 'Commons Layer ARN'
    Value: !Ref LayerCommons
    Export:
      Name: !Sub '${AWS::StackName}-CommonsLayerArn'

  KYCStartFunctionArn:
    Description: 'KYC Start Function ARN'
    Value: !GetAtt KYCStartFunction.Arn
    Export:
      Name: !Sub '${AWS::StackName}-KYCStartFunctionArn'

  KYCValidateFunctionArn:
    Description: 'KYC Validate Function ARN'
    Value: !GetAtt KYCValidateFunction.Arn
    Export:
      Name: !Sub '${AWS::StackName}-KYCValidateFunctionArn'

  KYCCompleteFunctionArn:
    Description: 'KYC Complete Function ARN'
    Value: !GetAtt KYCCompleteFunction.Arn
    Export:
      Name: !Sub '${AWS::StackName}-KYCCompleteFunctionArn'

  KYCStatusFunctionArn:
    Description: 'KYC Status Function ARN'
    Value: !GetAtt KYCStatusFunction.Arn
    Export:
      Name: !Sub '${AWS::StackName}-KYCStatusFunctionArn'

  KYCChallengeFunctionArn:
    Description: 'KYC Challenge Function ARN'
    Value: !GetAtt KYCChallengeFunction.Arn
    Export:
      Name: !Sub '${AWS::StackName}-KYCChallengeFunctionArn'