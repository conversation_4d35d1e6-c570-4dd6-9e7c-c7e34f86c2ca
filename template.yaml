AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: >
  CLKK Payment System - Root SAM Template orchestrating payment infrastructure

# More info about Globals: https://github.com/awslabs/serverless-application-model/blob/master/docs/globals.rst
Globals:
  Function:
    Runtime: nodejs18.x
    Architectures:
      - arm64
    MemorySize: 1024
    Timeout: 30
    Tracing: Active
    # You can add LoggingConfig for individual functions but not in Globals

  Api:
    TracingEnabled: true

Parameters:
  Environment:
    Type: String
    Default: dev
    Description: The deployment stage (e.g., dev, prod, staging).
    AllowedValues:
      - dev
      - prod
      - staging
  ClerkJwtPublicKeySsmParamPath:
    Type: String
    Default: /clkk/clerk/jwt-public-key
    Description: SSM Parameter Store path for the Clerk JWT Public Key.

  # Secret Names (not values) - these are the names of secrets in Secrets Manager
  # Lambda functions will retrieve the actual secret values using these names
  ClerkWebhookSecretName:
    Type: String
    Description: "Name of the Clerk webhook secret in Secrets Manager"
    Default: "dev/clkk/clerk/webhook-secret"

  ClerkJwtPublicKeySecretName:
    Type: String
    Description: "Name of the Clerk JWT public key secret in Secrets Manager"
    Default: "dev/clkk/clerk/jwt-public-key"

  ProveApiCredentialsSecretName:
    Type: String
    Description: "Name of the Prove API credentials secret in Secrets Manager"
    Default: "dev/clkk/prove/api-credentials"

  PocketKnightsApiSecretName:
    Type: String
    Description: "Name of the PocketKnights API secret in Secrets Manager"
    Default: "dev/clkk/pocketknights/api-credentials"

  # Payment Provider Parameters
  PocketKnightsBaseUrl:
    Type: String
    Description: "Base URL for PocketKnights API"
    Default: "https://api.pocketknights.com"

  CashAppMerchantNo:
    Type: String
    Description: "CashApp Merchant Number"
    NoEcho: true
    Default: "default"

  CashAppScriptUrl:
    Type: String
    Description: "CashApp SDK Script URL"
    Default: "https://sandbox.kit.cash.app/v1/pay.js"

  # KYC Provider Parameters
  ProveClientId:
    Type: String
    Description: "Prove API Client ID"
    NoEcho: true
    Default: "default"

  ProveClientSecret:
    Type: String
    Description: "Prove API Client Secret"
    NoEcho: true
    Default: "default"

  ProveEnvironment:
    Type: String
    Description: "Prove API Environment"
    Default: "SANDBOX"
    AllowedValues:
      - SANDBOX
      - PRODUCTION

  ProveEnabled:
    Type: String
    Description: "Whether Prove KYC is enabled"
    Default: "true"
    AllowedValues:
      - "true"
      - "false"

Resources:
  # ================================================
  # SECRETS MANAGER RESOURCES
  # ================================================
  # Note: These secrets are created empty and must be populated manually
  # or via the setup-secrets.sh script after deployment

  ClerkWebhookSecretResource:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub "${Environment}/clkk/clerk/webhook-secret"
      Description: "Clerk webhook secret for verifying webhook signatures"
      KmsKeyId: alias/aws/secretsmanager
      ReplicaRegions: []
    UpdateReplacePolicy: Retain
    DeletionPolicy: Retain

  ClerkJwtPublicKeySecretResource:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub "${Environment}/clkk/clerk/jwt-public-key"
      Description: "Clerk JWT public key for token verification (stored as plain text)"
      KmsKeyId: alias/aws/secretsmanager
      ReplicaRegions: []
    UpdateReplacePolicy: Retain
    DeletionPolicy: Retain

  ProveApiCredentialsSecretResource:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub "${Environment}/clkk/prove/api-credentials"
      Description: "Prove API credentials (client ID and secret)"
      KmsKeyId: alias/aws/secretsmanager
      ReplicaRegions: []
    UpdateReplacePolicy: Retain
    DeletionPolicy: Retain

  PocketKnightsApiCredentialsSecretResource:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub "${Environment}/clkk/pocketknights/api-credentials"
      Description: "PocketKnights API credentials"
      KmsKeyId: alias/aws/secretsmanager
      ReplicaRegions: []
    UpdateReplacePolicy: Retain
    DeletionPolicy: Retain

  # ================================================
  # CORE INFRASTRUCTURE
  # ================================================

  DynamoStack:
    Type: AWS::Serverless::Application
    Properties:
      Location: iac/dynamo/dynamo-stack.yaml
      Parameters:
        Environment: !Ref Environment
    DeletionPolicy: Delete
    UpdateReplacePolicy: Delete


  NetworkingStack:
    Type: AWS::Serverless::Application
    Properties:
      Location: ./iac/shared/networking.yaml
      Parameters:
        Environment: !Ref Environment
      TimeoutInMinutes: 30
      Tags:
        Project: CLKK-SAAS
        Environment: !Ref Environment

  SharedInfraStack:
    Type: AWS::Serverless::Application
    Properties:
      Location: iac/shared/shared-infra-stack.yaml
      Parameters:
        Environment: !Ref Environment
        ClerkJwtPublicKeySsmParamName: !Ref ClerkJwtPublicKeySsmParamPath
        ClerkJwtPublicKeySecretName: !Ref ClerkJwtPublicKeySecretName
        DynamoDbTableName: !GetAtt DynamoStack.Outputs.TableName
    DeletionPolicy: Delete
    UpdateReplacePolicy: Delete

  # ================================================
  # PAYMENT SYSTEM
  # ================================================

  PaymentsStack:
    Type: AWS::Serverless::Application
    Properties:
      Location: iac/payments/payments-stack.yaml
      Parameters:
        Environment: !Ref Environment
        LayerCommons: !GetAtt SharedInfraStack.Outputs.LayerCommons
        TableName: !GetAtt DynamoStack.Outputs.TableName
        ClerkAuthorizerArn: !GetAtt SharedInfraStack.Outputs.ClerkAuthorizerArn
        PocketKnightsBaseUrl: !Ref PocketKnightsBaseUrl
        PocketKnightsApiSecretName: !Ref PocketKnightsApiSecretName
        CashAppMerchantNo: !Ref CashAppMerchantNo
        CashAppScriptUrl: !Ref CashAppScriptUrl
    DeletionPolicy: Delete
    UpdateReplacePolicy: Delete

  # ================================================
  # PARTNER API KEY MANAGEMENT SYSTEM
  # ================================================

  PartnerStack:
    Type: AWS::Serverless::Application
    Properties:
      Location: iac/partners/partners-stack.yaml
      Parameters:
        Environment: !Ref Environment
        LayerCommons: !GetAtt SharedInfraStack.Outputs.LayerCommons
        TableName: !GetAtt DynamoStack.Outputs.TableName
        ClerkAuthorizerArn: !GetAtt SharedInfraStack.Outputs.ClerkAuthorizerArn
    DeletionPolicy: Delete
    UpdateReplacePolicy: Delete

  # ================================================
  # KYC VERIFICATION SYSTEM
  # ================================================

  KYCStack:
    Type: AWS::Serverless::Application
    Properties:
      Location: kyc-template.yaml
      Parameters:
        Environment: !Ref Environment
        TableName: !GetAtt DynamoStack.Outputs.TableName
        LayerCommons: !GetAtt SharedInfraStack.Outputs.LayerCommons
        ClerkAuthorizerArn: !GetAtt SharedInfraStack.Outputs.ClerkAuthorizerArn
    DeletionPolicy: Delete
    UpdateReplacePolicy: Delete

  # ================================================
  # WEBHOOK HANDLERS
  # ================================================

  ClerkWebhooksStack:
    Type: AWS::Serverless::Application
    Properties:
      Location: iac/clerk-webhooks/webhook-stack.yaml
      Parameters:
        Environment: !Ref Environment
        LayerCommons: !GetAtt SharedInfraStack.Outputs.LayerCommons
        DynamoTableName: !GetAtt DynamoStack.Outputs.TableName
    DeletionPolicy: Delete
    UpdateReplacePolicy: Delete

  # ================================================
  # CENTRALIZED API GATEWAY
  # ================================================

  PaymentApiStack:
    Type: AWS::Serverless::Application
    Properties:
      Location: iac/api/payment-api-stack.yaml
      Parameters:
        ApiStageName: !Ref Environment
        ClerkAuthorizerArn: !GetAtt SharedInfraStack.Outputs.ClerkAuthorizerArn
        # Payment function ARNs
        CreateCashAppPaymentFunctionArn: !GetAtt PaymentsStack.Outputs.CreateCashAppPaymentFunction
        CheckCashAppPaymentStatusFunctionArn: !GetAtt PaymentsStack.Outputs.CheckCashAppPaymentStatusFunction
        # Organization and user management function ARNs
        SubmitOrganizationApplicationFunctionArn: !GetAtt SharedInfraStack.Outputs.SubmitOrganizationApplicationFunctionArn
        ApproveApplicationFunctionArn: !GetAtt SharedInfraStack.Outputs.ApproveApplicationFunctionArn
        CreateOrganizationFunctionArn: !GetAtt SharedInfraStack.Outputs.CreateOrganizationFunctionArn
        # User management function ARNs
        ValidateUsernameFunctionArn: !GetAtt SharedInfraStack.Outputs.ValidateUsernameFunctionArn
        # KYC function ARNs
        KYCStartVerificationFunctionArn: !GetAtt KYCStack.Outputs.KYCStartFunctionArn
        KYCValidateStepFunctionArn: !GetAtt KYCStack.Outputs.KYCValidateFunctionArn
        KYCChallengeStepFunctionArn: !GetAtt KYCStack.Outputs.KYCChallengeFunctionArn
        KYCCompleteVerificationFunctionArn: !GetAtt KYCStack.Outputs.KYCCompleteFunctionArn
        KYCGetStatusFunctionArn: !GetAtt KYCStack.Outputs.KYCStatusFunctionArn
        # Webhook function ARNs
        ClerkWebhookFunctionArn: !GetAtt ClerkWebhooksStack.Outputs.WebhookHandlerFunction
        # Partner API key function ARNs
        GenerateAPIKeyFunctionArn: !GetAtt PartnerStack.Outputs.GenerateAPIKeyFunctionArn
        ListAPIKeysFunctionArn: !GetAtt PartnerStack.Outputs.ListAPIKeysFunctionArn
        RevokeAPIKeyFunctionArn: !GetAtt PartnerStack.Outputs.RevokeAPIKeyFunctionArn
        GetAPIKeyDetailsFunctionArn: !GetAtt PartnerStack.Outputs.GetAPIKeyDetailsFunctionArn
    DeletionPolicy: Delete
    UpdateReplacePolicy: Delete

Outputs:
  # ================================================
  # API ENDPOINTS
  # ================================================

  PaymentApiEndpoint:
    Description: "CLKK Payment System API Gateway endpoint URL"
    Value: !GetAtt PaymentApiStack.Outputs.ApiEndpoint
    Export:
      Name: !Sub "${Environment}-payment-api-endpoint"

  ClerkWebhookEndpoint:
    Description: "Clerk Webhook Endpoint URL"
    Value: !GetAtt ClerkWebhooksStack.Outputs.ClerkWebhookEndpoint
    Export:
      Name: !Sub "${Environment}-clerk-webhook-endpoint"

  # ================================================
  # INFRASTRUCTURE OUTPUTS
  # ================================================

  LayerCommons:
    Description: "ARN of the Commons Layer"
    Value: !GetAtt SharedInfraStack.Outputs.LayerCommons
    Export:
      Name: !Sub "${Environment}-layer-commons"

  DynamoTableName:
    Description: "Name of the CLKK Payment System DynamoDB table"
    Value: !GetAtt DynamoStack.Outputs.TableName
    Export:
      Name: !Sub "${Environment}-dynamo-table-name"

  DynamoTableArn:
    Description: "ARN of the CLKK Payment System DynamoDB table"
    Value: !GetAtt DynamoStack.Outputs.TableArn
    Export:
      Name: !Sub "${Environment}-dynamo-table-arn"

  # ================================================
  # PAYMENT FUNCTION OUTPUTS
  # ================================================

  CreateCashAppPaymentFunctionArn:
    Description: "Create CashApp Payment Lambda Function ARN"
    Value: !GetAtt PaymentsStack.Outputs.CreateCashAppPaymentFunction
    Export:
      Name: !Sub "${Environment}-create-cashapp-payment-function"

  CheckCashAppPaymentStatusFunctionArn:
    Description: "Check CashApp Payment Status Lambda Function ARN"
    Value: !GetAtt PaymentsStack.Outputs.CheckCashAppPaymentStatusFunction
    Export:
      Name: !Sub "${Environment}-check-cashapp-payment-status-function"

  # ================================================
  # ORGANIZATION MANAGEMENT OUTPUTS
  # ================================================

  SubmitOrganizationApplicationFunctionArn:
    Description: "Submit Organization Application Lambda Function ARN"
    Value: !GetAtt SharedInfraStack.Outputs.SubmitOrganizationApplicationFunctionArn
    Export:
      Name: !Sub "${Environment}-submit-org-application-function"

  CreateOrganizationFunctionArn:
    Description: "Create Organization Lambda Function ARN"
    Value: !GetAtt SharedInfraStack.Outputs.CreateOrganizationFunctionArn
    Export:
      Name: !Sub "${Environment}-create-organization-function"

  ApproveApplicationFunctionArn:
    Description: "Approve Application Lambda Function ARN"
    Value: !GetAtt SharedInfraStack.Outputs.ApproveApplicationFunctionArn
    Export:
      Name: !Sub "${Environment}-clkk-approve-application-function"

  # ================================================
  # KYC FUNCTION OUTPUTS
  # ================================================

  KYCStartFunctionArn:
    Description: "KYC Start Verification Lambda Function ARN"
    Value: !GetAtt KYCStack.Outputs.KYCStartFunctionArn
    Export:
      Name: !Sub "${Environment}-kyc-start-function"

  KYCValidateFunctionArn:
    Description: "KYC Validate Step Lambda Function ARN"
    Value: !GetAtt KYCStack.Outputs.KYCValidateFunctionArn
    Export:
      Name: !Sub "${Environment}-kyc-validate-function"

  KYCChallengeFunctionArn:
    Description: "KYC Challenge Step Lambda Function ARN"
    Value: !GetAtt KYCStack.Outputs.KYCChallengeFunctionArn
    Export:
      Name: !Sub "${Environment}-kyc-challenge-function"

  KYCCompleteFunctionArn:
    Description: "KYC Complete Verification Lambda Function ARN"
    Value: !GetAtt KYCStack.Outputs.KYCCompleteFunctionArn
    Export:
      Name: !Sub "${Environment}-kyc-complete-function"

  KYCStatusFunctionArn:
    Description: "KYC Get Status Lambda Function ARN"
    Value: !GetAtt KYCStack.Outputs.KYCStatusFunctionArn
    Export:
      Name: !Sub "${Environment}-kyc-status-function"

  KYCApiUrl:
    Description: "KYC API Gateway endpoint URL (integrated with Payment API)"
    Value: !GetAtt PaymentApiStack.Outputs.ApiEndpoint
    Export:
      Name: !Sub "${Environment}-kyc-api-endpoint"

  # ================================================
  # PARTNER API KEY MANAGEMENT OUTPUTS
  # ================================================

  GenerateAPIKeyFunctionArn:
    Description: "Generate API Key Lambda Function ARN"
    Value: !GetAtt PartnerStack.Outputs.GenerateAPIKeyFunctionArn
    Export:
      Name: !Sub "${Environment}-generate-api-key-function"

  ListAPIKeysFunctionArn:
    Description: "List API Keys Lambda Function ARN"
    Value: !GetAtt PartnerStack.Outputs.ListAPIKeysFunctionArn
    Export:
      Name: !Sub "${Environment}-list-api-keys-function"

  RevokeAPIKeyFunctionArn:
    Description: "Revoke API Key Lambda Function ARN"
    Value: !GetAtt PartnerStack.Outputs.RevokeAPIKeyFunctionArn
    Export:
      Name: !Sub "${Environment}-revoke-api-key-function"

  GetAPIKeyDetailsFunctionArn:
    Description: "Get API Key Details Lambda Function ARN"
    Value: !GetAtt PartnerStack.Outputs.GetAPIKeyDetailsFunctionArn
    Export:
      Name: !Sub "${Environment}-get-api-key-details-function"

  APIKeyAuthorizerFunctionArn:
    Description: "API Key Authorizer Lambda Function ARN"
    Value: !GetAtt PartnerStack.Outputs.APIKeyAuthorizerFunctionArn
    Export:
      Name: !Sub "${Environment}-api-key-authorizer-function"

