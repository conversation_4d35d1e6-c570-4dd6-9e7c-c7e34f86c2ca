openapi: "3.0.1"
info:
  title: "dev-clkk-payment-system-api-v2"
  description: "# CLKK Payment System API\n\nThe CLKK Payment System REST API provides\
    \ comprehensive access to the CLKK platform's payment processing,\norganization\
    \ management, and user application functionality.\n\nThis API enables you to process\
    \ CashApp payments, manage organization applications, handle user registrations,\n\
    and integrate with Clerk authentication services.\n\n## Authentication\n\nAll\
    \ API requests require authentication using a JWT token from <PERSON>. The token\
    \ should be included in the Authorization header.\n\n## Rate Limiting\n\nAPI requests\
    \ are subject to rate limiting to ensure system stability. Please implement appropriate\
    \ retry logic with exponential backoff.\n\n## Versioning\n\nThe API follows semantic\
    \ versioning. Breaking changes will be introduced in major version updates.\n"
  version: "1.0.0"
servers:
- url: "https://ysqppnk9z0.execute-api.us-east-1.amazonaws.com/{basePath}"
  variables:
    basePath:
      default: "dev"
paths:
  /payments/cashapp/status:
    post:
      operationId: "checkCashAppPaymentStatus"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CheckCashAppPaymentStatusRequest"
        required: true
      responses:
        "404":
          description: "404 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "200":
          description: "200 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CheckCashAppPaymentStatusResponse"
        "400":
          description: "400 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "500":
          description: "500 response"
          content: {}
    options:
      responses:
        "200":
          description: "200 response"
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: "string"
            Access-Control-Allow-Methods:
              schema:
                type: "string"
            Access-Control-Max-Age:
              schema:
                type: "string"
            Access-Control-Allow-Headers:
              schema:
                type: "string"
          content: {}
  /kyc/complete/{correlationId}:
    post:
      operationId: "completeKYCVerification"
      parameters:
      - name: "correlationId"
        in: "path"
        required: true
        schema:
          type: "string"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CompleteKYCVerificationRequest"
        required: true
      responses:
        "404":
          description: "404 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "200":
          description: "200 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CompleteKYCVerificationResponse"
        "400":
          description: "400 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "401 response"
          content: {}
        "500":
          description: "500 response"
          content: {}
    options:
      parameters:
      - name: "correlationId"
        in: "path"
        required: true
        schema:
          type: "string"
      responses:
        "200":
          description: "200 response"
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: "string"
            Access-Control-Allow-Methods:
              schema:
                type: "string"
            Access-Control-Max-Age:
              schema:
                type: "string"
            Access-Control-Allow-Headers:
              schema:
                type: "string"
          content: {}
  /organizations:
    post:
      operationId: "createOrganization"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrganizationRequest"
        required: true
      responses:
        "201":
          description: "201 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateOrganizationResponse"
        "400":
          description: "400 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "401 response"
          content: {}
        "500":
          description: "500 response"
          content: {}
        "403":
          description: "403 response"
          content: {}
    options:
      responses:
        "200":
          description: "200 response"
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: "string"
            Access-Control-Allow-Methods:
              schema:
                type: "string"
            Access-Control-Max-Age:
              schema:
                type: "string"
            Access-Control-Allow-Headers:
              schema:
                type: "string"
          content: {}
  /api-keys:
    get:
      operationId: "listAPIKeys"
      parameters:
      - name: "pageToken"
        in: "query"
        schema:
          type: "string"
      - name: "status"
        in: "query"
        schema:
          type: "string"
      - name: "pageSize"
        in: "query"
        schema:
          type: "string"
      - name: "type"
        in: "query"
        schema:
          type: "string"
      responses:
        "401":
          description: "401 response"
          content: {}
        "500":
          description: "500 response"
          content: {}
        "200":
          description: "200 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ListAPIKeysResponse"
    post:
      operationId: "generateAPIKey"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GenerateAPIKeyRequest"
        required: true
      responses:
        "201":
          description: "201 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GenerateAPIKeyResponse"
        "400":
          description: "400 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "401 response"
          content: {}
        "500":
          description: "500 response"
          content: {}
        "403":
          description: "403 response"
          content: {}
    options:
      responses:
        "200":
          description: "200 response"
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: "string"
            Access-Control-Allow-Methods:
              schema:
                type: "string"
            Access-Control-Max-Age:
              schema:
                type: "string"
            Access-Control-Allow-Headers:
              schema:
                type: "string"
          content: {}
  /kyc/validate/{correlationId}:
    post:
      operationId: "validateKYCStep"
      parameters:
      - name: "correlationId"
        in: "path"
        required: true
        schema:
          type: "string"
      responses:
        "404":
          description: "404 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "200":
          description: "200 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidateKYCStepResponse"
        "400":
          description: "400 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "401 response"
          content: {}
        "500":
          description: "500 response"
          content: {}
    options:
      parameters:
      - name: "correlationId"
        in: "path"
        required: true
        schema:
          type: "string"
      responses:
        "200":
          description: "200 response"
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: "string"
            Access-Control-Allow-Methods:
              schema:
                type: "string"
            Access-Control-Max-Age:
              schema:
                type: "string"
            Access-Control-Allow-Headers:
              schema:
                type: "string"
          content: {}
  /kyc/challenge/{correlationId}:
    post:
      operationId: "challengeKYCStep"
      parameters:
      - name: "correlationId"
        in: "path"
        required: true
        schema:
          type: "string"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ChallengeKYCStepRequest"
        required: true
      responses:
        "404":
          description: "404 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "200":
          description: "200 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChallengeKYCStepResponse"
        "400":
          description: "400 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "401 response"
          content: {}
        "500":
          description: "500 response"
          content: {}
    options:
      parameters:
      - name: "correlationId"
        in: "path"
        required: true
        schema:
          type: "string"
      responses:
        "200":
          description: "200 response"
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: "string"
            Access-Control-Allow-Methods:
              schema:
                type: "string"
            Access-Control-Max-Age:
              schema:
                type: "string"
            Access-Control-Allow-Headers:
              schema:
                type: "string"
          content: {}
  /applications/{applicationId}/approve:
    post:
      operationId: "approveApplication"
      parameters:
      - name: "applicationId"
        in: "path"
        required: true
        schema:
          type: "string"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ApproveApplicationRequest"
        required: true
      responses:
        "404":
          description: "404 response"
          content: {}
        "200":
          description: "200 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ApproveApplicationResponse"
        "400":
          description: "400 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "401 response"
          content: {}
        "500":
          description: "500 response"
          content: {}
        "403":
          description: "403 response"
          content: {}
    options:
      parameters:
      - name: "applicationId"
        in: "path"
        required: true
        schema:
          type: "string"
      responses:
        "200":
          description: "200 response"
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: "string"
            Access-Control-Allow-Methods:
              schema:
                type: "string"
            Access-Control-Max-Age:
              schema:
                type: "string"
            Access-Control-Allow-Headers:
              schema:
                type: "string"
          content: {}
  /api-keys/{keyId}:
    get:
      operationId: "getAPIKeyDetails"
      parameters:
      - name: "keyId"
        in: "path"
        required: true
        schema:
          type: "string"
      responses:
        "404":
          description: "404 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "200":
          description: "200 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAPIKeyDetailsResponse"
        "401":
          description: "401 response"
          content: {}
        "500":
          description: "500 response"
          content: {}
    delete:
      operationId: "revokeAPIKey"
      parameters:
      - name: "keyId"
        in: "path"
        required: true
        schema:
          type: "string"
      responses:
        "404":
          description: "404 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "200":
          description: "200 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RevokeAPIKeyResponse"
        "401":
          description: "401 response"
          content: {}
        "500":
          description: "500 response"
          content: {}
    options:
      parameters:
      - name: "keyId"
        in: "path"
        required: true
        schema:
          type: "string"
      responses:
        "200":
          description: "200 response"
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: "string"
            Access-Control-Allow-Methods:
              schema:
                type: "string"
            Access-Control-Max-Age:
              schema:
                type: "string"
            Access-Control-Allow-Headers:
              schema:
                type: "string"
          content: {}
  /users/validate-username:
    get:
      operationId: "validateUsernameGet"
      parameters:
      - name: "username"
        in: "query"
        required: true
        schema:
          type: "string"
      responses:
        "400":
          description: "400 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "500":
          description: "500 response"
          content: {}
        "200":
          description: "200 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidateUsernameResponse"
    post:
      operationId: "validateUsernamePost"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ValidateUsernameRequest"
        required: true
      responses:
        "400":
          description: "400 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "500":
          description: "500 response"
          content: {}
        "200":
          description: "200 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidateUsernameResponse"
    options:
      responses:
        "200":
          description: "200 response"
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: "string"
            Access-Control-Allow-Methods:
              schema:
                type: "string"
            Access-Control-Max-Age:
              schema:
                type: "string"
            Access-Control-Allow-Headers:
              schema:
                type: "string"
          content: {}
  /kyc/start:
    post:
      operationId: "startKYCVerification"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/StartKYCVerificationRequest"
        required: true
      responses:
        "201":
          description: "201 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StartKYCVerificationResponse"
        "400":
          description: "400 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "401 response"
          content: {}
        "500":
          description: "500 response"
          content: {}
        "409":
          description: "409 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
    options:
      responses:
        "200":
          description: "200 response"
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: "string"
            Access-Control-Allow-Methods:
              schema:
                type: "string"
            Access-Control-Max-Age:
              schema:
                type: "string"
            Access-Control-Allow-Headers:
              schema:
                type: "string"
          content: {}
  /webhooks/clerk:
    post:
      operationId: "clerkWebhook"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ClerkWebhookRequest"
        required: true
      responses:
        "200":
          description: "200 response"
          content: {}
        "400":
          description: "400 response"
          content: {}
        "401":
          description: "401 response"
          content: {}
        "500":
          description: "500 response"
          content: {}
    options:
      responses:
        "200":
          description: "200 response"
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: "string"
            Access-Control-Allow-Methods:
              schema:
                type: "string"
            Access-Control-Max-Age:
              schema:
                type: "string"
            Access-Control-Allow-Headers:
              schema:
                type: "string"
          content: {}
  /payments/cashapp:
    post:
      operationId: "createCashAppPayment"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateCashAppPaymentRequest"
        required: true
      responses:
        "201":
          description: "201 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateCashAppPaymentResponse"
        "400":
          description: "400 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "401 response"
          content: {}
        "500":
          description: "500 response"
          content: {}
    options:
      responses:
        "200":
          description: "200 response"
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: "string"
            Access-Control-Allow-Methods:
              schema:
                type: "string"
            Access-Control-Max-Age:
              schema:
                type: "string"
            Access-Control-Allow-Headers:
              schema:
                type: "string"
          content: {}
  /applications:
    post:
      operationId: "submitApplication"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SubmitApplicationRequest"
        required: true
      responses:
        "201":
          description: "201 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SubmitApplicationResponse"
        "400":
          description: "400 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "401 response"
          content: {}
        "500":
          description: "500 response"
          content: {}
        "409":
          description: "409 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
    options:
      responses:
        "200":
          description: "200 response"
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: "string"
            Access-Control-Allow-Methods:
              schema:
                type: "string"
            Access-Control-Max-Age:
              schema:
                type: "string"
            Access-Control-Allow-Headers:
              schema:
                type: "string"
          content: {}
  /kyc/status/{verificationId}:
    get:
      operationId: "getKYCStatus"
      parameters:
      - name: "verificationId"
        in: "path"
        required: true
        schema:
          type: "string"
      responses:
        "404":
          description: "404 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "200":
          description: "200 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetKYCStatusResponse"
        "400":
          description: "400 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ValidationError"
        "401":
          description: "401 response"
          content: {}
        "500":
          description: "500 response"
          content: {}
    options:
      parameters:
      - name: "verificationId"
        in: "path"
        required: true
        schema:
          type: "string"
      responses:
        "200":
          description: "200 response"
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: "string"
            Access-Control-Allow-Methods:
              schema:
                type: "string"
            Access-Control-Max-Age:
              schema:
                type: "string"
            Access-Control-Allow-Headers:
              schema:
                type: "string"
          content: {}
  /kyc/user/status:
    get:
      operationId: "getUserKYCStatus"
      responses:
        "401":
          description: "401 response"
          content: {}
        "500":
          description: "500 response"
          content: {}
        "200":
          description: "200 response"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetKYCStatusResponse"
    options:
      responses:
        "200":
          description: "200 response"
          headers:
            Access-Control-Allow-Origin:
              schema:
                type: "string"
            Access-Control-Allow-Methods:
              schema:
                type: "string"
            Access-Control-Max-Age:
              schema:
                type: "string"
            Access-Control-Allow-Headers:
              schema:
                type: "string"
          content: {}
components:
  schemas:
    KYCIndividual:
      title: "KYCIndividual"
      required:
      - "dateOfBirth"
      - "firstName"
      - "lastName"
      - "phoneNumber"
      type: "object"
      properties:
        firstName:
          type: "string"
          description: "First name"
        lastName:
          type: "string"
          description: "Last name"
        addresses:
          type: "array"
          description: "Physical addresses"
          items:
            $ref: "#/components/schemas/KYCAddress"
        phoneNumber:
          type: "string"
          description: "Phone number with country code"
        emailAddresses:
          type: "array"
          description: "Email addresses"
          items:
            type: "string"
            format: "email"
        dateOfBirth:
          pattern: "^\\d{4}-\\d{2}-\\d{2}$"
          type: "string"
          description: "Date of birth in YYYY-MM-DD format"
        ssn:
          type: "string"
          description: "Social Security Number (last 4 digits for security)"
    GenerateAPIKeyResponse:
      title: "GenerateAPIKeyResponse"
      type: "object"
      properties:
        createdAt:
          type: "string"
          description: "When the API key was created"
          format: "date-time"
        apiKey:
          type: "string"
          description: "The actual API key (only shown once)"
        name:
          type: "string"
          description: "Name of the API key"
        keyId:
          type: "string"
          description: "Unique identifier for the API key"
        type:
          type: "string"
          description: "Type of API key"
          enum:
          - "TEST"
          - "LIVE"
        expiresAt:
          type: "string"
          description: "When the API key expires (if applicable)"
          format: "date-time"
    ChallengeKYCStepResponse:
      title: "ChallengeKYCStepResponse"
      type: "object"
      properties:
        challengeQuestions:
          type: "array"
          description: "Challenge questions (if any)"
          items:
            type: "string"
        success:
          type: "boolean"
          description: "Whether the challenge step was successful"
        nextStep:
          type: "string"
          description: "Next step in the verification process"
        correlationId:
          type: "string"
          description: "Provider correlation ID"
        message:
          type: "string"
          description: "Human-readable message about the challenge"
        verificationId:
          type: "string"
          description: "Verification ID"
    CheckCashAppPaymentStatusRequest:
      title: "CheckCashAppPaymentStatusRequest"
      required:
      - "paymentId"
      type: "object"
      properties:
        paymentId:
          type: "string"
          description: "Unique identifier for the payment to check"
    RevokeAPIKeyResponse:
      title: "RevokeAPIKeyResponse"
      type: "object"
      properties:
        keyId:
          type: "string"
          description: "ID of the revoked API key"
        revokedAt:
          type: "string"
          description: "When the API key was revoked"
          format: "date-time"
        status:
          type: "string"
          description: "New status of the API key"
          enum:
          - "REVOKED"
    ValidateUsernameResponse:
      title: "ValidateUsernameResponse"
      type: "object"
      properties:
        isAvailable:
          type: "boolean"
          description: "Whether the username is available for use"
        isValid:
          type: "boolean"
          description: "Whether the username is valid and available"
        validationErrors:
          type: "array"
          description: "List of validation errors (if any)"
          items:
            type: "string"
        suggestions:
          type: "array"
          description: "Alternative username suggestions (if username is taken)"
          items:
            type: "string"
        username:
          type: "string"
          description: "The username that was validated"
    GenerateAPIKeyRequest:
      title: "GenerateAPIKeyRequest"
      required:
      - "name"
      - "type"
      type: "object"
      properties:
        metadata:
          type: "object"
          additionalProperties: true
          description: "Additional metadata for the API key"
        rateLimit:
          type: "object"
          properties:
            requestsPerMinute:
              maximum: 10000
              minimum: 1
              type: "integer"
              description: "Maximum requests per minute"
              format: "int32"
            requestsPerHour:
              maximum: 100000
              minimum: 1
              type: "integer"
              description: "Maximum requests per hour"
              format: "int32"
          description: "Rate limit configuration for this API key"
        permissions:
          type: "array"
          description: "List of permissions for this API key"
          items:
            type: "string"
        name:
          maxLength: 100
          minLength: 3
          type: "string"
          description: "Name/description for the API key"
        type:
          type: "string"
          description: "Type of API key"
          enum:
          - "TEST"
          - "LIVE"
        expiresAt:
          type: "string"
          description: "Optional expiration date for the API key"
          format: "date-time"
    SubmitApplicationResponse:
      title: "SubmitApplicationResponse"
      type: "object"
      properties:
        applicationType:
          type: "string"
          description: "Type of application"
          enum:
          - "org_admin"
          - "agent"
        submittedAt:
          type: "string"
          description: "When the application was submitted"
          format: "date-time"
        applicationId:
          type: "string"
          description: "Unique identifier for the submitted application"
        username:
          type: "string"
          description: "Requested username"
        status:
          type: "string"
          description: "Current application status"
          enum:
          - "PENDING"
          - "APPROVED"
          - "REJECTED"
    CreateCashAppPaymentRequest:
      title: "CreateCashAppPaymentRequest"
      required:
      - "amount"
      - "currency"
      - "redirectUrl"
      type: "object"
      properties:
        amount:
          minimum: 0.01
          type: "number"
          description: "Payment amount in the specified currency"
        metadata:
          type: "object"
          additionalProperties: true
          description: "Additional metadata for the payment"
        redirectUrl:
          type: "string"
          description: "URL to redirect user after payment completion"
          format: "uri"
        description:
          maxLength: 255
          type: "string"
          description: "Description of the payment"
        currency:
          type: "string"
          description: "Currency code (ISO 4217)"
          enum:
          - "USD"
          default: "USD"
    ListAPIKeysResponse:
      title: "ListAPIKeysResponse"
      type: "object"
      properties:
        keys:
          type: "array"
          description: "List of API keys"
          items:
            type: "object"
            properties:
              createdAt:
                type: "string"
                description: "When the API key was created"
                format: "date-time"
              lastUsedAt:
                type: "string"
                description: "When the API key was last used"
                format: "date-time"
              name:
                type: "string"
                description: "Name of the API key"
              keyId:
                type: "string"
                description: "Unique identifier for the API key"
              type:
                type: "string"
                description: "Type of API key"
                enum:
                - "TEST"
                - "LIVE"
              expiresAt:
                type: "string"
                description: "When the API key expires (if applicable)"
                format: "date-time"
              status:
                type: "string"
                description: "Current status of the API key"
                enum:
                - "ACTIVE"
                - "REVOKED"
                - "EXPIRED"
        nextPageToken:
          type: "string"
          description: "Token for pagination"
        totalCount:
          type: "integer"
          description: "Total number of API keys"
          format: "int32"
    SubmitApplicationRequest:
      title: "SubmitApplicationRequest"
      required:
      - "applicationType"
      - "username"
      type: "object"
      properties:
        applicationType:
          type: "string"
          description: "Type of application being submitted"
          enum:
          - "org_admin"
          - "agent"
        organizationName:
          type: "string"
          description: "Name of the organization (required for org_admin applications)"
        contactInfo:
          type: "object"
          properties:
            address:
              type: "string"
              description: "Business address"
            phone:
              type: "string"
              description: "Contact phone number"
            email:
              type: "string"
              description: "Contact email"
              format: "email"
          description: "Contact information"
        affiliateUsername:
          type: "string"
          description: "Username of the affiliate/organization admin (required for\
            \ agent applications)"
        businessDetails:
          type: "object"
          properties:
            website:
              type: "string"
              description: "Business website"
              format: "uri"
            taxId:
              type: "string"
              description: "Tax identification number"
            registrationNumber:
              type: "string"
              description: "Business registration number"
            description:
              type: "string"
              description: "Business description"
            businessType:
              type: "string"
              description: "Type of business"
          description: "Business details (for org_admin applications)"
        username:
          pattern: "^[a-zA-Z0-9_]{3,30}$"
          type: "string"
          description: "Desired username for the applicant"
    KYCAddress:
      title: "KYCAddress"
      required:
      - "city"
      - "country"
      - "postalCode"
      - "region"
      - "street"
      type: "object"
      properties:
        country:
          type: "string"
          description: "Country code (ISO 3166-1 alpha-2)"
        city:
          type: "string"
          description: "City"
        street:
          type: "string"
          description: "Street address"
        postalCode:
          type: "string"
          description: "Postal or ZIP code"
        region:
          type: "string"
          description: "State or region"
    StartKYCVerificationResponse:
      title: "StartKYCVerificationResponse"
      type: "object"
      properties:
        createdAt:
          type: "string"
          description: "When the verification was created"
          format: "date-time"
        provider:
          type: "string"
          description: "KYC provider being used"
        verificationLevel:
          type: "string"
          description: "Level of verification"
        nextStep:
          type: "string"
          description: "Next step in the verification process"
        correlationId:
          type: "string"
          description: "Provider correlation ID for tracking"
        message:
          type: "string"
          description: "Human-readable message about the verification"
        verificationId:
          type: "string"
          description: "Unique identifier for the verification"
        status:
          type: "string"
          description: "Current verification status"
          enum:
          - "NOT_STARTED"
          - "IN_PROGRESS"
          - "VERIFIED"
          - "FAILED"
          - "EXPIRED"
    ValidateKYCStepResponse:
      title: "ValidateKYCStepResponse"
      type: "object"
      properties:
        isValid:
          type: "boolean"
          description: "Whether the validation step was successful"
        nextStep:
          type: "string"
          description: "Next step in the verification process"
        correlationId:
          type: "string"
          description: "Provider correlation ID"
        message:
          type: "string"
          description: "Human-readable message about the validation"
        verificationId:
          type: "string"
          description: "Verification ID"
        status:
          type: "string"
          description: "Current verification status"
    ApproveApplicationRequest:
      title: "ApproveApplicationRequest"
      required:
      - "applicationId"
      - "approved"
      type: "object"
      properties:
        adminNotes:
          type: "string"
          description: "Administrative notes regarding the approval or rejection"
        approved:
          type: "boolean"
          description: "Set to true to approve the application, false to reject"
        applicationId:
          type: "string"
          description: "ID of the application to approve or reject"
        rejectionReason:
          type: "string"
          description: "Reason for rejection, required if 'approved' is false"
    ClerkWebhookRequest:
      title: "ClerkWebhookRequest"
      type: "object"
      properties:
        data:
          type: "object"
          additionalProperties: true
          description: "The data of the webhook event"
        created_at:
          type: "integer"
          description: "Unix timestamp when the event was created"
          format: "int64"
        type:
          type: "string"
          description: "The type of event"
        object:
          type: "string"
          description: "The type of object that triggered the event"
    ValidationError:
      title: "ValidationError"
      required:
      - "code"
      - "message"
      - "validationErrors"
      type: "object"
      properties:
        code:
          type: "string"
          description: "Error code for validation errors"
          enum:
          - "VALIDATION_ERROR"
        validationErrors:
          type: "array"
          description: "List of validation errors"
          items:
            type: "object"
            properties:
              code:
                type: "string"
                description: "Specific validation error code"
              field:
                type: "string"
                description: "The field that failed validation"
              message:
                type: "string"
                description: "The validation error message"
        message:
          type: "string"
          description: "Human-readable error message"
    CreateCashAppPaymentResponse:
      title: "CreateCashAppPaymentResponse"
      type: "object"
      properties:
        createdAt:
          type: "string"
          description: "When the payment was created"
          format: "date-time"
        amount:
          type: "number"
          description: "Payment amount"
        paymentId:
          type: "string"
          description: "Unique identifier for the payment"
        currency:
          type: "string"
          description: "Currency code"
        paymentUrl:
          type: "string"
          description: "URL to redirect user for payment"
          format: "uri"
        expiresAt:
          type: "string"
          description: "When the payment link expires"
          format: "date-time"
        status:
          type: "string"
          description: "Current payment status"
          enum:
          - "PENDING"
          - "COMPLETED"
          - "FAILED"
          - "CANCELLED"
    CheckCashAppPaymentStatusResponse:
      title: "CheckCashAppPaymentStatusResponse"
      type: "object"
      properties:
        completedAt:
          type: "string"
          description: "When the payment was completed (if applicable)"
          format: "date-time"
        amount:
          type: "number"
          description: "Payment amount"
        paymentId:
          type: "string"
          description: "Unique identifier for the payment"
        failureReason:
          type: "string"
          description: "Reason for failure (if applicable)"
        currency:
          type: "string"
          description: "Currency code"
        status:
          type: "string"
          description: "Current payment status"
          enum:
          - "PENDING"
          - "COMPLETED"
          - "FAILED"
          - "CANCELLED"
    ApproveApplicationResponse:
      title: "ApproveApplicationResponse"
      type: "object"
      properties:
        organizationId:
          type: "string"
          description: "ID of the created organization (for approved org_admin applications)"
        clerkOrganizationId:
          type: "string"
          description: "Clerk organization ID (for approved applications)"
        processedAt:
          type: "string"
          description: "When the application was processed"
          format: "date-time"
        applicationId:
          type: "string"
          description: "ID of the processed application"
        status:
          type: "string"
          description: "New status of the application"
          enum:
          - "APPROVED"
          - "REJECTED"
    CompleteKYCVerificationRequest:
      title: "CompleteKYCVerificationRequest"
      required:
      - "individual"
      type: "object"
      properties:
        individual:
          $ref: "#/components/schemas/KYCIndividual"
    GetKYCStatusResponse:
      title: "GetKYCStatusResponse"
      type: "object"
      properties:
        canRetry:
          type: "boolean"
          description: "Whether the user can retry verification"
        createdAt:
          type: "string"
          description: "When the verification was created"
          format: "date-time"
        metadata:
          type: "object"
          properties:
            verificationResult:
              type: "string"
              description: "Verification result"
            completedAt:
              type: "string"
              description: "When verification was completed"
              format: "date-time"
            challengeCompleted:
              type: "boolean"
              description: "Whether challenge step was completed"
            maxRetries:
              type: "integer"
              description: "Maximum allowed retries"
              format: "int32"
            retryCount:
              type: "integer"
              description: "Number of retry attempts (if applicable)"
              format: "int32"
            canRetryAfter:
              type: "string"
              description: "When retry is allowed (if applicable)"
              format: "date-time"
            hasIndividualData:
              type: "boolean"
              description: "Whether individual data is stored"
            correlationId:
              type: "string"
              description: "Provider correlation ID"
          description: "Verification metadata (filtered for security)"
        provider:
          type: "string"
          description: "KYC provider used"
        verificationLevel:
          type: "string"
          description: "Level of verification"
        hasVerification:
          type: "boolean"
          description: "Whether the user has any verification"
        verificationId:
          type: "string"
          description: "Verification ID (if exists)"
        status:
          type: "string"
          description: "Current verification status"
          enum:
          - "NOT_STARTED"
          - "IN_PROGRESS"
          - "VERIFIED"
          - "FAILED"
          - "EXPIRED"
        updatedAt:
          type: "string"
          description: "When the verification was last updated"
          format: "date-time"
    ChallengeKYCStepRequest:
      title: "ChallengeKYCStepRequest"
      type: "object"
      properties:
        last4SSN:
          pattern: "^\\d{4}$"
          type: "string"
          description: "Last 4 digits of Social Security Number"
        dateOfBirth:
          pattern: "^\\d{4}-\\d{2}-\\d{2}$"
          type: "string"
          description: "Date of birth in YYYY-MM-DD format"
        challengeAnswers:
          type: "array"
          description: "Answers to challenge questions"
          items:
            type: "string"
    CompleteKYCVerificationResponse:
      title: "CompleteKYCVerificationResponse"
      type: "object"
      properties:
        verificationResult:
          type: "string"
          description: "Detailed verification result"
        score:
          maximum: 100
          minimum: 0
          type: "number"
          description: "Verification confidence score (0-100)"
        completedAt:
          type: "string"
          description: "When the verification was completed"
          format: "date-time"
        individual:
          $ref: "#/components/schemas/KYCIndividual"
        success:
          type: "boolean"
          description: "Whether the verification was successful"
        correlationId:
          type: "string"
          description: "Provider correlation ID"
        message:
          type: "string"
          description: "Human-readable message about the completion"
        verificationId:
          type: "string"
          description: "Verification ID"
        status:
          type: "string"
          description: "Final verification status"
          enum:
          - "VERIFIED"
          - "FAILED"
    StartKYCVerificationRequest:
      title: "StartKYCVerificationRequest"
      required:
      - "individual"
      - "provider"
      - "verificationLevel"
      type: "object"
      properties:
        metadata:
          type: "object"
          additionalProperties: true
          description: "Additional metadata for the verification"
        individual:
          $ref: "#/components/schemas/KYCIndividual"
        provider:
          type: "string"
          description: "KYC provider to use for verification"
          enum:
          - "PROVE"
          - "JUMIO"
          - "ONFIDO"
          - "MANUAL"
        verificationLevel:
          type: "string"
          description: "Level of verification required"
          enum:
          - "BASIC"
          - "ENHANCED"
          - "PREMIUM"
    CreateOrganizationResponse:
      title: "CreateOrganizationResponse"
      type: "object"
      properties:
        createdAt:
          type: "string"
          description: "When the organization was created"
          format: "date-time"
        name:
          type: "string"
          description: "Organization name"
        clerkId:
          type: "string"
          description: "Clerk organization ID"
        id:
          type: "string"
          description: "Unique identifier for the organization"
        type:
          type: "string"
          description: "Organization type"
        status:
          type: "string"
          description: "Organization status"
          enum:
          - "ACTIVE"
          - "INACTIVE"
          - "PENDING"
          - "SUSPENDED"
    ErrorResponse:
      title: "ErrorResponse"
      required:
      - "code"
      - "message"
      type: "object"
      properties:
        code:
          type: "string"
          description: "Error code that can be used to programmatically identify the\
            \ error"
        details:
          type: "object"
          additionalProperties: true
          description: "Additional details about the error, if available"
        message:
          type: "string"
          description: "Human-readable error message"
    GetAPIKeyDetailsResponse:
      title: "GetAPIKeyDetailsResponse"
      type: "object"
      properties:
        createdAt:
          type: "string"
          description: "When the API key was created"
          format: "date-time"
        metadata:
          type: "object"
          additionalProperties: true
          description: "Additional metadata for the API key"
        rateLimit:
          type: "object"
          properties:
            requestsPerMinute:
              type: "integer"
              format: "int32"
            requestsPerHour:
              type: "integer"
              format: "int32"
          description: "Rate limit configuration"
        permissions:
          type: "array"
          description: "List of permissions for this API key"
          items:
            type: "string"
        usage:
          type: "object"
          properties:
            requestsToday:
              type: "integer"
              description: "Number of requests made today"
              format: "int32"
            lastUsedAt:
              type: "string"
              description: "When the API key was last used"
              format: "date-time"
            totalRequests:
              type: "integer"
              description: "Total number of requests made"
              format: "int32"
            requestsThisMonth:
              type: "integer"
              description: "Number of requests made this month"
              format: "int32"
          description: "Usage statistics for the API key"
        name:
          type: "string"
          description: "Name of the API key"
        keyId:
          type: "string"
          description: "Unique identifier for the API key"
        type:
          type: "string"
          description: "Type of API key"
          enum:
          - "TEST"
          - "LIVE"
        expiresAt:
          type: "string"
          description: "When the API key expires (if applicable)"
          format: "date-time"
        status:
          type: "string"
          description: "Current status of the API key"
          enum:
          - "ACTIVE"
          - "REVOKED"
          - "EXPIRED"
    CreateOrganizationRequest:
      title: "CreateOrganizationRequest"
      required:
      - "name"
      - "type"
      type: "object"
      properties:
        website:
          type: "string"
          description: "Organization website"
          format: "uri"
        metadata:
          type: "object"
          additionalProperties: true
          description: "Additional metadata"
        contactInfo:
          type: "object"
          properties:
            address:
              type: "string"
              description: "Organization address"
            phone:
              type: "string"
              description: "Primary contact phone"
            email:
              type: "string"
              description: "Primary contact email"
              format: "email"
          description: "Contact information for the organization"
        name:
          type: "string"
          description: "Name of the organization"
        description:
          type: "string"
          description: "Description of the organization"
        type:
          type: "string"
          description: "Type of organization"
          enum:
          - "PAYMENT_PROCESSOR"
          - "MERCHANT"
          - "AFFILIATE"
    ValidateUsernameRequest:
      title: "ValidateUsernameRequest"
      required:
      - "username"
      type: "object"
      properties:
        username:
          pattern: "^[a-zA-Z0-9_]{3,30}$"
          type: "string"
          description: "Username to validate"
