# API Key Management System

## Overview

This document describes the API Key Management system implemented for the CLKK SaaS backend. The system provides secure generation, validation, and management of API keys for partner organizations.

## Architecture

### Phase 1: Database Schema (Implemented ✅)

1. **APIKey Entity** (`layers/commons/data/entities/api-key.ts`)
   - Stores API key metadata with hashed key values
   - Supports key types: LIVE and TEST
   - Includes rate limiting, permissions, and expiration
   - Uses GSI for efficient lookups by hash and organization

2. **APIKeyAudit Entity** (`layers/commons/data/entities/api-key-audit.ts`)
   - Tracks all API key operations
   - Provides audit trail for security and compliance
   - Stores actions: CREATED, REVOKED, UPDATED, ACCESSED, FAILED_VALIDATION

### Phase 2: Core Service (Implemented ✅)

**APIKeyService** (`layers/commons/services/api-key-service.ts`)
- `generateAPIKey()`: Creates new API keys with cryptographic security
- `validateAPIKey()`: Validates keys and checks permissions
- `listAPIKeys()`: Lists keys for an organization
- `revokeAPIKey()`: Revokes compromised or unused keys
- `updateAPIKeyLabel()`: Updates key metadata
- `getAPIKeyDetails()`: Retrieves detailed key information
- `getAPIKeyAuditLogs()`: Fetches audit history

### Phase 3: Lambda Functions (Implemented ✅)

1. **Generate Key** (`lambdas/api-keys/generate-key/`)
   - POST /api/v1/api-keys
   - Creates new API keys with specified permissions

2. **List Keys** (`lambdas/api-keys/list-keys/`)
   - GET /api/v1/api-keys
   - Lists all keys for the authenticated organization

3. **Revoke Key** (`lambdas/api-keys/revoke-key/`)
   - DELETE /api/v1/api-keys/{keyId}
   - Revokes a specific API key

4. **Get Key Details** (`lambdas/api-keys/get-key-details/`)
   - GET /api/v1/api-keys/{keyId}
   - Retrieves detailed information and audit logs

## API Key Format

```
ckpl_<32-byte-base64url-encoded-random-value>
```

Example: `ckpl_1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p`

## Security Features

1. **Key Storage**
   - Keys are hashed using SHA-256 before storage
   - Original key value is never stored
   - Key is shown only once during generation

2. **Validation**
   - Format validation
   - Status checks (ACTIVE, REVOKED, EXPIRED, SUSPENDED)
   - Expiration date validation
   - Permission-based endpoint access control
   - IP whitelist support

3. **Rate Limiting**
   - Configurable per-key rate limits
   - Default limits: 10/sec, 100/min, 1000/hour, 10000/day
   - Ready for Redis/ElastiCache integration

4. **Audit Trail**
   - All key operations are logged
   - Failed validation attempts tracked
   - IP addresses and user agents recorded

## Usage Examples

### Generate API Key

```bash
POST /api/v1/api-keys
Authorization: Bearer <user-token>

{
  "type": "LIVE",
  "label": "Production API Key",
  "expiresAt": "2025-12-31T23:59:59Z",
  "rateLimit": {
    "requestsPerSecond": 20,
    "requestsPerMinute": 200
  },
  "permissions": {
    "allowedEndpoints": ["/api/v1/payments/*"],
    "allowedMethods": ["GET", "POST"],
    "ipWhitelist": ["***********/24"]
  }
}

Response:
{
  "success": true,
  "data": {
    "keyId": "key_123456",
    "keySecret": "ckpl_1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p",
    "keyPrefix": "ckpl_1a2b3c4d",
    "type": "LIVE",
    "label": "Production API Key",
    "createdAt": "2024-01-15T10:00:00Z",
    "expiresAt": "2025-12-31T23:59:59Z"
  }
}
```

### List API Keys

```bash
GET /api/v1/api-keys?status=ACTIVE
Authorization: Bearer <user-token>

Response:
{
  "success": true,
  "data": {
    "keys": [
      {
        "keyId": "key_123456",
        "keyPrefix": "ckpl_1a2b3c4d",
        "label": "Production API Key",
        "type": "LIVE",
        "status": "ACTIVE",
        "createdAt": "2024-01-15T10:00:00Z",
        "lastUsedAt": "2024-01-15T11:30:00Z",
        "usageCount": 150
      }
    ],
    "count": 1
  }
}
```

### Validate API Key (Internal Service Call)

```typescript
const result = await apiKeyService.validateAPIKey({
  apiKey: "ckpl_1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p",
  endpoint: "/api/v1/payments/create",
  method: "POST",
  ip: "*************",
  userAgent: "MyApp/1.0"
});

if (result.isValid) {
  // Process request with result.organizationId, result.tenantId
} else {
  // Handle error: result.errorCode, result.errorMessage
}
```

## Testing

### Unit Tests
- Service tests: `layers/commons/services/__tests__/api-key-service.test.ts`
- Lambda tests: `lambdas/api-keys/{function-name}/__tests__/index.test.ts`

### Running Tests
```bash
cd clkk-saas-backend
npm test
```

## Next Steps (Phase 4-6)

### Phase 4: API Gateway Integration
- Custom authorizer Lambda for API key validation
- Integration with existing authentication flow

### Phase 5: Partner Dashboard
- Frontend components for key management
- Secure display of generated keys
- Key lifecycle management UI

### Phase 6: Monitoring & Analytics
- CloudWatch metrics for API usage
- Rate limit monitoring
- Security alerts for suspicious activity

## Configuration

### Environment Variables
```
TABLE_NAME=YourDynamoDBTableName
AWS_REGION=us-east-1
```

### SAM Template Updates Required
Add the following Lambda functions to your `template.yaml`:

```yaml
GenerateAPIKeyFunction:
  Type: AWS::Serverless::Function
  Properties:
    CodeUri: lambdas/api-keys/generate-key/
    Handler: index.handler
    Runtime: nodejs18.x
    Layers:
      - !Ref CommonsLayer
    Environment:
      Variables:
        TABLE_NAME: !Ref DynamoDBTable

ListAPIKeysFunction:
  Type: AWS::Serverless::Function
  Properties:
    CodeUri: lambdas/api-keys/list-keys/
    Handler: index.handler
    Runtime: nodejs18.x
    Layers:
      - !Ref CommonsLayer
    Environment:
      Variables:
        TABLE_NAME: !Ref DynamoDBTable

RevokeAPIKeyFunction:
  Type: AWS::Serverless::Function
  Properties:
    CodeUri: lambdas/api-keys/revoke-key/
    Handler: index.handler
    Runtime: nodejs18.x
    Layers:
      - !Ref CommonsLayer
    Environment:
      Variables:
        TABLE_NAME: !Ref DynamoDBTable

GetAPIKeyDetailsFunction:
  Type: AWS::Serverless::Function
  Properties:
    CodeUri: lambdas/api-keys/get-key-details/
    Handler: index.handler
    Runtime: nodejs18.x
    Layers:
      - !Ref CommonsLayer
    Environment:
      Variables:
        TABLE_NAME: !Ref DynamoDBTable
```

### API Gateway Routes
Add these routes to your API Gateway configuration:

```yaml
/api/v1/api-keys:
  post:
    x-amazon-apigateway-integration:
      uri: !Sub arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${GenerateAPIKeyFunction.Arn}/invocations
  get:
    x-amazon-apigateway-integration:
      uri: !Sub arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ListAPIKeysFunction.Arn}/invocations

/api/v1/api-keys/{keyId}:
  delete:
    x-amazon-apigateway-integration:
      uri: !Sub arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${RevokeAPIKeyFunction.Arn}/invocations
  get:
    x-amazon-apigateway-integration:
      uri: !Sub arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${GetAPIKeyDetailsFunction.Arn}/invocations
```

## Best Practices

1. **Key Rotation**: Encourage partners to rotate keys periodically
2. **Least Privilege**: Grant minimum required permissions
3. **Monitoring**: Set up alerts for unusual API usage patterns
4. **Documentation**: Keep API documentation up-to-date
5. **Testing**: Test key validation in both success and failure scenarios

## Support

For questions or issues with the API key management system, please contact the development team. 