#!/bin/bash

# C<PERSON><PERSON><PERSON> SAAS KYC System Deployment Script
# This script deploys the KYC verification system as part of the main CLKK backend

set -e

# Configuration
ENVIRONMENT=${1:-dev}
STACK_NAME="clkk-saas-backend-${ENVIRONMENT}"
REGION=${AWS_REGION:-us-east-1}

# Prove API Configuration (should be set as environment variables)
PROVE_CLIENT_ID=${PROVE_CLIENT_ID:-"default"}
PROVE_CLIENT_SECRET=${PROVE_CLIENT_SECRET:-"default"}
PROVE_ENVIRONMENT=${PROVE_ENVIRONMENT:-"SANDBOX"}

# Clerk Configuration
CLERK_API_KEY=${CLERK_API_KEY:-""}
CLERK_WEBHOOK_SECRET=${CLERK_WEBHOOK_SECRET:-""}

# Payment Provider Configuration
POCKET_KNIGHTS_API_TOKEN=${POCKET_KNIGHTS_API_TOKEN:-"default"}
CASHAPP_MERCHANT_NO=${CASHAPP_MERCHANT_NO:-"default"}

echo "🚀 Deploying CLKK SAAS Backend with KYC System"
echo "Environment: ${ENVIRONMENT}"
echo "Stack Name: ${STACK_NAME}"
echo "Region: ${REGION}"
echo "Prove Environment: ${PROVE_ENVIRONMENT}"

# Validate required environment variables
if [ -z "$CLERK_API_KEY" ]; then
    echo "❌ Error: CLERK_API_KEY environment variable is required"
    exit 1
fi

if [ -z "$CLERK_WEBHOOK_SECRET" ]; then
    echo "❌ Error: CLERK_WEBHOOK_SECRET environment variable is required"
    exit 1
fi

# Build the commons layer
echo "📦 Building commons layer..."
cd layers/commons
npm install
npm run build
cd ../..

# Build KYC Lambda functions
echo "📦 Building KYC Lambda functions..."

kyc_functions=("start-verification" "validate-step" "challenge-step" "complete-verification" "get-status")

for func in "${kyc_functions[@]}"; do
    echo "Building KYC ${func}..."
    cd "lambdas/kyc/${func}"
    npm install
    npm run build
    cd ../../..
done

# Deploy using SAM (main template with KYC integration)
echo "🚀 Deploying CLKK SAAS Backend with KYC integration..."

sam build --template-file template.yaml

sam deploy \
    --template-file template.yaml \
    --stack-name "${STACK_NAME}" \
    --region "${REGION}" \
    --capabilities CAPABILITY_IAM \
    --parameter-overrides \
        Environment="${ENVIRONMENT}" \
        ClerkApiKey="${CLERK_API_KEY}" \
        ClerkWebhookSecret="${CLERK_WEBHOOK_SECRET}" \
        PocketKnightsApiToken="${POCKET_KNIGHTS_API_TOKEN}" \
        CashAppMerchantNo="${CASHAPP_MERCHANT_NO}" \
        ProveClientId="${PROVE_CLIENT_ID}" \
        ProveClientSecret="${PROVE_CLIENT_SECRET}" \
        ProveEnvironment="${PROVE_ENVIRONMENT}" \
    --confirm-changeset \
    --resolve-s3

# Get outputs
echo "📋 Deployment outputs:"
aws cloudformation describe-stacks \
    --stack-name "${STACK_NAME}" \
    --region "${REGION}" \
    --query 'Stacks[0].Outputs[*].[OutputKey,OutputValue]' \
    --output table

echo "✅ CLKK SAAS Backend with KYC System deployed successfully!"
echo ""
echo "🔗 KYC endpoints are now integrated with the main Payment API Gateway"

echo ""
echo "🔗 KYC API Endpoints (integrated with Payment API):"
echo "POST /kyc/start - Start KYC verification"
echo "POST /kyc/validate/{correlationId} - Validate verification step"
echo "POST /kyc/challenge/{correlationId} - Handle challenge questions"
echo "POST /kyc/complete/{correlationId} - Complete verification"
echo "GET /kyc/status/{verificationId} - Get verification status"
echo "GET /kyc/user/status - Get user's latest verification status"
echo ""
echo "📚 Next steps:"
echo "1. Test the KYC Lambda functions using the provided event files in events/kyc/"
echo "2. Configure Prove API credentials in environment variables"
echo "3. Set up monitoring and alerting for KYC functions"
echo "4. Update your frontend to use the new KYC endpoints"
echo "5. Test the API endpoints using the integrated Payment API"
echo "6. Configure proper authentication using Clerk JWT tokens"
echo "7. Test the verification flow in ${PROVE_ENVIRONMENT} environment"
echo "8. Monitor CloudWatch logs for any issues"
echo "9. Review the KYC documentation in KYC_README.md" 