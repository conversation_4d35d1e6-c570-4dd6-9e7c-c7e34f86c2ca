# CLKK SAAS KYC Verification System

This document describes the migrated KYC (Know Your Customer) verification system for CLKK SAAS, which has been migrated from Firebase Cloud Functions to AWS Lambda with a portable, multi-provider architecture.

## 🏗️ Architecture Overview

The KYC system is built with a modular, provider-agnostic architecture that supports multiple identity verification providers:

### Core Components

1. **KYC Service** (`layers/commons/services/kyc-service.ts`)
   - Central business logic for KYC operations
   - Provider-agnostic interface
   - Handles verification lifecycle management

2. **Provider Implementations** (`layers/commons/services/providers/`)
   - `ProveProvider` - Prove Identity verification integration
   - Extensible for additional providers (Jumio, Onfido, etc.)

3. **Data Layer** (`layers/commons/data/entities/`)
   - `KYCVerification` - DynamoDB entity for verification records
   - Comprehensive metadata tracking
   - Multi-tenant support

4. **Lambda Functions** (`functions/`)
   - `kyc-start` - Initiate verification process
   - `kyc-validate` - Validate verification steps
   - `kyc-challenge` - Handle challenge questions
   - `kyc-complete` - Finalize verification
   - `kyc-status` - Get verification status

## 🚀 Migration from Firebase

### What Was Migrated

The following Firebase Cloud Functions have been migrated to AWS Lambda:

| Firebase Function | AWS Lambda Function | Description |
|------------------|-------------------|-------------|
| `getEchoEndpoint` | Health check integrated into each function | Basic health check |
| `v3StartRequest` | `kyc-start` | Initiate Prove verification |
| `v3ValidateRequest` | `kyc-validate` | Validate correlation ID |
| `v3ChallengeRequest` | `kyc-challenge` | Handle challenge questions |
| `v3CompleteRequest` | `kyc-complete` | Complete verification |

### Key Improvements

1. **Provider Abstraction**: The new system supports multiple KYC providers through a common interface
2. **Better Data Modeling**: Comprehensive verification tracking with DynamoDB
3. **Scalability**: AWS Lambda auto-scaling vs Firebase limitations
4. **Type Safety**: Full TypeScript implementation with strict typing
5. **Infrastructure as Code**: SAM template for reproducible deployments
6. **Enhanced Security**: Proper IAM roles and encryption support

## 📋 API Endpoints

### Authentication
All endpoints require the following headers:
- `X-User-Id` or `x-user-id`: User identifier
- `X-Clerk-User-Id` or `x-clerk-user-id`: Clerk user identifier
- `X-Tenant-Id` or `x-tenant-id`: Tenant identifier (optional, defaults to 'default')

### Endpoints

#### 1. Start Verification
```http
POST /kyc/start
```

**Request Body:**
```json
{
  "provider": "PROVE",
  "verificationLevel": "BASIC",
  "individual": {
    "firstName": "John",
    "lastName": "Doe",
    "dateOfBirth": "1990-01-01",
    "phoneNumber": "+**********",
    "ssn": "1234"
  },
  "metadata": {
    "ipAddress": "***********",
    "userAgent": "Mozilla/5.0..."
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "verificationId": "kyc_123456",
    "correlationId": "prove_abc123",
    "status": "IN_PROGRESS",
    "nextStep": "validate",
    "authToken": "token_xyz",
    "provider": "PROVE"
  }
}
```

#### 2. Validate Step
```http
POST /kyc/validate/{correlationId}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "correlationId": "prove_abc123",
    "isValid": true,
    "nextStep": "challenge",
    "message": "Validation successful",
    "verificationId": "kyc_123456",
    "status": "IN_PROGRESS"
  }
}
```

#### 3. Challenge Step
```http
POST /kyc/challenge/{correlationId}
```

**Request Body:**
```json
{
  "dateOfBirth": "1990-01-01",
  "last4SSN": "1234",
  "challengeAnswers": ["Answer1", "Answer2"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "correlationId": "prove_abc123",
    "success": true,
    "challengeQuestions": [
      {
        "question": "What was your first car?",
        "answers": ["Honda", "Toyota", "Ford", "Other"]
      }
    ],
    "verificationId": "kyc_123456",
    "nextStep": "answer_questions"
  }
}
```

#### 4. Complete Verification
```http
POST /kyc/complete/{correlationId}
```

**Request Body:**
```json
{
  "individual": {
    "firstName": "John",
    "lastName": "Doe",
    "dateOfBirth": "1990-01-01",
    "phoneNumber": "+**********"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "verificationId": "kyc_123456",
    "correlationId": "prove_abc123",
    "status": "VERIFIED",
    "verified": true,
    "individual": {
      "firstName": "John",
      "lastName": "Doe"
    },
    "verificationResult": {
      "riskScore": 15,
      "confidenceScore": 95,
      "flags": []
    },
    "completedAt": "2024-01-01T12:00:00Z"
  }
}
```

#### 5. Get Status
```http
GET /kyc/status/{verificationId}
GET /kyc/user/status
```

**Response:**
```json
{
  "success": true,
  "data": {
    "verificationId": "kyc_123456",
    "status": "VERIFIED",
    "provider": "PROVE",
    "verificationLevel": "BASIC",
    "createdAt": "2024-01-01T10:00:00Z",
    "verifiedAt": "2024-01-01T12:00:00Z",
    "canRetry": false,
    "isExpired": false,
    "metadata": {
      "riskScore": 15,
      "confidenceScore": 95,
      "flags": []
    }
  }
}
```

## 🛠️ Deployment

### Prerequisites

1. **AWS CLI** configured with appropriate permissions
2. **AWS SAM CLI** installed
3. **Node.js 18+** installed
4. **Prove API credentials** (Client ID and Secret)

### Environment Variables

Set the following environment variables before deployment:

```bash
export PROVE_CLIENT_ID="your_prove_client_id"
export PROVE_CLIENT_SECRET="your_prove_client_secret"
export PROVE_ENVIRONMENT="SANDBOX"  # or "PRODUCTION"
export TABLE_NAME="clkk-saas-dev"   # Your DynamoDB table name
export AWS_REGION="us-east-1"       # Your preferred AWS region
```

### Deploy

1. **Make the deployment script executable:**
   ```bash
   chmod +x deploy-kyc.sh
   ```

2. **Deploy to development environment:**
   ```bash
   ./deploy-kyc.sh dev
   ```

3. **Deploy to production environment:**
   ```bash
   ./deploy-kyc.sh prod
   ```

### Manual Deployment

If you prefer manual deployment:

```bash
# Build commons layer
cd layers/commons && npm install && npm run build && cd ../..

# Build functions
for func in kyc-start kyc-validate kyc-complete kyc-status kyc-challenge; do
  cd functions/$func && npm install && npm run build && cd ../..
done

# Deploy with SAM
sam build --template-file kyc-template.yaml
sam deploy --guided
```

## 🔧 Configuration

### Prove Provider Configuration

The Prove provider requires the following configuration:

```typescript
const proveProvider = new ProveProvider({
  clientId: process.env.PROVE_CLIENT_ID!,
  clientSecret: process.env.PROVE_CLIENT_SECRET!,
  environment: "SANDBOX" | "PRODUCTION",
  isEnabled: true
});
```

### DynamoDB Schema

The KYC verification records are stored in DynamoDB with the following key structure:

- **PK**: `TENANT#{tenantId}#USER#{userId}#KYC#{verificationId}`
- **SK**: `METADATA`
- **GSI1**: Query by user across all verifications
- **GSI2**: Query by provider and status
- **GSI3**: Query by Clerk user ID

## 🔒 Security Considerations

1. **Data Encryption**: Sensitive data (SSN, etc.) should be encrypted at rest
2. **API Authentication**: Implement proper authentication (IAM, API keys, JWT)
3. **Rate Limiting**: Consider implementing rate limiting for API endpoints
4. **Audit Logging**: All verification attempts are logged for compliance
5. **Data Retention**: Configure appropriate data retention policies

## 🧪 Testing

### Unit Tests

```bash
# Test commons layer
cd layers/commons && npm test

# Test individual functions
cd functions/kyc-start && npm test
```

### Integration Testing

Use the provided API endpoints to test the complete verification flow:

1. Start verification with test data
2. Validate the correlation ID
3. Complete challenge steps (if required)
4. Finalize verification
5. Check status

### Test Data

For Prove sandbox environment, use these test values:

```json
{
  "phoneNumber": "+15551234567",
  "ssn": "1234",
  "dateOfBirth": "1990-01-01"
}
```

## 📊 Monitoring

### CloudWatch Logs

Each Lambda function has its own log group:
- `/aws/lambda/clkk-kyc-start-{environment}`
- `/aws/lambda/clkk-kyc-validate-{environment}`
- `/aws/lambda/clkk-kyc-complete-{environment}`
- `/aws/lambda/clkk-kyc-status-{environment}`
- `/aws/lambda/clkk-kyc-challenge-{environment}`

### Metrics to Monitor

1. **Success Rate**: Percentage of successful verifications
2. **Response Time**: API response times
3. **Error Rate**: Failed verification attempts
4. **Provider Performance**: Prove API response times and success rates

## 🔄 Adding New Providers

To add a new KYC provider (e.g., Jumio):

1. **Create Provider Implementation:**
   ```typescript
   // layers/commons/services/providers/jumio-provider.ts
   export class JumioProvider implements IKYCProvider {
     // Implement interface methods
   }
   ```

2. **Update Types:**
   ```typescript
   // layers/commons/types/kyc-types.ts
   export type KYCProvider = "PROVE" | "JUMIO" | "ONFIDO" | "MANUAL";
   ```

3. **Update Lambda Functions:**
   ```typescript
   // Add Jumio provider initialization in Lambda functions
   if (requestData.provider === "JUMIO") {
     const jumioProvider = new JumioProvider(config);
     // Handle Jumio-specific logic
   }
   ```

## 🐛 Troubleshooting

### Common Issues

1. **"Verification not found"**
   - Check user authentication headers
   - Verify correlation ID matches

2. **"Provider disabled"**
   - Check `PROVE_ENABLED` environment variable
   - Verify provider credentials

3. **"Invalid correlation ID"**
   - Ensure correlation ID from start step is used
   - Check for typos in correlation ID

### Debug Mode

Enable debug logging by setting:
```bash
export DEBUG=true
```

## 📚 Additional Resources

- [Prove API Documentation](https://docs.prove.com/)
- [AWS Lambda Best Practices](https://docs.aws.amazon.com/lambda/latest/dg/best-practices.html)
- [DynamoDB Best Practices](https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/best-practices.html)
- [AWS SAM Documentation](https://docs.aws.amazon.com/serverless-application-model/)

## 🤝 Contributing

1. Follow TypeScript best practices
2. Add comprehensive error handling
3. Include unit tests for new features
4. Update documentation for API changes
5. Test with multiple providers when adding new functionality

## 📄 License

This KYC system is part of the CLKK SAAS platform and follows the same licensing terms. 